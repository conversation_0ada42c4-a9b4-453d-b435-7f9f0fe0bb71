/*:
 * @target MZ
 * @plugindesc 使所有精灵可拖动
 * <AUTHOR> Name
 */

(() => {
  // 扩展Sprite_Clickable的初始化方法
  const _Sprite_Clickable_initialize = Sprite_Clickable.prototype.initialize;
  Sprite_Clickable.prototype.initialize = function () {
    _Sprite_Clickable_initialize.call(this);
    this._isDragging = false;
    this._dragOffsetX = 0;
    this._dragOffsetY = 0;
  };

  // 扩展processTouch方法
  const _Sprite_Clickable_processTouch = Sprite_Clickable.prototype.processTouch;
  Sprite_Clickable.prototype.processTouch = function () {
    _Sprite_Clickable_processTouch.call(this);

    if (this.isClickEnabled()) {
      if (this.isBeingTouched()) {
        if (!this._isDragging && TouchInput.isTriggered()) {
          // 开始拖动
          this._isDragging = true;
          this._dragOffsetX = TouchInput.x - this.x;
          this._dragOffsetY = TouchInput.y - this.y;
        }
      }

      // 处理拖动中的状态
      if (this._isDragging) {
        if (TouchInput.isPressed()) {
          this.x = TouchInput.x - this._dragOffsetX;
          this.y = TouchInput.y - this._dragOffsetY;
        } else {
          // 结束拖动
          this._isDragging = false;
        }
      }
    }
  };

  // 扩展Sprite_Battler的isClickEnabled方法
  const _Sprite_Battler_isClickEnabled = Sprite_Battler.prototype.isClickEnabled;
  Sprite_Battler.prototype.isClickEnabled = function () {
    return _Sprite_Battler_isClickEnabled.call(this) && this._battler && this._battler.isSelected();
  };
})();