/*:
 * @target MZ
 * @plugindesc 自定义动画库 - 为RPG Maker MZ提供轻量级动画功能
 * <AUTHOR> Editor
 * @help
 * ============================================================================
 * 自定义动画库插件 v2.0.0
 * ============================================================================
 *
 * 这个插件提供了一个轻量级的动画系统，不依赖任何外部库，
 * 使用RPG Maker MZ内置的update机制进行更新。
 *
 * 特性:
 * - 支持对象自身动画和对象数组动画
 * - 提供多种缓动函数
 * - 支持动画栈，可以为对象添加多个连续动画
 * - 支持多种动画类型（单次、循环、来回等）
 * - 支持动画的暂停、恢复、重启和停止
 * - 轻量级设计，性能优良
 *
 * 使用方法:
 * 1. 获取对象: const obj = findObjectByScenePath(SceneManager._scene, ["Scene_Title", "2"]);
 * 2. 创建动画栈: const stack = AnimationManager.createStack(obj);
 * 3. 添加动画: stack.add({ x: 100, alpha: 0.5 }, 1000, "easeOutElastic");
 * 4. 设置播放模式: stack.setPlayMode("loop", 3); // 循环播放3次
 *
 * 高级用法:
 * - 动画序列: stack.add({ x: 100 }).add({ y: 200 }).add({ alpha: 0 }, 500);
 * - 动画回调: stack.add({ x: 100 }, 1000, "linear", {
 *     onStart: () => console.log("动画开始"),
 *     onComplete: () => console.log("动画完成")
 *   });
 * - 直接赋值动画栈: obj.animationStack = AnimationManager.createStack(obj);
 *
 * ============================================================================
 */

(function () {
    'use strict';

    //=============================================================================
    // 模块1: 缓动函数库 (Easing)
    //=============================================================================

    const Easing = {
        // 线性
        linear: function (t) {
            return t;
        },

        // 二次方
        easeInQuad: function (t) {
            return t * t;
        },
        easeOutQuad: function (t) {
            return t * (2 - t);
        },
        easeInOutQuad: function (t) {
            return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
        },

        // 三次方
        easeInCubic: function (t) {
            return t * t * t;
        },
        easeOutCubic: function (t) {
            return (--t) * t * t + 1;
        },
        easeInOutCubic: function (t) {
            return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
        },

        // 四次方
        easeInQuart: function (t) {
            return t * t * t * t;
        },
        easeOutQuart: function (t) {
            return 1 - (--t) * t * t * t;
        },
        easeInOutQuart: function (t) {
            return t < 0.5 ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t;
        },

        // 弹性
        easeInElastic: function (t) {
            return (.04 - .04 / t) * Math.sin(25 * t) + 1;
        },
        easeOutElastic: function (t) {
            return .04 * t / (--t) * Math.sin(25 * t);
        },
        easeInOutElastic: function (t) {
            return (t -= .5) < 0 ? (.02 + .01 / t) * Math.sin(50 * t) : (.02 - .01 / t) * Math.sin(50 * t) + 1;
        },

        // 反弹
        easeInBounce: function (t) {
            return 1 - Easing.easeOutBounce(1 - t);
        },
        easeOutBounce: function (t) {
            if (t < (1 / 2.75)) {
                return 7.5625 * t * t;
            } else if (t < (2 / 2.75)) {
                return 7.5625 * (t -= (1.5 / 2.75)) * t + 0.75;
            } else if (t < (2.5 / 2.75)) {
                return 7.5625 * (t -= (2.25 / 2.75)) * t + 0.9375;
            } else {
                return 7.5625 * (t -= (2.625 / 2.75)) * t + 0.984375;
            }
        },
        easeInOutBounce: function (t) {
            return t < 0.5 ? Easing.easeInBounce(t * 2) * 0.5 : Easing.easeOutBounce(t * 2 - 1) * 0.5 + 0.5;
        },

        // 正弦
        easeInSine: function (t) {
            return 1 - Math.cos(t * Math.PI / 2);
        },
        easeOutSine: function (t) {
            return Math.sin(t * Math.PI / 2);
        },
        easeInOutSine: function (t) {
            return -(Math.cos(Math.PI * t) - 1) / 2;
        }
    };

    //=============================================================================
    // 模块2: 动画判断工具 (AnimationUtils)
    //=============================================================================

    const AnimationUtils = {
        /**
         * 为对象添加唯一ID
         * @param {Object} obj - 要添加ID的对象
         */
        addUniqueId: function (obj) {
            if (!obj.id) {
                obj.id = 'obj_' + Date.now().toString() + Math.floor(Math.random() * 1000);
            }
            return obj;
        },

        /**
         * 解析属性值，处理相对值
         * @param {Object} target - 目标对象
         * @param {Object} properties - 属性对象
         * @returns {Object} 解析后的属性值对象
         */
        parseProperties: function (target, properties) {
            const result = {
                startValues: {},
                endValues: {},
                deltaValues: {}
            };

            for (const prop in properties) {
                let startValue = target[prop];
                let endValue = properties[prop];

                // 处理相对值 ("+10", "-20")
                if (typeof endValue === 'string' && (endValue.startsWith('+') || endValue.startsWith('-'))) {
                    endValue = startValue + parseFloat(endValue);
                }

                result.startValues[prop] = startValue;
                result.endValues[prop] = endValue;
                result.deltaValues[prop] = endValue - startValue;
            }

            return result;
        },

        /**
         * 应用属性值到目标对象
         * @param {Object} target - 目标对象
         * @param {Object} properties - 属性对象
         */
        applyProperties: function (target, properties) {
            for (const prop in properties) {
                target[prop] = properties[prop];
            }
        }
    };

    //=============================================================================
    // 模块3: 动画类型 (AnimationTypes)
    //=============================================================================

    const AnimationTypes = {
        /**
         * 单次播放
         */
        ONCE: 'once',

        /**
         * 循环播放
         */
        LOOP: 'loop',

        /**
         * 来回播放
         */
        YOYO: 'yoyo',

        /**
         * 播放后删除
         */
        REMOVE: 'remove',

        /**
         * 获取所有可用的动画类型
         * @returns {Array} 动画类型数组
         */
        getAll: function () {
            return [this.ONCE, this.LOOP, this.YOYO, this.REMOVE];
        },

        /**
         * 检查动画类型是否有效
         * @param {string} type - 动画类型
         * @returns {boolean} 是否有效
         */
        isValid: function (type) {
            return this.getAll().includes(type);
        }
    };

    //=============================================================================
    // 模块4: 动画类 (Animation) - 处理单个动画
    //=============================================================================

    class Animation {
        constructor(target, properties, duration, easing, options = {}) {
            this.target = target;
            this.properties = properties;
            this.duration = duration || 1000;
            this.easing = typeof easing === 'function' ? easing : (Easing[easing] || Easing.linear);
            this.options = options;

            this.startTime = null;
            this.elapsedTime = 0;
            this.isPlaying = false;
            this.isPaused = false;
            this.isCompleted = false;
            this.isReversed = false;

            this.startValues = {};
            this.endValues = {};
            this.deltaValues = {};

            this.id = Date.now().toString() + Math.floor(Math.random() * 1000);

            // 确保目标对象有唯一ID
            AnimationUtils.addUniqueId(this.target);

            // 初始化属性值
            this._initProperties();
        }

        _initProperties() {
            if (!this.target) return;

            // 解析属性值
            const parsedProps = AnimationUtils.parseProperties(this.target, this.properties);
            this.startValues = parsedProps.startValues;
            this.endValues = parsedProps.endValues;
            this.deltaValues = parsedProps.deltaValues;
        }

        start() {
            this.startTime = Date.now();
            this.isPlaying = true;
            this.isPaused = false;
            this.isCompleted = false;

            if (this.options.onStart) {
                this.options.onStart(this);
            }

            return this;
        }

        update() {
            if (!this.isPlaying || this.isPaused || this.isCompleted || !this.target) return false;

            const currentTime = Date.now();
            this.elapsedTime = currentTime - this.startTime;

            // 检查动画是否完成
            if (this.elapsedTime >= this.duration) {
                this._complete();
                return true; // 动画已完成
            }

            // 计算当前进度
            let progress = this.elapsedTime / this.duration;

            // 如果是反向播放，反转进度
            if (this.isReversed) {
                progress = 1 - progress;
            }

            const easedProgress = this.easing(progress);

            // 更新目标属性
            for (const prop in this.properties) {
                const start = this.startValues[prop];
                const delta = this.deltaValues[prop];
                this.target[prop] = start + delta * easedProgress;
            }

            if (this.options.onUpdate) {
                this.options.onUpdate(progress, this);
            }

            return false; // 动画未完成
        }

        _complete() {
            if (!this.target) return;

            // 设置最终值
            const finalValues = this.isReversed ? this.startValues : this.endValues;
            AnimationUtils.applyProperties(this.target, finalValues);

            this.isPlaying = false;
            this.isCompleted = true;

            if (this.options.onComplete) {
                this.options.onComplete(this);
            }
        }

        pause() {
            if (this.isPlaying && !this.isPaused) {
                this.isPaused = true;

                if (this.options.onPause) {
                    this.options.onPause(this);
                }
            }
            return this;
        }

        resume() {
            if (this.isPlaying && this.isPaused) {
                this.isPaused = false;
                this.startTime = Date.now() - this.elapsedTime;

                if (this.options.onResume) {
                    this.options.onResume(this);
                }
            }
            return this;
        }

        stop() {
            this.isPlaying = false;
            this.isPaused = false;

            if (this.options.onStop) {
                this.options.onStop(this);
            }
            return this;
        }

        restart() {
            this.elapsedTime = 0;
            this.isCompleted = false;
            this.isPaused = false;
            return this.start();
        }

        reverse() {
            // 交换起始值和结束值
            this.isReversed = !this.isReversed;

            // 重置动画状态
            this.elapsedTime = 0;
            this.isCompleted = false;
            this.isPaused = false;

            return this.start();
        }
    }

    //=============================================================================
    // 模块5: 动画栈 (AnimationStack) - 管理对象的动画队列
    //=============================================================================

    class AnimationStack {
        constructor(target) {
            this.target = target;
            this.animations = [];
            this.currentIndex = 0;
            this.isPlaying = false;
            this.isPaused = false;

            // 播放模式设置
            this.playMode = AnimationTypes.ONCE; // 默认单次播放
            this.playCount = 1; // 播放次数，-1表示无限循环
            this.currentPlayCount = 0;

            // 确保目标对象有唯一ID
            AnimationUtils.addUniqueId(this.target);

            // 为目标对象添加update方法
            this._setupTargetUpdate();
        }

        /**
         * 设置目标对象的update方法
         * @private
         */
        _setupTargetUpdate() {
            if (!this.target) return;

            // 保存原始update方法（如果存在）
            if (this.target.update && typeof this.target.update === 'function') {
                this.target._originalUpdate = this.target.update;
            }

            // 添加新的update方法
            const self = this;
            this.target.update = function () {
                // 调用原始update方法（如果存在）
                if (this._originalUpdate) {
                    this._originalUpdate.apply(this, arguments);
                }

                // 更新动画栈
                self.update();
            };
        }

        /**
         * 添加动画到栈中
         * @param {Object} properties - 要动画的属性
         * @param {number} duration - 动画持续时间(毫秒)
         * @param {string|Function} easing - 缓动函数名称或函数
         * @param {Object} options - 其他选项(回调等)
         * @returns {AnimationStack} 当前动画栈实例（用于链式调用）
         */
        add(properties, duration = 1000, easing = 'linear', options = {}) {
            const animation = new Animation(this.target, properties, duration, easing, options);
            this.animations.push(animation);
            return this;
        }

        /**
         * 设置播放模式
         * @param {string} mode - 播放模式（once, loop, yoyo, remove）
         * @param {number} count - 播放次数，-1表示无限循环
         * @returns {AnimationStack} 当前动画栈实例
         */
        setPlayMode(mode, count = 1) {
            if (AnimationTypes.isValid(mode)) {
                this.playMode = mode;
                this.playCount = count;
            } else {
                console.warn(`无效的播放模式: ${mode}，使用默认模式: ${AnimationTypes.ONCE}`);
                this.playMode = AnimationTypes.ONCE;
            }
            return this;
        }

        /**
         * 开始播放动画栈
         * @returns {AnimationStack} 当前动画栈实例
         */
        play() {
            if (this.animations.length === 0) return this;

            this.isPlaying = true;
            this.isPaused = false;
            this.currentIndex = 0;

            // 重置计数器，但在yoyo模式下，如果是从中间恢复，则不重置
            // 这样可以确保奇数次数的来回播放能够正确完成
            if (this.playMode !== AnimationTypes.YOYO ||
                !this.animations[0] ||
                !this.animations[0].isReversed) {
                this.currentPlayCount = 0;
            }

            // 开始第一个动画
            if (this.animations[0]) {
                this.animations[0].start();
            }

            return this;
        }

        /**
         * 更新动画栈
         * @returns {boolean} 是否有动画正在播放
         */
        update() {
            if (!this.isPlaying || this.isPaused || this.animations.length === 0) return false;

            const currentAnimation = this.animations[this.currentIndex];
            if (!currentAnimation) return false;

            // 更新当前动画
            const isCompleted = currentAnimation.update();

            // 如果当前动画完成，移动到下一个动画
            if (isCompleted) {
                this._moveToNextAnimation();
            }

            return true;
        }

        /**
         * 移动到下一个动画
         * @private
         */
        _moveToNextAnimation() {
            this.currentIndex++;

            // 检查是否到达动画栈末尾
            if (this.currentIndex >= this.animations.length) {
                this._handleStackCompletion();
            } else {
                // 开始下一个动画
                this.animations[this.currentIndex].start();
            }
        }

        /**
         * 处理动画栈播放完成
         * @private
         */
        _handleStackCompletion() {
            // 只有在非yoyo模式下才在这里增加计数
            // yoyo模式的计数在其特定的case中处理
            if (this.playMode !== AnimationTypes.YOYO) {
                this.currentPlayCount++;
            }

            // 根据播放模式处理
            switch (this.playMode) {
                case AnimationTypes.LOOP:
                    // 循环播放
                    if (this.playCount === -1 || this.currentPlayCount < this.playCount) {
                        this.currentIndex = 0;
                        this.animations[0].start();
                    } else {
                        this.isPlaying = false;
                    }
                    break;

                case AnimationTypes.YOYO:
                    // 来回播放
                    // 检查是否是反向阶段结束
                    const isReversePhaseEnd = this.animations[0].isReversed;

                    // 只有在反向阶段结束时才增加计数（完成一个完整的来回）
                    if (isReversePhaseEnd) {
                        this.currentPlayCount++;
                    }

                    // 检查是否需要继续播放
                    if (this.playCount === -1 || this.currentPlayCount < this.playCount) {
                        // 如果是奇数次数且已经完成了最后一个来回的正向部分，则不再播放反向部分
                        if (this.playCount % 2 === 1 &&
                            this.currentPlayCount === this.playCount - 1 &&
                            !isReversePhaseEnd) {
                            this.isPlaying = false;
                            return;
                        }

                        // 反转动画顺序
                        this.animations.reverse();
                        // 反转每个动画
                        this.animations.forEach(anim => anim.reverse());
                        this.currentIndex = 0;
                        this.animations[0].start();
                    } else {
                        this.isPlaying = false;
                    }
                    break;

                case AnimationTypes.REMOVE:
                    // 播放后删除
                    this.isPlaying = false;
                    // 从目标对象中移除动画栈
                    if (this.target) {
                        this.target.animationStack = null;
                        // 恢复原始update方法
                        if (this.target._originalUpdate) {
                            this.target.update = this.target._originalUpdate;
                        }
                    }
                    break;

                case AnimationTypes.ONCE:
                default:
                    // 单次播放
                    this.isPlaying = false;
                    break;
            }
        }

        /**
         * 暂停动画栈
         * @returns {AnimationStack} 当前动画栈实例
         */
        pause() {
            if (this.isPlaying && !this.isPaused) {
                this.isPaused = true;

                // 暂停当前动画
                const currentAnimation = this.animations[this.currentIndex];
                if (currentAnimation) {
                    currentAnimation.pause();
                }
            }
            return this;
        }

        /**
         * 恢复动画栈
         * @returns {AnimationStack} 当前动画栈实例
         */
        resume() {
            if (this.isPlaying && this.isPaused) {
                this.isPaused = false;

                // 恢复当前动画
                const currentAnimation = this.animations[this.currentIndex];
                if (currentAnimation) {
                    currentAnimation.resume();
                }
            }
            return this;
        }

        /**
         * 停止动画栈
         * @returns {AnimationStack} 当前动画栈实例
         */
        stop() {
            this.isPlaying = false;
            this.isPaused = false;

            // 停止所有动画
            this.animations.forEach(animation => {
                animation.stop();
            });

            return this;
        }

        /**
         * 清空动画栈
         * @returns {AnimationStack} 当前动画栈实例
         */
        clear() {
            this.stop();
            this.animations = [];
            this.currentIndex = 0;
            return this;
        }
    }

    //=============================================================================
    // 模块6: 动画管理器 (AnimationManager) - 管理所有动画栈
    //=============================================================================

    const AnimationManager = {
        stacks: [],

        /**
         * 创建一个新的动画栈
         * @param {Object} target - 动画目标对象
         * @returns {AnimationStack} 动画栈实例
         */
        createStack: function (target) {
            if (!target) {
                console.error('无法创建动画栈：目标对象为空');
                return null;
            }

            // 确保目标对象有唯一ID
            AnimationUtils.addUniqueId(target);

            // 检查对象是否已有动画栈
            if (target.animationStack) {
                console.warn('对象已有动画栈，返回现有栈');
                return target.animationStack;
            }

            // 创建新的动画栈
            const stack = new AnimationStack(target);

            // 将栈添加到管理器
            this.stacks.push(stack);

            // 将栈赋值给目标对象
            target.animationStack = stack;

            return stack;
        },

        /**
         * 获取对象的动画栈
         * @param {Object} target - 目标对象
         * @returns {AnimationStack|null} 动画栈实例或null
         */
        getStack: function (target) {
            if (!target) return null;

            // 如果对象已有动画栈，直接返回
            if (target.animationStack) {
                return target.animationStack;
            }

            // 否则创建新的动画栈
            return this.createStack(target);
        },

        /**
         * 移除对象的动画栈
         * @param {Object} target - 目标对象
         */
        removeStack: function (target) {
            if (!target || !target.animationStack) return;

            // 停止动画栈
            target.animationStack.stop();

            // 从管理器中移除
            const index = this.stacks.indexOf(target.animationStack);
            if (index !== -1) {
                this.stacks.splice(index, 1);
            }

            // 从对象中移除
            target.animationStack = null;
        },

        /**
         * 更新所有动画栈
         */
        update: function () {
            for (let i = this.stacks.length - 1; i >= 0; i--) {
                const stack = this.stacks[i];

                // 如果栈不再有效（例如目标对象已被销毁），则移除
                if (!stack.target) {
                    this.stacks.splice(i, 1);
                    continue;
                }

                // 更新栈
                stack.update();
            }
        },

        /**
         * 停止所有动画栈
         */
        stopAll: function () {
            this.stacks.forEach(stack => stack.stop());
        },

        /**
         * 为了向后兼容，保留原来的animate方法
         * @param {Object|Array} targets - 动画目标对象或对象数组
         * @param {Object} properties - 要动画的属性
         * @param {number} duration - 动画持续时间(毫秒)
         * @param {string|Function} easing - 缓动函数名称或函数
         * @param {Object} options - 其他选项(回调等)
         * @returns {Animation} 动画实例
         */
        animate: function (targets, properties, duration = 1000, easing = 'linear', options = {}) {
            // 处理数组目标
            if (Array.isArray(targets)) {
                const animations = [];
                targets.forEach(target => {
                    if (!target) return;

                    // 为每个目标创建动画
                    const animation = new Animation(target, properties, duration, easing, options);
                    animation.start();
                    animations.push(animation);
                });

                // 返回第一个动画（为了兼容性）
                return animations.length > 0 ? animations[0] : null;
            } else {
                // 单个目标
                const target = targets;
                if (!target) return null;

                const animation = new Animation(target, properties, duration, easing, options);
                animation.start();
                return animation;
            }
        },

        /**
         * 为了向后兼容，保留原来的sequence方法
         * @param {Object|Array} targets - 动画目标对象或对象数组
         * @param {Array} propertiesArray - 属性数组
         * @param {number} duration - 每个动画的持续时间
         * @param {string|Function} easing - 缓动函数名称或函数
         * @param {Object} options - 其他选项(回调等)
         * @returns {Array} 动画实例数组
         */
        sequence: function (targets, propertiesArray, duration = 1000, easing = 'linear', options = {}) {
            // 创建动画栈
            const stack = Array.isArray(targets) ?
                this.createStack(targets[0]) :
                this.createStack(targets);

            if (!stack) return [];

            // 添加所有动画到栈中
            propertiesArray.forEach(props => {
                stack.add(props, duration, easing, options);
            });

            // 开始播放
            stack.play();

            // 返回动画栈中的动画（为了兼容性）
            return stack.animations;
        }
    };

    //=============================================================================
    // 扩展对象原型，添加动画方法
    //=============================================================================

    /**
     * 扩展对象，添加动画方法
     * @param {Object} obj - 要扩展的对象
     */
    const extendWithAnimationMethods = function (obj) {
        if (!obj) return;

        // 确保对象有唯一ID
        AnimationUtils.addUniqueId(obj);

        // 添加动画方法
        if (!obj.animate) {
            obj.animate = function (properties, duration, easing, options) {
                return AnimationManager.animate(this, properties, duration, easing, options);
            };
        }

        // 添加获取动画栈方法
        if (!obj.getAnimationStack) {
            obj.getAnimationStack = function () {
                return AnimationManager.getStack(this);
            };
        }

        // 添加停止动画方法
        if (!obj.stopAnimations) {
            obj.stopAnimations = function () {
                if (this.animationStack) {
                    this.animationStack.stop();
                }
            };
        }
    };

    /**
     * 扩展容器，添加子元素动画方法
     * @param {Object} container - 要扩展的容器
     */
    const extendContainerWithAnimationMethods = function (container) {
        if (!container || !container.children) return;

        // 添加子元素动画方法
        if (!container.animateChildren) {
            container.animateChildren = function (properties, duration, easing, options) {
                const visibleChildren = this.children.filter(child => {
                    return child && !child._hidden && child.visible !== false;
                });

                if (visibleChildren.length === 0) return null;

                return AnimationManager.animate(visibleChildren, properties, duration, easing, options);
            };
        }

        // 添加停止子元素动画方法
        if (!container.stopChildrenAnimations) {
            container.stopChildrenAnimations = function () {
                this.children.forEach(child => {
                    if (child && child.animationStack) {
                        child.animationStack.stop();
                    }
                });
            };
        }
    };

    //=============================================================================
    // 将AnimationManager挂载到SceneManager的update循环中
    //=============================================================================

    // 保存原始的SceneManager.updateScene方法
    const _SceneManager_updateScene = SceneManager.updateScene;

    // 重写SceneManager.updateScene方法，添加动画更新
    SceneManager.updateScene = function () {
        // 调用原始方法
        _SceneManager_updateScene.call(this);

        // 更新动画
        AnimationManager.update();
    };

    //=============================================================================
    // 导出到全局作用域
    //=============================================================================

    // 将AnimationManager导出到全局作用域
    window.AnimationManager = AnimationManager;

    // 导出辅助方法
    window.extendWithAnimationMethods = extendWithAnimationMethods;
    window.extendContainerWithAnimationMethods = extendContainerWithAnimationMethods;
    window.AnimationTypes = AnimationTypes;

    //=============================================================================
    // 初始化
    //=============================================================================

    // 在游戏加载完成后初始化
    const _Scene_Boot_start = Scene_Boot.prototype.start;
    Scene_Boot.prototype.start = function () {
        _Scene_Boot_start.call(this);

        // 输出初始化信息
        console.log('CustomAnimation插件 v2.0.0 初始化完成');
    };
})();