/**
 * 属性修改代码生成器
 * 负责生成各种属性修改的JavaScript代码
 */

import { BaseGenerator } from '../core/BaseGenerator';
import {
  OperationInfo,
  PropertyModificationInfo,
  CodeSnippet,
  OperationType,
  PropertyGeneratorConfig,
  PropertyNotSupportedError
} from '../core/types';
import {
  isPropertyModification,
  getRealObjectType,
  getPropertyCategory,
  validateOperationInfo
} from '../core/utils';

/**
 * 属性生成器主类
 */
export class PropertyGenerator extends BaseGenerator {
  private propertyGenerators: Map<string, PropertyGeneratorHandler>;

  constructor(config: Partial<PropertyGeneratorConfig> = {}) {
    const propertyConfig: PropertyGeneratorConfig = {
      handleSpecialProperties: true,
      validateValues: true,
      debug: false,
      minify: false,
      addComments: true,
      variablePrefix: 'rpgEditor_',
      indentSize: 2,
      ...config
    };

    super(propertyConfig);

    this.propertyGenerators = new Map();
    this.initializePropertyGenerators();
  }

  /**
   * 检查是否可以处理该操作
   */
  canHandle(operation: OperationInfo): boolean {
    return isPropertyModification(operation);
  }

  /**
   * 生成属性修改代码
   */
  generate(operation: OperationInfo): CodeSnippet {
    if (!this.canHandle(operation)) {
      throw new Error('PropertyGenerator 只能处理属性修改操作');
    }

    const propertyOp = operation as PropertyModificationInfo;
    this.validateOperation(propertyOp);

    const objectType = getRealObjectType(propertyOp.targetObject);
    const propertyCategory = getPropertyCategory(propertyOp.propertyName, objectType);

    // 获取对应的属性生成器
    const generator = this.getPropertyGenerator(propertyCategory, objectType);

    if (!generator) {
      throw new PropertyNotSupportedError(propertyOp.propertyName, objectType);
    }

    return generator.generate(propertyOp, this.context);
  }

  /**
   * 初始化属性生成器
   */
  private initializePropertyGenerators(): void {
    // 基础属性生成器
    this.propertyGenerators.set('basic', new BasicPropertyGenerator());

    // Sprite属性生成器
    this.propertyGenerators.set('sprite', new SpritePropertyGenerator());

    // Container属性生成器
    this.propertyGenerators.set('container', new ContainerPropertyGenerator());

    // 自定义属性生成器
    this.propertyGenerators.set('custom', new CustomPropertyGenerator());
  }

  /**
   * 获取属性生成器
   */
  private getPropertyGenerator(category: string, objectType: string): PropertyGeneratorHandler | null {
    // 优先使用特定类型的生成器
    const specificGenerator = this.propertyGenerators.get(`${objectType.toLowerCase()}_${category}`);
    if (specificGenerator) {
      return specificGenerator;
    }

    // 使用通用类别生成器
    return this.propertyGenerators.get(category) || null;
  }

  /**
   * 注册自定义属性生成器
   */
  public registerPropertyGenerator(category: string, generator: PropertyGeneratorHandler): void {
    this.propertyGenerators.set(category, generator);
  }

  /**
   * 验证属性修改操作
   */
  protected validateOperation(operation: PropertyModificationInfo): void {
    super.validateOperation(operation);

    const errors = validateOperationInfo(operation);
    if (errors.length > 0) {
      throw new Error(`属性修改操作验证失败: ${errors.join(', ')}`);
    }

    if (!operation.propertyName) {
      throw new Error('属性名不能为空');
    }

    if (this.config.validateValues && operation.newValue === undefined) {
      throw new Error('属性值不能为undefined');
    }
  }
}

// ==================== 属性生成器接口 ====================

/**
 * 属性生成器处理接口
 */
export interface PropertyGeneratorHandler {
  generate(operation: PropertyModificationInfo, context: any): CodeSnippet;
  canHandle?(propertyName: string, objectType: string): boolean;
}

// ==================== 基础属性生成器 ====================

/**
 * 基础属性生成器
 * 处理 x, y, width, height, visible, alpha 等基础属性
 */
export class BasicPropertyGenerator implements PropertyGeneratorHandler {
  generate(operation: PropertyModificationInfo, context: any): CodeSnippet {
    const { propertyName, newValue, objectPath, className } = operation;

    // 生成单个属性的设置代码（不包含对象查找）
    const propertyCode = `target.${propertyName} = ${this.serializeValue(newValue)};`;
    const debugCode = `if (DEBUG) console.log('设置属性:', '${propertyName}', ${this.serializeValue(newValue)}, target);`;

    return {
      code: [propertyCode, debugCode].join('\n'),
      description: `设置 ${className} 的 ${propertyName} 属性为 ${newValue}`,
      dependencies: ['findObjectByScenePath'],
      // 添加元数据用于后续分组
      metadata: {
        objectPath: objectPath.join('/'),
        className,
        variableName: this.generateVariableName(objectPath, className)
      }
    };
  }

  private generateVariableName(objectPath: string[], className: string): string {
    const pathStr = objectPath.join('_');
    return `target_${className.toLowerCase()}_${pathStr.replace(/[^a-zA-Z0-9]/g, '_')}`;
  }

  private generateObjectLookup(objectPath: string[], variableName: string): string {
    const pathArray = objectPath.map(p => `"${p}"`).join(', ');
    return [
      `const ${variableName}Path = [${pathArray}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`
    ].join('\n');
  }

  private generatePropertyAssignment(variableName: string, propertyName: string, value: any): string {
    const serializedValue = this.serializeValue(value);
    return [
      `if (${variableName}) {`,
      `    ${variableName}.${propertyName} = ${serializedValue};`,
      '}'
    ].join('\n');
  }

  private generateDebugLog(variableName: string, propertyName: string, value: any): string {
    return `if (DEBUG) console.log('设置属性:', '${propertyName}', ${this.serializeValue(value)}, ${variableName});`;
  }

  private serializeValue(value: any): string {
    if (typeof value === 'string') return `"${value}"`;
    if (typeof value === 'number' || typeof value === 'boolean') return String(value);
    if (value === null) return 'null';
    if (value === undefined) return 'undefined';
    return JSON.stringify(value);
  }
}

// ==================== Sprite属性生成器 ====================

/**
 * Sprite属性生成器
 * 处理 texture, tint, hue, colorTone 等Sprite特有属性
 */
export class SpritePropertyGenerator implements PropertyGeneratorHandler {
  generate(operation: PropertyModificationInfo, context: any): CodeSnippet {
    const { propertyName, newValue, objectPath, className } = operation;

    // 根据不同的Sprite属性生成不同的代码
    switch (propertyName) {
      case 'texture':
        return this.generateTextureCode(operation);
      case 'hue':
        return this.generateHueCode(operation);
      case 'colorTone':
        return this.generateColorToneCode(operation);
      case 'blendColor':
        return this.generateBlendColorCode(operation);
      default:
        // 使用基础属性生成器
        return new BasicPropertyGenerator().generate(operation, context);
    }
  }

  private generateTextureCode(operation: PropertyModificationInfo): CodeSnippet {
    // 纹理设置的特殊处理逻辑
    const { newValue, objectPath, className } = operation;
    const variableName = `sprite_${objectPath.join('_')}`;

    const code = [
      `const ${variableName}Path = [${objectPath.map(p => `"${p}"`).join(', ')}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`,
      `if (${variableName}) {`,
      `    ${variableName}.texture = PIXI.Texture.from("${newValue}");`,
      `    if (DEBUG) console.log('设置纹理:', "${newValue}", ${variableName});`,
      '}'
    ].join('\n');

    return {
      code,
      description: `设置Sprite纹理为 ${newValue}`,
      dependencies: ['findObjectByScenePath', 'PIXI.Texture']
    };
  }

  private generateHueCode(operation: PropertyModificationInfo): CodeSnippet {
    const { newValue, objectPath } = operation;
    const variableName = `sprite_${objectPath.join('_')}`;

    const code = [
      `const ${variableName}Path = [${objectPath.map(p => `"${p}"`).join(', ')}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`,
      `if (${variableName} && ${variableName}.setHue) {`,
      `    ${variableName}.setHue(${newValue});`,
      `    if (DEBUG) console.log('设置色相:', ${newValue}, ${variableName});`,
      '}'
    ].join('\n');

    return {
      code,
      description: `设置Sprite色相为 ${newValue}`,
      dependencies: ['findObjectByScenePath']
    };
  }

  private generateColorToneCode(operation: PropertyModificationInfo): CodeSnippet {
    const { newValue, objectPath } = operation;
    const variableName = `sprite_${objectPath.join('_')}`;
    const colorArray = Array.isArray(newValue) ? newValue : [0, 0, 0, 0];

    const code = [
      `const ${variableName}Path = [${objectPath.map(p => `"${p}"`).join(', ')}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`,
      `if (${variableName} && ${variableName}.setColorTone) {`,
      `    ${variableName}.setColorTone([${colorArray.join(', ')}]);`,
      `    if (DEBUG) console.log('设置色调:', [${colorArray.join(', ')}], ${variableName});`,
      '}'
    ].join('\n');

    return {
      code,
      description: `设置Sprite色调为 [${colorArray.join(', ')}]`,
      dependencies: ['findObjectByScenePath']
    };
  }

  private generateBlendColorCode(operation: PropertyModificationInfo): CodeSnippet {
    const { newValue, objectPath } = operation;
    const variableName = `sprite_${objectPath.join('_')}`;
    const colorArray = Array.isArray(newValue) ? newValue : [0, 0, 0, 0];

    const code = [
      `const ${variableName}Path = [${objectPath.map(p => `"${p}"`).join(', ')}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`,
      `if (${variableName} && ${variableName}.setBlendColor) {`,
      `    ${variableName}.setBlendColor([${colorArray.join(', ')}]);`,
      `    if (DEBUG) console.log('设置混合颜色:', [${colorArray.join(', ')}], ${variableName});`,
      '}'
    ].join('\n');

    return {
      code,
      description: `设置Sprite混合颜色为 [${colorArray.join(', ')}]`,
      dependencies: ['findObjectByScenePath']
    };
  }
}

// ==================== Container属性生成器 ====================

/**
 * Container属性生成器
 * 处理Container特有的属性
 */
export class ContainerPropertyGenerator implements PropertyGeneratorHandler {
  generate(operation: PropertyModificationInfo, context: any): CodeSnippet {
    // Container特有属性的处理逻辑
    return new BasicPropertyGenerator().generate(operation, context);
  }
}

// ==================== 自定义属性生成器 ====================

/**
 * 自定义属性生成器
 * 处理不在标准分类中的属性
 */
export class CustomPropertyGenerator implements PropertyGeneratorHandler {
  generate(operation: PropertyModificationInfo, context: any): CodeSnippet {
    // 自定义属性的通用处理逻辑
    return new BasicPropertyGenerator().generate(operation, context);
  }
}
