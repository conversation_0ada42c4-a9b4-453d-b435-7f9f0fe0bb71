D:\gpgmake\rpgEditor\src-tauri\target\debug\deps\rpgeditor.exe: src\main.rs src\asset_manager.rs src\coreRPGtype\mod.rs src\coreRPGtype\readJs.rs src\coreRPGtype\type_reader.rs src\file_utils.rs src\history.rs src\project.rs src\save\mod.rs src\save\bitmap_generator.rs src\save\filter_generator.rs src\save\modification_manager.rs src\save\object_lifecycle_generator.rs src\save\path_utils.rs src\save\plugin_generator.rs src\save\plugin_parser.rs src\save\plugins_file.rs src\save\save.rs src\save\types.rs src\save\unified_api\mod.rs src\save\unified_api\entry_api.rs src\save\unified_api\operation_router.rs src\save\unified_api\type_operations.rs src\save\unified_api\object_operations.rs src\temp_plugins.rs src\utils.rs D:\gpgmake\rpgEditor\src-tauri\target\debug\build\rpgeditor-14bb3894b7ee3933\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

D:\gpgmake\rpgEditor\src-tauri\target\debug\deps\rpgeditor.d: src\main.rs src\asset_manager.rs src\coreRPGtype\mod.rs src\coreRPGtype\readJs.rs src\coreRPGtype\type_reader.rs src\file_utils.rs src\history.rs src\project.rs src\save\mod.rs src\save\bitmap_generator.rs src\save\filter_generator.rs src\save\modification_manager.rs src\save\object_lifecycle_generator.rs src\save\path_utils.rs src\save\plugin_generator.rs src\save\plugin_parser.rs src\save\plugins_file.rs src\save\save.rs src\save\types.rs src\save\unified_api\mod.rs src\save\unified_api\entry_api.rs src\save\unified_api\operation_router.rs src\save\unified_api\type_operations.rs src\save\unified_api\object_operations.rs src\temp_plugins.rs src\utils.rs D:\gpgmake\rpgEditor\src-tauri\target\debug\build\rpgeditor-14bb3894b7ee3933\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

src\main.rs:
src\asset_manager.rs:
src\coreRPGtype\mod.rs:
src\coreRPGtype\readJs.rs:
src\coreRPGtype\type_reader.rs:
src\file_utils.rs:
src\history.rs:
src\project.rs:
src\save\mod.rs:
src\save\bitmap_generator.rs:
src\save\filter_generator.rs:
src\save\modification_manager.rs:
src\save\object_lifecycle_generator.rs:
src\save\path_utils.rs:
src\save\plugin_generator.rs:
src\save\plugin_parser.rs:
src\save\plugins_file.rs:
src\save\save.rs:
src\save\types.rs:
src\save\unified_api\mod.rs:
src\save\unified_api\entry_api.rs:
src\save\unified_api\operation_router.rs:
src\save\unified_api\type_operations.rs:
src\save\unified_api\object_operations.rs:
src\temp_plugins.rs:
src\utils.rs:
D:\gpgmake\rpgEditor\src-tauri\target\debug\build\rpgeditor-14bb3894b7ee3933\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=rpgeditor
# env-dep:OUT_DIR=D:\\gpgmake\\rpgEditor\\src-tauri\\target\\debug\\build\\rpgeditor-14bb3894b7ee3933\\out
