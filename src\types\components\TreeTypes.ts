/**
 * 类型模板定义文件
 * 包含基本的类型模板，用于创建对象
 */

import { NodeType } from '../../utils/tree/TreeNodeTypes';

// 类型模板接口
export interface TypeTemplate {
  id: string;         // 类型ID
  name: string;       // 显示名称
  icon: string;       // 图标名称或组件
  nodeType: NodeType; // 节点类型
  description?: string; // 类型描述
  createFn: (params?: any) => any; // 创建函数
}

/**
 * 基本类型模板列表
 * 包含系统内置的基本类型
 */
export const basicTypeTemplates: TypeTemplate[] = [
  {
    id: "Sprite",
    name: "新Sprite",
    icon: "sprite-icon",
    nodeType: NodeType.SPRITE,
    description: "基本图像显示对象",
    createFn: (params) => {
      // 获取全局Sprite构造函数
      const Sprite = (window as any).Sprite;
      if (!Sprite) {
        console.error("Sprite构造函数不可用");
        return null;
      }

      // 创建Sprite实例
      const sprite = new Sprite();
      sprite.name = params?.name || "新Sprite";
      sprite.x = params?.x || 0;
      sprite.y = params?.y || 0;
      sprite.visible = params?.visible !== undefined ? params.visible : true;

      return sprite;
    }
  },
  {
    id: "Label",
    name: "新Label",
    icon: "label-icon",
    nodeType: NodeType.LABEL,
    description: "文本标签对象",
    createFn: (params) => {
      // 获取全局Sprite和Bitmap构造函数
      const Sprite = (window as any).Sprite;
      const Bitmap = (window as any).Bitmap;

      if (!Sprite || !Bitmap) {
        console.error("Sprite或Bitmap构造函数不可用");
        return null;
      }

      // 创建Label实例（实际上是带有文本的Sprite）
      const sprite = new Sprite();
      sprite.name = params?.name || "新Label";
      sprite.x = params?.x || 0;
      sprite.y = params?.y || 0;
      sprite.visible = params?.visible !== undefined ? params.visible : true;

      // 创建位图并设置文本
      const bitmap = new Bitmap(200, 40);
      bitmap.fontSize = 20;
      bitmap.textColor = "#ffffff";
      bitmap.outlineColor = "rgba(0, 0, 0, 0.5)";
      bitmap.outlineWidth = 4;
      bitmap.drawText(params?.text || "新文本", 0, 0, 200, 40, "left");
      bitmap.text = params?.text || "新文本"; // 添加自定义属性，用于在树中显示

      // 设置位图
      sprite.bitmap = bitmap;

      return sprite;
    }
  },
  {
    id: "Container",
    name: "新Container",
    icon: "container-icon",
    nodeType: NodeType.CONTAINER,
    description: "容器对象，可以包含其他显示对象",
    createFn: (params) => {
      // 获取全局PIXI对象
      const PIXI = (window as any).PIXI;
      if (!PIXI || !PIXI.Container) {
        console.error("PIXI.Container构造函数不可用");
        return null;
      }

      // 创建Container实例
      const container = new PIXI.Container();
      container.name = params?.name || "新Container";
      container.x = params?.x || 0;
      container.y = params?.y || 0;
      container.visible = params?.visible !== undefined ? params.visible : true;

      return container;
    }
  },
  {
    id: "Window",
    name: "新Window",
    icon: "window-icon",
    nodeType: NodeType.WINDOW,
    description: "窗口对象，用于显示UI元素",
    createFn: (params) => {
      // 获取全局Window_Base和Rectangle构造函数
      const Window_Base = (window as any).Window_Base;
      const Rectangle = (window as any).Rectangle;

      if (!Window_Base || !Rectangle) {
        console.error("Window_Base或Rectangle构造函数不可用");
        return null;
      }

      // 创建Window实例
      const rect = new Rectangle(0, 0, 200, 100);
      const windowObj = new Window_Base(rect);
      windowObj.name = params?.name || "新Window";
      windowObj.x = params?.x || 0;
      windowObj.y = params?.y || 0;
      windowObj.visible = params?.visible !== undefined ? params.visible : true;

      return windowObj;
    }
  },
  {
    id: "Button",
    name: "新Button",
    icon: "button-icon",
    nodeType: NodeType.BUTTON,
    description: "按钮对象，可以响应点击事件",
    createFn: (params) => {
      // 获取全局Sprite_Clickable和Bitmap构造函数
      const Sprite_Clickable = (window as any).Sprite_Clickable;
      const Bitmap = (window as any).Bitmap;

      if (!Sprite_Clickable || !Bitmap) {
        console.error("Sprite_Clickable或Bitmap构造函数不可用");
        return null;
      }

      // 创建Button实例
      const button = new Sprite_Clickable();
      button.name = params?.name || "新Button";
      button.x = params?.x || 0;
      button.y = params?.y || 0;
      button.visible = params?.visible !== undefined ? params.visible : true;

      // 创建位图并设置按钮外观
      const bitmap = new Bitmap(120, 40);
      bitmap.fillRect(0, 0, 120, 40, "#3498db");
      bitmap.fontSize = 18;
      bitmap.textColor = "#ffffff";
      bitmap.drawText(params?.text || "按钮", 0, 0, 120, 40, "center");

      // 设置位图
      button.bitmap = bitmap;

      // 添加按钮标记
      (button as any)._isButton = true;

      return button;
    }
  },
  {
    id: "LayoutContainer",
    name: "新布局容器",
    icon: "layout-icon",
    nodeType: NodeType.LAYOUT_CONTAINER,
    description: "布局容器，用于自动排列子元素",
    createFn: (params) => {
      // 获取全局LayoutContainer构造函数
      const LayoutContainer = (window as any).LayoutContainer;
      const Sprite = (window as any).Sprite;

      if (LayoutContainer) {
        // 如果有LayoutContainer类可用
        const layoutContainer = new LayoutContainer();
        layoutContainer.name = params?.name || "新布局容器";
        layoutContainer.x = params?.x || 0;
        layoutContainer.y = params?.y || 0;
        layoutContainer.visible = params?.visible !== undefined ? params.visible : true;

        return layoutContainer;
      } else if (Sprite) {
        // 如果LayoutContainer不可用，创建普通Sprite作为替代
        console.warn("LayoutContainer类不可用，创建普通Sprite作为替代");
        const sprite = new Sprite();
        sprite.name = params?.name || "新布局容器";
        sprite.x = params?.x || 0;
        sprite.y = params?.y || 0;
        sprite.visible = params?.visible !== undefined ? params.visible : true;

        // 添加标记，表示这是一个布局容器
        (sprite as any)._isLayoutContainer = true;

        return sprite;
      } else {
        console.error("无法创建布局容器：缺少必要的构造函数");
        return null;
      }
    }
  }
];

/**
 * 获取合并后的类型模板
 * 包含基本类型模板
 */
export function getMergedTypeTemplates(): TypeTemplate[] {
  console.log('getMergedTypeTemplates - 开始获取类型模板');

  // 获取基本类型模板
  const baseTemplates = [...basicTypeTemplates];
  console.log('getMergedTypeTemplates - 基本类型模板:', baseTemplates);

  // 不再支持自定义类型模板
  console.log('getMergedTypeTemplates - 返回基本类型模板');
  return baseTemplates;
}

/**
 * 根据类型ID创建对象
 * @param typeId 类型ID
 * @param params 创建参数
 * @param parentObjects 父级对象数组，创建后会自动添加到这些对象中
 * @returns 创建的对象实例
 */
export function createObjectByType(typeId: string, params?: any, parentObjects?: any[]): any {
  // 获取所有类型模板
  const templates = getMergedTypeTemplates();

  // 查找匹配的模板
  const template = templates.find(t => t.id === typeId);

  if (!template) {
    console.error(`未找到类型模板: ${typeId}`);
    return null;
  }

  try {
    // 调用创建函数
    const instance = template.createFn(params);

    if (instance) {
      // console.log(`对象创建成功，添加自定义对象标记...`);

      // 添加自定义对象标记
      instance._isCustomObject = true;
      instance._typeName = typeId;
      instance._createdAt = new Date().toISOString();

      // 获取父类型名称
      const parentTypeName = params && params._parentTypeName ? params._parentTypeName : null;
      instance._parentTypeName = parentTypeName;

      // 如果提供了父级对象数组，将创建的对象添加到这些父级对象中
      if (parentObjects && parentObjects.length > 0) {
        console.log(`将对象添加到 ${parentObjects.length} 个父级对象中...`);

        parentObjects.forEach((parentObj, index) => {
          if (!parentObj) {
            console.warn(`父级对象 #${index} 为空，跳过`);
            return;
          }

          console.log(`添加到父级对象 #${index}:`, parentObj.name || parentObj.constructor?.name || '未命名对象');

          // 检查父级对象是否有children数组
          if (parentObj.children) {
            console.log(`使用 children 数组添加子对象...`);
            parentObj.children.push(instance);
            // 设置父对象引用
            instance.parent = parentObj;
          }
          // 检查父级对象是否有addChild方法
          else if (typeof parentObj.addChild === 'function') {
            console.log(`使用 addChild 方法添加子对象...`);
            parentObj.addChild(instance);
          }
          else {
            console.warn(`父级对象 #${index} 既没有 children 数组也没有 addChild 方法，无法添加子对象`);
          }
        });
      }
    }

    return instance;
  } catch (error) {
    console.error(`创建对象失败: ${typeId}`, error);
    return null;
  }
}

/**
 * 删除对象
 * @param object 要删除的对象
 * @param parentType 父类型名称，如果为空则直接从父级对象中删除
 * @param gameWindow 游戏窗口对象
 * @returns 是否删除成功
 */
export function deleteObject(object: any, parentType: string | null, gameWindow: any): boolean {
  if (!object) {
    console.error('无法删除对象: 对象为空');
    return false;
  }

  try {
    console.log(`删除对象:`, object);

    // 获取对象ID
    const objectId = object._createdAt;
    if (!objectId) {
      console.warn('对象没有_createdAt属性，可能不是自定义对象');
    }

    // 如果没有提供父类型，尝试从对象本身获取
    if (!parentType && object._parentTypeName) {
      parentType = object._parentTypeName;
      console.log(`从对象获取到父类型: ${parentType}`);
    }

    // 递归删除子对象
    const deleteChildObjects = (obj: any) => {
      if (!obj || !obj.children || !Array.isArray(obj.children)) return;

      // 创建一个子对象的副本，因为我们会在遍历过程中修改数组
      const childrenCopy = [...obj.children];

      // 遍历所有子对象
      for (const child of childrenCopy) {
        if (child && child._isCustomObject) {
          // 递归删除子对象的子对象
          deleteChildObjects(child);

          // 从全局customObjects数组中删除子对象记录
          if (gameWindow && gameWindow.RPGMakerMZTypes && gameWindow.RPGMakerMZTypes.customObjects) {
            const customObjects = gameWindow.RPGMakerMZTypes.customObjects;
            const childIndex = customObjects.findIndex((o: any) => o.id === child._createdAt);

            if (childIndex !== -1) {
              customObjects.splice(childIndex, 1);
              console.log(`删除嵌套对象: 从customObjects中删除子对象记录`, child);
            }
          }
        }
      }
    };

    // 先递归删除所有子对象
    deleteChildObjects(object);

    // 从全局customObjects数组中删除对象记录
    if (gameWindow && gameWindow.RPGMakerMZTypes && gameWindow.RPGMakerMZTypes.customObjects) {
      const customObjects = gameWindow.RPGMakerMZTypes.customObjects;
      const objectIndex = customObjects.findIndex((obj: any) => obj.id === objectId);

      if (objectIndex !== -1) {
        // 从数组中删除对象
        customObjects.splice(objectIndex, 1);
        console.log(`成功从customObjects数组中删除对象记录`);
      }
    }

    // 如果提供了父类型，需要先查找所有该类型的对象，然后删除其中的目标对象
    if (parentType) {
      console.log(`删除类型 ${parentType} 中的对象，先查找所有该类型的对象...`);

      if (!gameWindow) {
        console.error('游戏窗口对象不可用，无法查找对象');
        return false;
      }

      // 导入findAllInstancesInCurrentScene函数
      const { findAllInstancesInCurrentScene } = require('../../utils/object/ObjectFinder');

      // 查找当前场景中所有父类型的对象
      const typeInstances = findAllInstancesInCurrentScene(gameWindow, parentType);
      console.log(`找到 ${typeInstances.length} 个 ${parentType} 类型的对象`);

      // 遍历所有找到的对象，查找并删除目标对象
      let deleted = false;
      for (const instance of typeInstances) {
        // 递归查找和删除对象
        const findAndRemoveObjects = (container: any): boolean => {
          if (!container || !container.children) return false;

          // 创建一个要删除的对象数组
          const objectsToRemove = [];

          // 遍历子对象
          for (let i = 0; i < container.children.length; i++) {
            const child = container.children[i];

            // 检查是否是要删除的对象
            if (child && child._isCustomObject && child._createdAt === objectId) {
              console.log(`在 ${parentType} 实例中找到要删除的对象:`, child);
              objectsToRemove.push(child);
            }

            // 递归检查子对象的子对象
            if (child && child.children && child.children.length > 0) {
              const childDeleted = findAndRemoveObjects(child);
              if (childDeleted) deleted = true;
            }
          }

          // 删除找到的对象
          for (const obj of objectsToRemove) {
            if (container.removeChild) {
              container.removeChild(obj);
              console.log(`已从 ${parentType} 实例中删除对象:`, obj);
              deleted = true;
            }
          }

          return objectsToRemove.length > 0;
        };

        // 从当前类型实例开始递归查找和删除对象
        findAndRemoveObjects(instance);
      }

      if (deleted) {
        console.log(`成功从 ${parentType} 类型的对象中删除目标对象`);
      } else {
        console.warn(`在 ${parentType} 类型的对象中未找到要删除的对象`);
      }

      return deleted;
    }
    // 如果没有提供父类型，直接从父级对象中删除
    else {
      console.log(`直接从父级对象中删除对象...`);

      // 获取父级对象
      const parent = object.parent;

      if (!parent) {
        console.warn('对象没有父级对象，无法删除');
        return false;
      }

      // 从父级对象中删除
      if (parent.removeChild) {
        parent.removeChild(object);
        console.log(`已从父级对象中删除对象`);
      } else if (parent.children) {
        // 如果父级对象没有removeChild方法但有children数组
        const index = parent.children.indexOf(object);
        if (index !== -1) {
          parent.children.splice(index, 1);
          console.log(`已从父级对象的children数组中删除对象`);
        }
      } else {
        console.warn('父级对象既没有removeChild方法也没有children数组，无法删除对象');
        return false;
      }

      return true;
    }
  } catch (error) {
    console.error(`删除对象失败:`, error);
    return false;
  }
}
