// 在 iframe 内部使用文件选择和文件转换功能的示例

// 示例1: 使用文件选择对话框（同时获取路径和ArrayBuffer）
function selectFileExample() {
  // 检查是否有外部方法可用
  if (window.handleFileSelectDialog) {
    console.log('调用外部文件选择对话框...');

    // 调用文件选择对话框，传入回调函数
    window.handleFileSelectDialog((filePath, arrayBuffer) => {
      if (filePath) {
        console.log('用户选择了文件:', filePath);
        if (arrayBuffer) {
          console.log('文件大小:', arrayBuffer.byteLength, 'bytes');
          // 在这里同时处理文件路径和ArrayBuffer数据
          processFileData(filePath, arrayBuffer);
        } else {
          console.log('文件读取失败，但获得了文件路径');
        }
      } else {
        console.log('用户取消了文件选择');
      }
    });
  } else {
    console.error('外部文件选择方法不可用');
  }
}

// 示例2: 将文件路径转换为ArrayBuffer
function convertFileToArrayBufferExample(filePath) {
  // 检查是否有外部方法可用
  if (window.convertFilePathToArrayBuffer) {
    console.log('转换文件为ArrayBuffer:', filePath);

    // 调用文件转换方法，传入文件路径和回调函数
    window.convertFilePathToArrayBuffer(filePath, (buffer) => {
      if (buffer) {
        console.log('文件转换成功，大小:', buffer.byteLength, 'bytes');
        // 在这里处理ArrayBuffer
        // 例如：创建Blob、读取图片数据等

        // 示例：如果是图片文件，可以创建一个Image对象
        if (filePath.match(/\.(png|jpg|jpeg|gif|bmp|webp)$/i)) {
          const blob = new Blob([buffer]);
          const url = URL.createObjectURL(blob);
          const img = new Image();
          img.onload = () => {
            console.log('图片加载成功，尺寸:', img.width, 'x', img.height);
            URL.revokeObjectURL(url); // 清理内存
          };
          img.src = url;
        }
      } else {
        console.error('文件转换失败');
      }
    });
  } else {
    console.error('外部文件转换方法不可用');
  }
}

// 示例3: 使用文件选择对话框（现在已经同时返回路径和ArrayBuffer）
function selectAndConvertFileExample() {
  // 使用文件选择对话框，一次性获取路径和ArrayBuffer
  if (window.handleFileSelectDialog) {
    window.handleFileSelectDialog((filePath, arrayBuffer) => {
      if (filePath && arrayBuffer) {
        console.log('文件选择和读取都成功完成');
        console.log('文件路径:', filePath);
        console.log('文件大小:', arrayBuffer.byteLength, 'bytes');

        // 在这里进行后续处理
        processFileData(filePath, arrayBuffer);
      } else if (filePath) {
        console.log('文件选择成功但读取失败:', filePath);
      } else {
        console.log('用户取消了文件选择');
      }
    });
  }
}

// 处理文件数据的示例函数
function processFileData(filePath, arrayBuffer) {
  console.log('处理文件数据:', filePath);

  // 根据文件类型进行不同的处理
  const extension = filePath.split('.').pop().toLowerCase();

  switch (extension) {
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'bmp':
    case 'webp':
      // 处理图片文件
      handleImageFile(filePath, arrayBuffer);
      break;

    case 'mp3':
    case 'wav':
    case 'ogg':
    case 'm4a':
      // 处理音频文件
      handleAudioFile(filePath, arrayBuffer);
      break;

    case 'json':
      // 处理JSON文件
      handleJsonFile(filePath, arrayBuffer);
      break;

    default:
      console.log('未知文件类型:', extension);
  }
}

function handleImageFile(filePath, arrayBuffer) {
  console.log('处理图片文件:', filePath);
  // 创建图片对象并显示
  const blob = new Blob([arrayBuffer]);
  const url = URL.createObjectURL(blob);

  const img = new Image();
  img.onload = () => {
    console.log('图片加载完成:', img.width, 'x', img.height);
    // 在这里可以将图片添加到canvas或其他地方
    URL.revokeObjectURL(url);
  };
  img.src = url;
}

function handleAudioFile(filePath, arrayBuffer) {
  console.log('处理音频文件:', filePath);
  // 创建音频对象
  const blob = new Blob([arrayBuffer]);
  const url = URL.createObjectURL(blob);

  const audio = new Audio(url);
  audio.onloadeddata = () => {
    console.log('音频加载完成，时长:', audio.duration, '秒');
    URL.revokeObjectURL(url);
  };
}

function handleJsonFile(filePath, arrayBuffer) {
  console.log('处理JSON文件:', filePath);
  // 将ArrayBuffer转换为文本
  const decoder = new TextDecoder('utf-8');
  const text = decoder.decode(arrayBuffer);

  try {
    const data = JSON.parse(text);
    console.log('JSON数据解析成功:', data);
  } catch (error) {
    console.error('JSON解析失败:', error);
  }
}

// 导出示例函数供外部调用
window.FileExamples = {
  selectFile: selectFileExample,
  convertFile: convertFileToArrayBufferExample,
  selectAndConvert: selectAndConvertFileExample
};

console.log('文件处理示例已加载，可以通过 window.FileExamples 访问示例函数');
