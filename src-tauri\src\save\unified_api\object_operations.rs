use tauri::AppHandle;
use crate::utils::logging;
use crate::save::modification_manager;
use super::operation_router::OperationRequest;

/// 处理对象创建操作
/// 在场景初始化中创建对象
pub fn handle_object_create(
    app_handle: AppHandle,
    request: OperationRequest,
) -> Result<String, String> {
    logging::log_info(
        "ObjectOperations",
        &format!(
            "处理对象创建操作 - 路径: {:?}, 字段: {}, 类名: {}",
            request.target_path, request.field_name, request.class_name
        ),
    );

    // 验证场景路径
    if request.target_path.is_empty() {
        return Err("对象创建操作缺少场景路径".to_string());
    }

    let scene_name = &request.target_path[0];
    if !scene_name.starts_with("Scene_") {
        return Err(format!(
            "无效的场景名称: {}，对象创建操作需要有效的场景路径",
            scene_name
        ));
    }

    // 调用对象属性修改接口
    // 这将在场景初始化中生成对象创建代码
    modification_manager::record_property_modification(
        app_handle,
        request.target_path,
        request.class_name,
        request.field_name,
        request.field_value,
    )
}

/// 处理对象删除操作
/// 在场景初始化中删除对象
pub fn handle_object_delete(
    app_handle: AppHandle,
    request: OperationRequest,
) -> Result<String, String> {
    logging::log_info(
        "ObjectOperations",
        &format!(
            "处理对象删除操作 - 路径: {:?}, 字段: {}, 类名: {}",
            request.target_path, request.field_name, request.class_name
        ),
    );

    // 验证场景路径
    if request.target_path.is_empty() {
        return Err("对象删除操作缺少场景路径".to_string());
    }

    let scene_name = &request.target_path[0];
    if !scene_name.starts_with("Scene_") {
        return Err(format!(
            "无效的场景名称: {}，对象删除操作需要有效的场景路径",
            scene_name
        ));
    }

    // 调用对象属性修改接口
    // 这将在场景初始化中生成对象删除代码
    modification_manager::record_property_modification(
        app_handle,
        request.target_path,
        request.class_name,
        request.field_name,
        request.field_value,
    )
}

/// 处理对象修改操作
/// 在场景初始化中修改对象属性
pub fn handle_object_modify(
    app_handle: AppHandle,
    request: OperationRequest,
) -> Result<String, String> {
    logging::log_info(
        "ObjectOperations",
        &format!(
            "处理对象修改操作 - 路径: {:?}, 字段: {}, 类名: {}",
            request.target_path, request.field_name, request.class_name
        ),
    );

    // 验证场景路径
    if request.target_path.is_empty() {
        return Err("对象修改操作缺少场景路径".to_string());
    }

    let scene_name = &request.target_path[0];
    if !scene_name.starts_with("Scene_") {
        return Err(format!(
            "无效的场景名称: {}，对象修改操作需要有效的场景路径",
            scene_name
        ));
    }

    // 根据路径和字段操作具体的值
    if request.target_path.len() == 1 {
        // 直接在场景上操作
        logging::log_info(
            "ObjectOperations",
            &format!("直接修改场景 {} 的属性", scene_name),
        );
    } else {
        // 在场景的嵌套对象上操作
        logging::log_info(
            "ObjectOperations",
            &format!(
                "修改场景 {} 中路径 {:?} 的属性",
                scene_name, request.target_path
            ),
        );
    }

    // 调用对象属性修改接口
    // 这将在场景初始化中生成属性修改代码
    modification_manager::record_property_modification(
        app_handle,
        request.target_path,
        request.class_name,
        request.field_name,
        request.field_value,
    )
}
