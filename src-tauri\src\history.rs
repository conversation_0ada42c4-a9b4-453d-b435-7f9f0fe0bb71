use serde::{Deserialize, Serialize};
use std::collections::VecDeque;
use std::sync::Mutex;
use std::time::{SystemTime, UNIX_EPOCH};
use tauri::AppHandle;
use tauri::Manager;
// 操作类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OperationType {
    ModifyProperty,
    AddObject,
    DeleteObject,
}

// 操作详情
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct OperationDetails {
    pub property_name: Option<String>,
    pub old_value: Option<serde_json::Value>,
    pub new_value: Option<serde_json::Value>,
    // 可以根据需要添加更多字段
}

// 操作记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Operation {
    pub id: String,
    pub operation_type: OperationType,
    pub timestamp: u64,
    pub object_path: Vec<String>,
    pub details: OperationDetails,
}

// 历史记录状态
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct HistoryState {
    pub operations: VecDeque<Operation>,
    pub current_index: i32,
    pub max_size: usize,
}

impl HistoryState {
    pub fn new(max_size: usize) -> Self {
        Self {
            operations: VecDeque::with_capacity(max_size),
            current_index: -1,
            max_size,
        }
    }

    // 添加操作
    pub fn add_operation(&mut self, operation: Operation) {
        // 如果当前不是最新操作，则删除当前索引之后的所有操作
        // 安全处理：确保 current_index 是有效的正数，避免整数溢出
        if self.current_index >= 0 && self.current_index < (self.operations.len() as i32 - 1) {
            let current_idx_usize = self.current_index as usize;
            // 计算需要移除的元素数量
            let to_remove = self.operations.len().saturating_sub(current_idx_usize + 1);
            for _ in 0..to_remove {
                self.operations.pop_back();
            }
        } else if self.current_index < 0 {
            // 如果索引为负，清空所有操作（因为我们将从头开始）
            self.operations.clear();
        }

        // 添加新操作
        self.operations.push_back(operation);

        // 如果超过最大容量，删除最早的操作
        if self.operations.len() > self.max_size {
            self.operations.pop_front();
        }

        // 更新当前索引
        self.current_index = self.operations.len() as i32 - 1;
    }

    // 获取当前操作
    pub fn current_operation(&self) -> Option<&Operation> {
        if self.current_index >= 0 && self.current_index < self.operations.len() as i32 {
            self.operations.get(self.current_index as usize)
        } else {
            None
        }
    }

    // 获取上一个操作（用于撤销）
    pub fn previous_operation(&self) -> Option<&Operation> {
        if self.current_index > 0 && (self.current_index as usize) < self.operations.len() {
            self.operations.get((self.current_index - 1) as usize)
        } else {
            None
        }
    }

    // 获取下一个操作（用于重做）
    pub fn next_operation(&self) -> Option<&Operation> {
        if self.current_index >= 0 && self.current_index < (self.operations.len() as i32 - 1) {
            self.operations.get((self.current_index + 1) as usize)
        } else {
            None
        }
    }

    // 撤销操作
    pub fn undo(&mut self) -> Option<&Operation> {
        if self.current_index >= 0 && (self.current_index as usize) < self.operations.len() {
            // 先获取当前操作的引用
            let operation = self.operations.get(self.current_index as usize);
            // 然后减少索引
            self.current_index -= 1;
            // 返回操作引用
            operation
        } else {
            None
        }
    }

    // 重做操作
    pub fn redo(&mut self) -> Option<&Operation> {
        if self.current_index < (self.operations.len() as i32 - 1) && self.operations.len() > 0 {
            self.current_index += 1;
            if self.current_index >= 0 && (self.current_index as usize) < self.operations.len() {
                self.operations.get(self.current_index as usize)
            } else {
                None
            }
        } else {
            None
        }
    }

    // 清空历史记录
    pub fn clear(&mut self) {
        self.operations.clear();
        self.current_index = -1;
    }

    // 检查是否可以撤销
    pub fn can_undo(&self) -> bool {
        self.current_index >= 0
    }

    // 检查是否可以重做
    pub fn can_redo(&self) -> bool {
        self.current_index < (self.operations.len() as i32 - 1)
    }
}

// 全局历史记录状态
pub struct HistoryManager(pub Mutex<HistoryState>);

// 生成唯一ID
fn generate_id() -> String {
    // 使用 as_secs_f64 代替 as_millis 避免可能的整数溢出
    // 然后乘以 1000 并转换为整数，得到毫秒级时间戳
    let now = SystemTime::now().duration_since(UNIX_EPOCH).unwrap();

    // 使用秒和纳秒部分组合成唯一ID
    let secs = now.as_secs();
    let nanos = now.subsec_nanos();
    format!("{}{:09}", secs, nanos)
}

// 获取当前时间戳
fn get_timestamp() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs()
}

// 添加操作到历史记录
#[tauri::command]
pub fn add_operation(
    app_handle: AppHandle,
    operation_type: OperationType,
    object_path: Vec<String>,
    property_name: Option<String>,
    old_value: Option<serde_json::Value>,
    new_value: Option<serde_json::Value>,
) -> Result<String, String> {
    // 检查值是否相同，如果相同则不记录
    if let (Some(old), Some(new)) = (&old_value, &new_value) {
        if old == new {
            return Ok("值没有变化，不记录历史".to_string());
        }
    }

    let history_manager = app_handle.state::<HistoryManager>();
    let mut history = history_manager.0.lock().unwrap();

    let operation = Operation {
        id: generate_id(),
        operation_type,
        timestamp: get_timestamp(),
        object_path,
        details: OperationDetails {
            property_name,
            old_value,
            new_value,
        },
    };

    history.add_operation(operation);
    Ok("操作已添加到历史记录".to_string())
}

// 撤销操作
#[tauri::command]
pub fn undo_operation(app_handle: AppHandle) -> Result<Option<Operation>, String> {
    let history_manager = app_handle.state::<HistoryManager>();
    let mut history = history_manager.0.lock().unwrap();

    if !history.can_undo() {
        return Ok(None);
    }

    let operation = history.undo().cloned();
    Ok(operation)
}

// 重做操作
#[tauri::command]
pub fn redo_operation(app_handle: AppHandle) -> Result<Option<Operation>, String> {
    let history_manager = app_handle.state::<HistoryManager>();
    let mut history = history_manager.0.lock().unwrap();

    if !history.can_redo() {
        return Ok(None);
    }

    let operation = history.redo().cloned();
    Ok(operation)
}

// 清空历史记录
#[tauri::command]
pub fn clear_history(app_handle: AppHandle) -> Result<String, String> {
    let history_manager = app_handle.state::<HistoryManager>();
    let mut history = history_manager.0.lock().unwrap();

    history.clear();
    Ok("历史记录已清空".to_string())
}

// 获取历史记录状态
#[tauri::command]
pub fn get_history_state(app_handle: AppHandle) -> Result<HistoryState, String> {
    let history_manager = app_handle.state::<HistoryManager>();
    let history = history_manager.0.lock().unwrap();

    Ok(history.clone())
}

// 检查是否可以撤销
#[tauri::command]
pub fn can_undo(app_handle: AppHandle) -> Result<bool, String> {
    let history_manager = app_handle.state::<HistoryManager>();
    let history = history_manager.0.lock().unwrap();

    Ok(history.can_undo())
}

// 检查是否可以重做
#[tauri::command]
pub fn can_redo(app_handle: AppHandle) -> Result<bool, String> {
    let history_manager = app_handle.state::<HistoryManager>();
    let history = history_manager.0.lock().unwrap();

    Ok(history.can_redo())
}
