use crate::save::path_utils::process_image_path;
use serde_json;
use std::collections::HashMap;

/// 生成bitmap相关代码
pub fn generate_bitmap_code(
    var_name: &str,
    properties: &HashMap<String, serde_json::Value>,
) -> String {
    let mut code = String::new();

    code.push_str(&format!("        // 合并处理所有_bitmap相关属性\n"));
    code.push_str(&format!(
        "        if (targetObject_{}._bitmap) {{\n",
        var_name
    ));

    // 检查是否有 _bitmap.elements 相关的修改
    let has_elements_modifications = properties.keys().any(|k| k.starts_with("_bitmap.elements"));

    // 检查是否有图片元素（仅当 _bitmap.elements 数组中包含图片类型元素时）
    let has_image_elements = check_for_image_elements(properties);

    if has_elements_modifications {
        // 优先处理 _bitmap.elements 的直接修改
        code.push_str(&generate_elements_modification_code(var_name, properties));
    } else if has_image_elements {
        // 只有当没有直接的 elements 修改时，才处理图片加载
        code.push_str(&generate_image_loading_code(var_name, properties));
    } else {
        code.push_str(&generate_text_redraw_code(var_name, properties));
    }

    code.push_str(&format!("        }}\n"));

    code
}

/// 检查是否有图片元素
fn check_for_image_elements(properties: &HashMap<String, serde_json::Value>) -> bool {
    if let Some(elements_value) = properties.get("_bitmap.elements") {
        if let Ok(elements_array) =
            serde_json::from_value::<Vec<serde_json::Value>>(elements_value.clone())
        {
            for element in &elements_array {
                if let Some(element_type) = element.get("type").and_then(|t| t.as_str()) {
                    if element_type == "image" {
                        return true;
                    }
                }
            }
        }
    }
    false
}

/// 生成图片加载代码
fn generate_image_loading_code(
    var_name: &str,
    properties: &HashMap<String, serde_json::Value>,
) -> String {
    let mut code = String::new();
    let mut image_urls = Vec::new();

    // 提取图片URLs
    if let Some(elements_value) = properties.get("_bitmap.elements") {
        if let Ok(elements_array) =
            serde_json::from_value::<Vec<serde_json::Value>>(elements_value.clone())
        {
            for element in &elements_array {
                if let Some(element_type) = element.get("type").and_then(|t| t.as_str()) {
                    if element_type == "image" {
                        if let Some(source) = element.get("source") {
                            if let Some(url) = source.get("_url").and_then(|u| u.as_str()) {
                                image_urls.push(url.to_string());
                            }
                        }
                    }
                }
            }
        }
    }

    // 为每个图片URL生成加载代码
    for (index, image_url) in image_urls.iter().enumerate() {
        code.push_str(&format!("            // 加载图片 {}\n", index + 1));

        // 使用专用的路径处理方法
        let path_code = process_image_path(image_url);
        code.push_str(&format!(
            "            const fullPath_{} = {};\n",
            index, path_code
        ));

        code.push_str(&format!(
            "            const newBitmap_{} = ImageManager.loadBitmapFromUrl(fullPath_{});\n",
            index, index
        ));
        code.push_str(&format!(
            "            newBitmap_{}.addLoadListener(function (bitmap) {{\n",
            index
        ));
        code.push_str(&format!(
            "                // 设置bitmap（这会自动触发必要的更新）\n"
        ));
        code.push_str(&format!(
            "                targetObject_{}.bitmap = bitmap;\n",
            var_name
        ));
        code.push_str(&format!(
            "                if (DEBUG) log('图片加载完成，已设置bitmap:', fullPath_{});\n",
            index
        ));
        code.push_str(&format!("            }});\n"));
    }

    code.push_str(&format!(
        "            // 图片元素将在加载完成后自动设置bitmap\n"
    ));

    code
}

/// 生成文本重绘代码
fn generate_text_redraw_code(
    var_name: &str,
    properties: &HashMap<String, serde_json::Value>,
) -> String {
    let mut code = String::new();

    // 设置所有_bitmap属性
    for (key, value) in properties {
        if key.starts_with("_bitmap.") {
            let bitmap_prop = key.trim_start_matches("_bitmap.");
            let value_str = serde_json::to_string(value).unwrap_or_else(|_| "null".to_string());
            code.push_str(&format!("            // 设置 {} 属性\n", bitmap_prop));
            code.push_str(&format!(
                "            targetObject_{}._bitmap.{} = {};\n",
                var_name, bitmap_prop, value_str
            ));
            code.push_str(&format!(
                "            if (DEBUG) log('设置文字样式 {} =', {});\n",
                bitmap_prop, value_str
            ));
        }
    }

    // 重绘逻辑
    code.push_str(&format!("            // 一次性重新绘制文字\n"));
    code.push_str(&format!("            // 先清除位图\n"));
    code.push_str(&format!(
        "            targetObject_{}._bitmap.clear();\n",
        var_name
    ));

    code.push_str(&format!("            // 检查是否有 elements 数组\n"));
    code.push_str(&format!(
        "            if (targetObject_{}._bitmap.elements && targetObject_{}._bitmap.elements.length > 0) {{\n",
        var_name, var_name
    ));

    code.push_str(&format!("                // 调用统一的重绘方法\n"));
    code.push_str(&format!(
        "                targetObject_{}._bitmap.redrawing();\n",
        var_name
    ));
    code.push_str(&format!("            }}\n"));

    code
}

/// 生成 _bitmap.elements 修改代码
fn generate_elements_modification_code(
    var_name: &str,
    properties: &HashMap<String, serde_json::Value>,
) -> String {
    let mut code = String::new();

    code.push_str(&format!("            // 处理 _bitmap.elements 相关修改\n"));

    // 首先处理整个数组的替换（如果存在）
    if let Some(elements_value) = properties.get("_bitmap.elements") {
        // 检查数组中是否包含图片元素
        let has_image_elements = check_elements_for_images(elements_value);

        if has_image_elements {
            // 如果包含图片元素，需要特殊处理
            code.push_str(&generate_elements_with_images_code(
                var_name,
                elements_value,
            ));
        } else {
            // 如果只包含文本元素，直接设置数组
            let value_str =
                serde_json::to_string(elements_value).unwrap_or_else(|_| "null".to_string());
            code.push_str(&format!(
                "            // 设置整个 elements 数组（仅文本）\n"
            ));
            code.push_str(&format!(
                "            targetObject_{}._bitmap.elements = {};\n",
                var_name, value_str
            ));
            code.push_str(&format!(
                "            if (DEBUG) log('设置 _bitmap.elements 数组，长度:', targetObject_{}._bitmap.elements.length);\n",
                var_name
            ));
        }
    }

    // 然后处理数组元素的具体属性修改
    for (key, value) in properties {
        if key.starts_with("_bitmap.elements[") && key != "_bitmap.elements" {
            let value_str = serde_json::to_string(value).unwrap_or_else(|_| "null".to_string());
            let bitmap_prop = key.trim_start_matches("_bitmap.");

            code.push_str(&format!("            // 设置 {} 属性\n", bitmap_prop));
            code.push_str(&format!(
                "            if (targetObject_{}._bitmap.{}) {{\n",
                var_name,
                // 提取数组索引部分，如 elements[0]
                bitmap_prop.split('.').next().unwrap_or("elements[0]")
            ));
            code.push_str(&format!(
                "                targetObject_{}._bitmap.{} = {};\n",
                var_name, bitmap_prop, value_str
            ));
            code.push_str(&format!(
                "                if (DEBUG) log('设置 _bitmap.{} =', {});\n",
                bitmap_prop, value_str
            ));
            code.push_str(&format!("            }} else {{\n"));
            code.push_str(&format!(
                "                if (DEBUG) log('警告: _bitmap.{} 不存在，跳过设置');\n",
                bitmap_prop.split('.').next().unwrap_or("elements[0]")
            ));
            code.push_str(&format!("            }}\n"));
        }
    }

    // 触发重绘
    code.push_str(&format!("            // 触发bitmap重绘\n"));
    code.push_str(&format!(
        "            if (targetObject_{}._bitmap.redrawing) {{\n",
        var_name
    ));
    code.push_str(&format!(
        "                targetObject_{}._bitmap.redrawing();\n",
        var_name
    ));
    code.push_str(&format!(
        "                if (DEBUG) log('触发 _bitmap 重绘');\n"
    ));
    code.push_str(&format!("            }} else {{\n"));
    code.push_str(&format!(
        "                if (DEBUG) log('警告: _bitmap.redrawing 方法不存在');\n"
    ));
    code.push_str(&format!("            }}\n"));

    code
}

/// 检查 elements 数组中是否包含图片元素
fn check_elements_for_images(elements_value: &serde_json::Value) -> bool {
    if let Ok(elements_array) =
        serde_json::from_value::<Vec<serde_json::Value>>(elements_value.clone())
    {
        for element in &elements_array {
            if let Some(element_type) = element.get("type").and_then(|t| t.as_str()) {
                if element_type == "image" {
                    return true;
                }
            }
        }
    }
    false
}

/// 生成包含图片元素的 elements 数组设置代码
fn generate_elements_with_images_code(
    var_name: &str,
    elements_value: &serde_json::Value,
) -> String {
    let mut code = String::new();

    code.push_str(&format!("            // 设置包含图片的 elements 数组\n"));

    if let Ok(elements_array) =
        serde_json::from_value::<Vec<serde_json::Value>>(elements_value.clone())
    {
        // 首先创建一个不包含source的临时数组
        let mut temp_elements = Vec::new();
        let mut image_urls = Vec::new();

        for (index, element) in elements_array.iter().enumerate() {
            if let Some(element_type) = element.get("type").and_then(|t| t.as_str()) {
                if element_type == "image" {
                    // 对于图片元素，提取URL并创建不包含source的版本
                    if let Some(source) = element.get("source") {
                        if let Some(url) = source.get("_url").and_then(|u| u.as_str()) {
                            image_urls.push((index, url.to_string()));
                        }
                    }

                    // 创建不包含source的元素副本
                    let mut temp_element = element.clone();
                    if let Some(obj) = temp_element.as_object_mut() {
                        obj.remove("source");
                    }
                    temp_elements.push(temp_element);
                } else {
                    // 文本元素直接添加
                    temp_elements.push(element.clone());
                }
            }
        }

        // 设置临时数组（不包含source）
        let temp_array_str =
            serde_json::to_string(&temp_elements).unwrap_or_else(|_| "[]".to_string());
        code.push_str(&format!(
            "            targetObject_{}._bitmap.elements = {};\n",
            var_name, temp_array_str
        ));

        // 为每个图片元素生成bitmap加载代码
        for (index, image_url) in image_urls {
            code.push_str(&format!("            // 为元素 {} 加载图片\n", index));

            // 使用路径处理方法
            let path_code = process_image_path(&image_url);
            code.push_str(&format!(
                "            const imagePath_{} = {};\n",
                index, path_code
            ));

            code.push_str(&format!(
                "            const imageBitmap_{} = ImageManager.loadBitmapFromUrl(imagePath_{});\n",
                index, index
            ));

            code.push_str(&format!(
                "            imageBitmap_{}.addLoadListener(function (bitmap) {{\n",
                index
            ));

            code.push_str(&format!("                // 设置元素 {} 的source\n", index));

            code.push_str(&format!(
                "                if (targetObject_{}._bitmap.elements[{}]) {{\n",
                var_name, index
            ));

            code.push_str(&format!(
                "                    targetObject_{}._bitmap.elements[{}].source = bitmap;\n",
                var_name, index
            ));

            code.push_str(&format!(
                "                    if (DEBUG) log('已设置元素 {} 的source bitmap:', imagePath_{});\n",
                index, index
            ));

            code.push_str(&format!("                    // 触发重绘\n"));

            code.push_str(&format!(
                "                    if (targetObject_{}._bitmap.redrawing) {{\n",
                var_name
            ));

            code.push_str(&format!(
                "                        targetObject_{}._bitmap.redrawing();\n",
                var_name
            ));

            code.push_str(&format!("                    }}\n"));
            code.push_str(&format!("                }}\n"));
            code.push_str(&format!("            }});\n"));
        }

        code.push_str(&format!(
            "            if (DEBUG) log('设置 _bitmap.elements 数组，长度:', targetObject_{}._bitmap.elements.length);\n",
            var_name
        ));
    }

    code
}

/// 生成设置图片的代码模板
///
/// # 参数
/// * `target_object_name` - 目标对象名称，如 "targetObject_sprite_Scene_Title_0_88300"
/// * `image_path` - 图片路径，如 "../projects/Project4/img/battlebacks2/DarkSpace.png"
///
/// # 返回
/// 返回完整的图片设置代码模板字符串
///
/// # 示例
/// ```
/// let code = generate_image_setting_code(
///     "targetObject_sprite_Scene_Title_0_88300",
///     "../projects/Project4/img/battlebacks2/DarkSpace.png"
/// );
/// ```
pub fn generate_image_setting_code(target_object_name: &str, image_path: &str) -> String {
    let processed_path = process_image_path(image_path);

    format!(
        r#"            const newBitmap = ImageManager.loadBitmapFromUrl({});
            newBitmap.addLoadListener(function (bitmap) {{
                // 设置bitmap（这会自动触发必要的更新）
                {}.bitmap = bitmap;
            }});"#,
        processed_path, target_object_name
    )
}
