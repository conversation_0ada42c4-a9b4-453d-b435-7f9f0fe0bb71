//=============================================================================
// RPGEditor_DisableDefaultInput.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc RPG Editor - 禁用默认输入处理 v1.0.0
 * <AUTHOR> Editor
 * @version 1.0.0
 * @description 禁用RPG Maker MZ的默认键盘输入处理，允许编辑器正常使用键盘输入
 *
 * @param enablePlugin
 * @text 启用插件
 * @desc 是否启用此插件
 * @type boolean
 * @default true
 *
 * @param disableBackspace
 * @text 禁用Backspace阻止
 * @desc 是否禁用对Backspace键的默认阻止行为
 * @type boolean
 * @default true
 *
 * @param disableArrowKeys
 * @text 禁用方向键阻止
 * @desc 是否禁用对方向键的默认阻止行为
 * @type boolean
 * @default true
 *
 * @param disableTab
 * @text 禁用Tab阻止
 * @desc 是否禁用对Tab键的默认阻止行为
 * @type boolean
 * @default true
 *
 * @param disablePageKeys
 * @text 禁用翻页键阻止
 * @desc 是否禁用对PageUp/PageDown键的默认阻止行为
 * @type boolean
 * @default true
 *
 * @help RPGEditor_DisableDefaultInput.js
 * 
 * 这个插件用于禁用RPG Maker MZ引擎的默认键盘输入处理，
 * 特别是阻止引擎对某些键（如Backspace、方向键等）调用preventDefault()。
 * 
 * 这样可以让编辑器中的输入框正常使用这些键进行文本编辑。
 * 
 * 使用方法：
 * 1. 将此插件放在插件列表的最前面
 * 2. 启用插件
 * 3. 根据需要配置要禁用的键
 * 
 * 注意：
 * - 此插件主要用于编辑器环境
 * - 在游戏运行时可能会影响正常的游戏输入
 * - 建议只在编辑器中使用
 */

(() => {
    'use strict';

    // 获取插件参数
    const pluginName = 'RPGEditor_DisableDefaultInput';
    const parameters = PluginManager.parameters(pluginName);

    const enablePlugin = parameters['enablePlugin'] === 'true';
    const disableBackspace = parameters['disableBackspace'] === 'true';
    const disableArrowKeys = parameters['disableArrowKeys'] === 'true';
    const disableTab = parameters['disableTab'] === 'true';
    const disablePageKeys = parameters['disablePageKeys'] === 'true';

    // 如果插件未启用，直接返回
    // if (!enablePlugin) {
    //     return;
    // }

    console.log('RPGEditor_DisableDefaultInput: 插件已启用');

    // 保存原始的Input._shouldPreventDefault方法
    const _Input_shouldPreventDefault = Input._shouldPreventDefault;

    // 重写Input._shouldPreventDefault方法
    Input._shouldPreventDefault = function (keyCode) {
        // 检查当前焦点元素是否是输入框
        const activeElement = document.activeElement;
        const isInputElement = activeElement && (
            activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA' ||
            activeElement.contentEditable === 'true' ||
            activeElement.closest('.MuiTextField-root') !== null ||
            activeElement.closest('[contenteditable="true"]') !== null
        );

        // 如果当前焦点在输入框上，根据配置决定是否阻止默认行为
        if (isInputElement) {
            switch (keyCode) {
                case 8: // backspace
                    if (disableBackspace) {
                        console.log('RPGEditor_DisableDefaultInput: 允许Backspace键在输入框中使用');
                        return false;
                    }
                    break;
                case 9: // tab
                    if (disableTab) {
                        console.log('RPGEditor_DisableDefaultInput: 允许Tab键在输入框中使用');
                        return false;
                    }
                    break;
                case 33: // pageup
                case 34: // pagedown
                    if (disablePageKeys) {
                        console.log('RPGEditor_DisableDefaultInput: 允许翻页键在输入框中使用');
                        return false;
                    }
                    break;
                case 37: // left arrow
                case 38: // up arrow
                case 39: // right arrow
                case 40: // down arrow
                    if (disableArrowKeys) {
                        console.log('RPGEditor_DisableDefaultInput: 允许方向键在输入框中使用');
                        return false;
                    }
                    break;
            }
        }

        // 对于非输入框或未配置禁用的键，使用原始行为
        return _Input_shouldPreventDefault.call(this, keyCode);
    };

    // 保存原始的Input._onKeyDown方法
    const _Input_onKeyDown = Input._onKeyDown;

    // 重写Input._onKeyDown方法，添加额外的检查
    Input._onKeyDown = function (event) {
        // 检查当前焦点元素是否是输入框
        const activeElement = document.activeElement;
        const isInputElement = activeElement && (
            activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA' ||
            activeElement.contentEditable === 'true' ||
            activeElement.closest('.MuiTextField-root') !== null ||
            activeElement.closest('[contenteditable="true"]') !== null
        );

        // 如果当前焦点在输入框上，不处理游戏输入
        if (isInputElement) {
            console.log('RPGEditor_DisableDefaultInput: 跳过游戏输入处理，焦点在输入框上');
            return;
        }

        // 否则使用原始的键盘处理
        _Input_onKeyDown.call(this, event);
    };

    console.log('RPGEditor_DisableDefaultInput: 输入处理已重写');

    // 添加全局键盘事件监听器，用于调试
    if (window.RPGEditorDebug) {
        document.addEventListener('keydown', function (event) {
            const activeElement = document.activeElement;
            console.log('RPGEditor_DisableDefaultInput Debug:', {
                key: event.key,
                keyCode: event.keyCode,
                activeElement: activeElement ? activeElement.tagName : 'none',
                isInputElement: activeElement && (
                    activeElement.tagName === 'INPUT' ||
                    activeElement.tagName === 'TEXTAREA' ||
                    activeElement.contentEditable === 'true' ||
                    activeElement.closest('.MuiTextField-root') !== null
                ),
                defaultPrevented: event.defaultPrevented
            });
        });
    }

})();
