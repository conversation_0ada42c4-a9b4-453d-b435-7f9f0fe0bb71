import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import {
  Paper,
  Typography,
  IconButton,
  Box
} from '@mui/material';
import {
  Close as CloseIcon,
  DragIndicator as DragIcon
} from '@mui/icons-material';
import TypeTreePanel from './TypeTreePanel';

interface TypeTreeWindowProps {
  open: boolean;
  onClose: () => void;
}

const TypeTreeWindow: React.FC<TypeTreeWindowProps> = ({ open, onClose }) => {
  const [position, setPosition] = useState({ x: 100, y: 100 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const dialogRef = useRef<HTMLDivElement>(null);

  // 处理拖拽开始
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    });
  };

  // 处理拖拽移动
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;

    setPosition({
      x: e.clientX - dragStart.x,
      y: e.clientY - dragStart.y
    });
  };

  // 处理拖拽结束
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragStart]);

  if (!open) return null;

  return createPortal(
    <Paper
      ref={dialogRef}
      elevation={8}
      className="type-tree-window"
      data-component="type-tree-window"
      sx={{
        position: 'fixed',
        left: position.x,
        top: position.y,
        width: 400,
        height: 500,
        zIndex: 9999, // 确保在最顶层
        cursor: isDragging ? 'grabbing' : 'default',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}
    >
      {/* 可拖拽的标题栏 */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: 2,
          cursor: 'grab',
          userSelect: 'none',
          borderBottom: 1,
          borderColor: 'divider',
          '&:active': {
            cursor: 'grabbing'
          }
        }}
        onMouseDown={handleMouseDown}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <DragIcon color="action" />
          <Typography variant="h6">类型树</Typography>
        </Box>

        <IconButton
          size="small"
          onClick={onClose}
          sx={{ ml: 1 }}
        >
          <CloseIcon />
        </IconButton>
      </Box>

      {/* 内容区域 */}
      <Box sx={{ flex: 1, overflow: 'hidden' }}>
        <TypeTreePanel />
      </Box>
    </Paper>,
    document.body // 渲染到body中，避免被其他元素遮挡
  );
};

export default TypeTreeWindow;
