import { recordPropertyModification } from './BackendService';

// ==================== Sprite颜色相关方法 ====================

/**
 * Sprite颜色属性类型定义
 */
export type SpriteColorPropertyType =
  | 'hue'          // 色相
  | 'colorTone'    // 色调
  | 'blendColor'   // 混合颜色
  | 'blendMode';   // 混合模式

/**
 * Sprite颜色属性映射
 */
export const SPRITE_COLOR_PROPERTY_MAP: Record<string, SpriteColorPropertyType> = {
  'hue': 'hue',
  'colorTone': 'colorTone',
  'blendColor': 'blendColor',
  'blendMode': 'blendMode'
};

/**
 * 检查是否为Sprite颜色属性
 */
export const isSpriteColorProperty = (propertyName: string): boolean => {
  return propertyName in SPRITE_COLOR_PROPERTY_MAP;
};

/**
 * 获取Sprite颜色属性类型
 */
export const getSpriteColorPropertyType = (propertyName: string): SpriteColorPropertyType | null => {
  return SPRITE_COLOR_PROPERTY_MAP[propertyName] || null;
};

/**
 * 检查对象是否支持Sprite颜色属性
 */
export const supportsSpriteColorProperties = (object: any): boolean => {
  return object && (
    typeof object.setHue === 'function' ||
    typeof object.setColorTone === 'function' ||
    typeof object.setBlendColor === 'function' ||
    object.blendMode !== undefined
  );
};

/**
 * 处理Sprite颜色属性修改（前端对象更新）
 * @param objects 要修改的对象数组
 * @param propertyName 属性名称
 * @param value 新值
 * @returns 是否成功修改
 */
export const updateSpriteColorProperty = (objects: any[], propertyName: string, value: any): boolean => {
  if (!isSpriteColorProperty(propertyName)) {
    console.warn(`${propertyName} 不是Sprite颜色属性`);
    return false;
  }

  try {
    const propertyType = getSpriteColorPropertyType(propertyName);
    console.log(`更新Sprite颜色属性: ${propertyName} (类型: ${propertyType}) = ${value}`);

    // 对每个对象应用修改
    for (const object of objects) {
      if (!object || !supportsSpriteColorProperties(object)) {
        console.warn(`对象不支持Sprite颜色属性: ${object?.constructor?.name}`);
        continue;
      }

      switch (propertyName) {
        case 'hue':
          if (typeof object.setHue === 'function') {
            object.setHue(value);
            console.log(`设置色相: ${value}`);
          } else {
            object.hue = value;
            console.log(`直接设置hue属性: ${value}`);
          }
          break;

        case 'colorTone':
          if (typeof object.setColorTone === 'function') {
            // colorTone 通常是一个数组 [red, green, blue, gray]
            object.setColorTone(value);
            console.log(`设置色调: ${JSON.stringify(value)}`);
          } else {
            object.colorTone = value;
            console.log(`直接设置colorTone属性: ${JSON.stringify(value)}`);
          }
          break;

        case 'blendColor':
          if (typeof object.setBlendColor === 'function') {
            // blendColor 通常是一个数组 [red, green, blue, alpha]
            object.setBlendColor(value);
            console.log(`设置混合颜色: ${JSON.stringify(value)}`);
          } else {
            object.blendColor = value;
            console.log(`直接设置blendColor属性: ${JSON.stringify(value)}`);
          }
          break;

        case 'blendMode':
          object.blendMode = value;
          console.log(`设置混合模式: ${value}`);
          break;

        default:
          console.warn(`未知的Sprite颜色属性: ${propertyName}`);
          return false;
      }
    }

    return true;
  } catch (error) {
    console.error(`更新Sprite颜色属性 ${propertyName} 失败:`, error);
    return false;
  }
};

/**
 * 记录Sprite颜色属性修改到后端
 * @param object 要修改的对象
 * @param propertyName 属性名称
 * @param value 新值
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export const recordSpriteColorPropertyModification = async (
  object: any,
  propertyName: string,
  value: any
): Promise<string> => {
  if (!isSpriteColorProperty(propertyName)) {
    throw new Error(`${propertyName} 不是Sprite颜色属性`);
  }

  if (!supportsSpriteColorProperties(object)) {
    throw new Error(`对象不支持Sprite颜色属性: ${object?.constructor?.name}`);
  }

  const propertyType = getSpriteColorPropertyType(propertyName);
  console.log(`记录Sprite颜色属性修改: ${propertyName} (类型: ${propertyType}) = ${value}`);

  // 使用统一的后端记录方法
  return await recordPropertyModification(object, propertyName, value);
};

/**
 * 获取Sprite颜色属性的当前值
 * @param object 对象
 * @param propertyName 属性名称
 * @returns 当前值
 */
export const getSpriteColorPropertyValue = (object: any, propertyName: string): any => {
  if (!object || !isSpriteColorProperty(propertyName)) {
    return undefined;
  }

  switch (propertyName) {
    case 'hue':
      return object.hue || 0;
    case 'colorTone':
      return object.colorTone || [0, 0, 0, 0];
    case 'blendColor':
      return object.blendColor || [0, 0, 0, 0];
    case 'blendMode':
      return object.blendMode || 0;
    default:
      return undefined;
  }
};

/**
 * 验证Sprite颜色属性值的有效性
 * @param propertyName 属性名称
 * @param value 值
 * @returns 验证结果
 */
export const validateSpriteColorPropertyValue = (propertyName: string, value: any): {
  isValid: boolean;
  errorMessage?: string;
} => {
  switch (propertyName) {
    case 'hue':
      if (typeof value !== 'number') {
        return { isValid: false, errorMessage: '色相值必须是数字' };
      }
      if (value < 0 || value > 360) {
        return { isValid: false, errorMessage: '色相值必须在0-360之间' };
      }
      break;

    case 'colorTone':
    case 'blendColor':
      if (!Array.isArray(value) || value.length !== 4) {
        return { isValid: false, errorMessage: `${propertyName}必须是包含4个元素的数组` };
      }
      if (!value.every(v => typeof v === 'number' && v >= -255 && v <= 255)) {
        return { isValid: false, errorMessage: `${propertyName}的每个值必须是-255到255之间的数字` };
      }
      break;

    case 'blendMode':
      if (typeof value !== 'number' || value < 0) {
        return { isValid: false, errorMessage: '混合模式必须是非负整数' };
      }
      break;

    default:
      return { isValid: false, errorMessage: `未知的Sprite颜色属性: ${propertyName}` };
  }

  return { isValid: true };
};
