import { TreeNode } from './TreeNodeTypes';

/**
 * 扁平化树结构，将树转换为一维数组
 * 只包含展开的节点，便于虚拟化渲染
 * 
 * @param nodes 树节点数组
 * @param expandedNodes 已展开的节点ID集合
 * @param level 当前层级
 * @param isVisible 当前节点是否可见
 * @param parentVisible 父节点是否可见
 * @returns 扁平化后的节点数组
 */
export const flattenTree = (
  nodes: TreeNode[], 
  expandedNodes: Set<string>, 
  level: number = 0, 
  isVisible: boolean = true,
  parentVisible: boolean = true
): Array<TreeNode & { level: number; isVisible: boolean }> => {
  let result: Array<TreeNode & { level: number; isVisible: boolean }> = [];
  
  for (const node of nodes) {
    // 当前节点是否可见（考虑父节点可见性）
    const nodeVisible = isVisible && parentVisible;
    
    // 添加当前节点到结果数组
    result.push({ ...node, level, isVisible: nodeVisible });
    
    // 如果节点有子节点且已展开，则递归处理子节点
    if (node.children.length > 0 && expandedNodes.has(node.id)) {
      const childrenNodes = flattenTree(
        node.children, 
        expandedNodes, 
        level + 1, 
        isVisible, 
        nodeVisible
      );
      result = result.concat(childrenNodes);
    }
  }
  
  return result;
};

/**
 * 计算可见节点的数量
 * 用于优化渲染性能，只渲染可见区域内的节点
 * 
 * @param flattenedNodes 扁平化后的节点数组
 * @returns 可见节点的数量
 */
export const countVisibleNodes = (
  flattenedNodes: Array<TreeNode & { level: number; isVisible: boolean }>
): number => {
  return flattenedNodes.filter(node => node.isVisible).length;
};

/**
 * 获取指定范围内的可见节点
 * 用于虚拟列表渲染
 * 
 * @param flattenedNodes 扁平化后的节点数组
 * @param startIndex 起始索引
 * @param endIndex 结束索引
 * @returns 指定范围内的可见节点
 */
export const getVisibleNodes = (
  flattenedNodes: Array<TreeNode & { level: number; isVisible: boolean }>,
  startIndex: number,
  endIndex: number
): Array<TreeNode & { level: number; isVisible: boolean }> => {
  return flattenedNodes
    .filter(node => node.isVisible)
    .slice(startIndex, endIndex);
};
