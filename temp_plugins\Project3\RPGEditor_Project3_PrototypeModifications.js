/*:
 * @target MZ
 * @plugindesc RPG Editor - 自动生成的原型链修改插件
 * <AUTHOR> Editor
 * @help
 * 此插件由RPG Editor自动生成，包含对游戏对象原型链的修改。
 * 修改仅应用于满足特定条件的对象，而不是所有同类型的对象。
 */

(function() {
    'use strict';
    // 保存原始的drawText方法
    // 添加调试标志
    const DEBUG = true;


    // 调试日志函数
    function log(...args) {
        if (DEBUG) {
            console.log('[RPGEditor]', ...args);
        }
    }

    // 根据场景路径查找对象
    function findObjectByScenePath(scene, path) {
        if (DEBUG) log('查找对象，场景:', scene.constructor.name, '路径:', path);
        if (!path || path.length < 1) return null;

        // 检查场景名称是否匹配
        const sceneName = path[0];
        if (scene.constructor.name !== sceneName) {
            if (DEBUG) log('场景类型不匹配:', scene.constructor.name, '!=', sceneName);
            return null;
        }

        // 如果路径只有一个元素（场景名称），则返回场景本身
        if (path.length === 1) {
            return scene;
        }

        // 递归查找嵌套对象
        let currentObject = scene;

        // 从第二个元素开始遍历路径
        for (let i = 1; i < path.length; i++) {
            const index = parseInt(path[i]);

            // 检查索引是否有效
            if (isNaN(index) || !currentObject.children || index >= currentObject.children.length) {
                if (DEBUG) log('索引无效或超出范围:', index, '子对象数量:', currentObject.children ? currentObject.children.length : 0, '在路径位置', i);
                return null;
            }

            // 获取下一级对象
            currentObject = currentObject.children[index];

            // 如果对象为空，则中断查找
            if (!currentObject) {
                if (DEBUG) log('在索引', index, '处找不到对象，在路径位置', i);
                return null;
            }

            if (DEBUG) log('找到中间对象:', currentObject.constructor.name, '在索引', index, '在路径位置', i);
        }

        if (DEBUG) log('最终找到对象:', currentObject ? currentObject.constructor.name : 'null', '完整路径:', path.join('/'));
        return currentObject;
    }

    // 原型链修改
    // 修改 Scene_Title 场景
    const originalScene_TitleStart = Scene_Title.prototype.start;
    Scene_Title.prototype.start = function() {
        // 调用原始方法
        originalScene_TitleStart.apply(this, arguments);
        
        if (DEBUG) log('Scene_Title.start 被调用');
        
        // 修改路径为 Scene_Title/0 的 Sprite 对象
        const scenePath_sprite_Scene_Title_0_65213 = ["Scene_Title","0"];
        const targetObject_sprite_Scene_Title_0_65213 = findObjectByScenePath(this, scenePath_sprite_Scene_Title_0_65213);
        if (DEBUG) log('路径为 Scene_Title/0 的Sprite 对象:', targetObject_sprite_Scene_Title_0_65213 ? targetObject_sprite_Scene_Title_0_65213.constructor.name : 'null');
        if (targetObject_sprite_Scene_Title_0_65213) {
            // 应用属性修改
            // 更新 PIXI.js Sprite 纹理
            // 根据 window.mzl 选择合适的路径
            let fullPath = 'img/sv_actors/Actor1_8.png';
            // 如果 window.mzl 存在并且为 true，使用带前缀的路径
            if (window.mzl) {
                fullPath = '../projects/Project3/' + fullPath;
            }
            const newTexture = PIXI.Texture.from(fullPath);
            targetObject_sprite_Scene_Title_0_65213.texture = newTexture;
            if (DEBUG) log('设置 Sprite 纹理 = ', fullPath);
        }

    };

})();
