import { DisplayObject } from 'pixi.js';

/**
 * 节点类型枚举
 */
export enum NodeType {
  SCENE = 'scene',
  SPRITE = 'sprite',
  LABEL = 'label',
  CONTAINER = 'container',
  WINDOW = 'window',
  BUTTON = 'button',
  LAYOUT_CONTAINER = 'layout_container',
  CUSTOM = 'custom',
  OTHER = 'other'
}

/**
 * 表示对象树中的节点
 */
export interface TreeNode {
  id: string;
  name: string;
  object: DisplayObject;
  children: TreeNode[];
  visible: boolean;
  hasEvents: boolean;
  type?: NodeType; // 可选，节点类型
  parentId?: string; // 可选，父节点ID
}

/**
 * 表示场景节点
 */
export interface SceneNode extends TreeNode {
  type: NodeType.SCENE;
  sceneName: string;
}

/**
 * 表示右键菜单的位置和状态
 */
export interface ContextMenuState {
  mouseX: number;
  mouseY: number;
  nodeId: string | null;
}

/**
 * 表示对象树的展开状态
 */
export type ExpandedState = Record<string, boolean>;
