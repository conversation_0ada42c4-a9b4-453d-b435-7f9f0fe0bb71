use once_cell::sync::Lazy;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Mutex;

/// 原型修改数据结构
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct Modification {
    pub class_name: String,
    pub scene_path: Vec<String>,
    pub properties: HashMap<String, serde_json::Value>,
    pub modification_type: ModificationType,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub enum ModificationType {
    ObjectInstance, // 修改具体对象实例
    ClassPrototype, // 修改类型原型
}

/// 全局缓存，用于存储已解析的修改
pub static MODIFICATIONS_CACHE: Lazy<Mutex<Option<Vec<Modification>>>> =
    Lazy::new(|| Mutex::new(None));
