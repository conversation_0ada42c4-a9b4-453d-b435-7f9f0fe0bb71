//=============================================================================
// RPG Maker MZ - Login System
//=============================================================================

/*:
 * @target MZ
 * @plugindesc 登录系统
 * <AUTHOR> AI
 *
 * @param fontSize
 * @text 字体大小
 * @type number
 * @min 10
 * @max 72
 * @default 20
 * @desc 界面文字的字体大小
 *
 * @param fontColor
 * @text 字体颜色
 * @type string
 * @default #ffffff
 * @desc 界面文字的颜色（CSS颜色格式）
 *
 * @param backgroundColor
 * @text 背景颜色
 * @type string
 * @default rgba(0, 0, 0, 0.7)
 * @desc 窗口背景的颜色（CSS颜色格式）
 *
 * @param backgroundImage
 * @text 背景图片
 * @type file
 * @dir img/titles1
 * @default
 * @desc 登录界面的背景图片
 *
 * @param borderSize
 * @text 边框大小
 * @type number
 * @min 0
 * @max 10
 * @default 2
 * @desc 窗口边框的大小（像素）
 *
 * @param borderColor
 * @text 边框颜色
 * @type string
 * @default #ffffff
 * @desc 窗口边框的颜色（CSS颜色格式）
 *
 * @param bgm
 * @text 背景音乐
 * @type file
 * @dir audio/bgm
 * @default
 * @desc 登录界面的背景音乐
 *
 * @help LoginSystem.js
 *
 * 这个插件提供了一个完整的登录系统：
 * 1. 替换原有的标题界面
 * 2. 提供登录、注册和密码找回功能
 * 3. 支持邮箱验证
 * 4. 自定义界面样式
 */

(() => {
  const pluginName = "LoginSystem";
  const parameters = PluginManager.parameters(pluginName);

  // // 确保NetworkManager已初始化
  // if (typeof NetworkManager !== "undefined") {
  //   NetworkManager.initialize();
  // }

  const fontSize = Number(parameters.fontSize || 20);
  const fontColor = String(parameters.fontColor || "#ffffff");
  const backgroundColor = String(
    parameters.backgroundColor || "rgba(0, 0, 0, 0.7)"
  );
  const backgroundImage = String(parameters.backgroundImage || "");
  const borderSize = Number(parameters.borderSize || 2);
  const borderColor = String(parameters.borderColor || "#ffffff");
  const bgm = String(parameters.bgm || "");

  //-----------------------------------------------------------------------------
  // Scene_Login
  //
  // 登录场景

  function Scene_Login() {
    this.initialize(...arguments);
  }

  Scene_Login.prototype = Object.create(Scene_Base.prototype);
  Scene_Login.prototype.constructor = Scene_Login;

  Scene_Login.prototype.initialize = function () {
    Scene_Base.prototype.initialize.call(this);
    this._bgmPlayed = false;
  };

  Scene_Login.prototype.update = function () {
    Scene_Base.prototype.update.call(this);
    if (!this._bgmPlayed && bgm) {
      AudioManager.playBgm({ name: bgm, volume: 90, pitch: 100 });
      this._bgmPlayed = true;
    }
  };

  Scene_Login.prototype.create = function () {
    Scene_Base.prototype.create.call(this);
    this.createBackground();
    this.createWindowLayer();
    this.createLoginWindow();
  };

  Scene_Login.prototype.createBackground = function () {
    this._backgroundSprite = new Sprite();
    if (backgroundImage) {
      this._backgroundSprite.bitmap = ImageManager.loadTitle1(backgroundImage);
    } else {
      this._backgroundSprite.bitmap = SceneManager.backgroundBitmap();
    }
    this._backgroundSprite.x = (Graphics.width - Graphics.boxWidth) / 2;
    this._backgroundSprite.y = (Graphics.height - Graphics.boxHeight) / 2;
    this.addChild(this._backgroundSprite);
  };

  Scene_Login.prototype.createLoginWindow = function () {
    const width = 400;
    const height = 400;
    const x = (Graphics.boxWidth - width) / 2;
    const y = (Graphics.boxHeight - height) / 2;
    this._loginWindow = new Window_Login(new Rectangle(x, y, width, height));
    this.addWindow(this._loginWindow);
  };

  //-----------------------------------------------------------------------------
  // Window_Login
  //
  // 登录窗口

  function Window_Login() {
    this.initialize(...arguments);
  }

  Window_Login.prototype = Object.create(Window_Selectable.prototype);
  Window_Login.prototype.constructor = Window_Login;

  Window_Login.prototype.initialize = function (rect) {
    Window_Selectable.prototype.initialize.call(this, rect);
    this._state = "login"; // login, register, recover
    this._borderSize = borderSize;
    this._borderColor = borderColor;
    this.createInputFields();
    this.createButtons();
    this.refresh();
    this.select(0);
    this.activate();
  };

  Window_Login.prototype._updateFilterArea = function () {
    Window_Selectable.prototype._updateFilterArea.call(this);
    if (
      this._borderSize > 0 &&
      this._windowFrameSprite &&
      this._windowFrameSprite.bitmap
    ) {
      const ctx = this._windowFrameSprite.bitmap.context;
      ctx.strokeStyle = this._borderColor;
      ctx.lineWidth = this._borderSize;
      ctx.strokeRect(
        this._borderSize / 2,
        this._borderSize / 2,
        this.width - this._borderSize,
        this.height - this._borderSize
      );
    }
  };

  Window_Login.prototype.createInputFields = function () {
    const padding = this.padding;
    const width = this.innerWidth - padding * 2;
    const height = this.lineHeight();
    const startY = padding + 80;
    const spacing = 60;

    // 用户名输入框
    this._usernameInput = new Window_LoginInput(
      new Rectangle(padding, startY, width, height),
      "用户名"
    );
    this.addChild(this._usernameInput);

    // 密码输入框
    this._passwordInput = new Window_LoginInput(
      new Rectangle(padding, startY + spacing, width, height),
      "密码",
      true
    );
    this.addChild(this._passwordInput);

    // 邮箱输入框（注册和找回密码时使用）
    this._emailInput = new Window_LoginInput(
      new Rectangle(padding, startY + spacing * 2, width, height),
      "邮箱"
    );
    this._emailInput.hide();
    this.addChild(this._emailInput);
  };

  Window_Login.prototype.createButtons = function () {
    const padding = this.padding;
    const buttonSpacing = 20;
    const y = this.innerHeight - padding - 100;
    const buttonWidth = (this.innerWidth - padding * 2 - buttonSpacing) / 2;
    const buttonHeight = 45;

    // 登录/注册/确认按钮
    this._confirmButton = new Window_LoginButton(
      new Rectangle(padding, y, buttonWidth, buttonHeight),
      "登录",
      () => this.onConfirm()
    );
    this.addChild(this._confirmButton);

    // 切换按钮
    this._switchButton = new Window_LoginButton(
      new Rectangle(
        padding + buttonWidth + buttonSpacing,
        y,
        buttonWidth,
        buttonHeight
      ),
      "注册",
      () => this.switchMode()
    );
    this.addChild(this._switchButton);

    // 找回密码按钮
    this._recoverButton = new Window_LoginButton(
      new Rectangle(
        padding,
        y + buttonHeight + buttonSpacing,
        this.innerWidth - padding * 2,
        buttonHeight
      ),
      "找回密码",
      () => this.switchToRecover()
    );
    this.addChild(this._recoverButton);
  };

  Window_Login.prototype.switchMode = function () {
    if (this._state === "login") {
      this._state = "register";
      this._emailInput.show();
      this._confirmButton.setText("注册");
      this._switchButton.setText("返回登录");
    } else {
      this._state = "login";
      this._emailInput.hide();
      this._confirmButton.setText("登录");
      this._switchButton.setText("注册");
    }
    this.refresh();
  };

  Window_Login.prototype.switchToRecover = function () {
    if (this._state === "recover") {
      this._state = "login";
      this._emailInput.hide();
      this._confirmButton.setText("登录");
      this._switchButton.setText("注册");
      this._recoverButton.setText("找回密码");
    } else {
      this._state = "recover";
      this._emailInput.show();
      this._confirmButton.setText("重置密码");
      this._switchButton.setText("返回登录");
      this._recoverButton.setText("返回登录");
    }
    this.refresh();
  };

  Window_Login.prototype.refresh = function () {
    this.contents.clear();
    this.drawTitle();
  };

  Window_Login.prototype.maxItems = function () {
    return 3; // 用户名、密码和邮箱输入框
  };

  Window_Login.prototype.itemHeight = function () {
    return this.lineHeight();
  };

  Window_Login.prototype.drawTitle = function () {
    const title =
      this._state === "login"
        ? "登录"
        : this._state === "register"
        ? "注册"
        : "找回密码";
    this.drawText(title, 0, 0, this.innerWidth, "center");
  };

  Window_Login.prototype.onConfirm = function () {
    const username = this._usernameInput.value();
    const password = this._passwordInput.value();
    const email = this._emailInput.value();

    switch (this._state) {
      case "login":
        this.processLogin(username, password);
        break;
      case "register":
        this.processRegister(username, password, email);
        break;
      case "recover":
        this.processRecover(email);
        break;
    }
  };

  Window_Login.prototype.processLogin = function (username, password) {
    if (username && password) {
      this.loginSuccess();
    } else {
      this.showError("请输入用户名和密码");
    }
  };

  Window_Login.prototype.processRegister = function (
    username,
    password,
    email
  ) {
    // 这里添加注册逻辑
    if (username && password && email) {
      NetworkManager.sendMessage(
        1,
        {
          userId: username,
          pwd: password,
          email: email,
        },
        (data) => {
          // 这个回调函数只会执行一次
          console.log("收到服务器响应:", data);
        }
      );
      // 模拟注册成功
      this.showMessage("注册成功，请登录");
      this.switchMode();
    } else {
      this.showError("请填写所有必填信息");
    }
  };

  Window_Login.prototype.processRecover = function (email) {
    // 这里添加密码找回逻辑
    if (email) {
      // 模拟发送重置邮件
      this.showMessage("重置密码邮件已发送");
      this.switchToRecover();
    } else {
      this.showError("请输入邮箱地址");
    }
  };

  Window_Login.prototype.loginSuccess = function () {
    SceneManager.goto(Scene_Map);
  };

  Window_Login.prototype.showError = function (message) {
    MessageSystem.show({
      text: message,
      type: "error",
      duration: 180,
    });
  };

  Window_Login.prototype.showMessage = function (message) {
    MessageSystem.show({
      text: message,
      type: "success",
      duration: 180,
    });
  };

  //-----------------------------------------------------------------------------
  // Window_LoginInput
  //
  // 登录输入框窗口

  function Window_LoginInput() {
    this.initialize(...arguments);
  }

  Window_LoginInput.prototype = Object.create(Window_Base.prototype);
  Window_LoginInput.prototype.constructor = Window_LoginInput;

  Window_LoginInput.prototype.initialize = function (
    rect,
    placeholder,
    isPassword
  ) {
    Window_Base.prototype.initialize.call(this, rect);
    this._placeholder = placeholder;
    this._isPassword = isPassword;
    this._value = "";
    this._active = false;
    this._maxLength = 20;
    this.createDOMInput();
    this.refresh();
  };

  Window_LoginInput.prototype.destroy = function () {
    if (this._domInput) {
      document.body.removeChild(this._domInput);
      this._domInput = null;
    }
    Window_Base.prototype.destroy.call(this);
  };

  Window_LoginInput.prototype.update = function () {
    Window_Base.prototype.update.call(this);
    this.processTouch();
    if (this._active) {
      this.processInput();
    }
  };

  Window_LoginInput.prototype.processTouch = function () {
    if (TouchInput.isTriggered() && this.isTouchedInsideFrame()) {
      this.activate();
      SoundManager.playOk();
    } else if (TouchInput.isTriggered() && !this.isTouchedInsideFrame()) {
      this.deactivate();
    }
  };

  Window_LoginInput.prototype.isTouchedInsideFrame = function () {
    const touchPos = new Point(TouchInput.x, TouchInput.y);
    const localPos = this.worldTransform.applyInverse(touchPos);
    return this.innerRect.contains(localPos.x, localPos.y);
  };

  Window_LoginInput.prototype.activate = function () {
    this._active = true;
    this._domInput.focus();
    this.refresh();
  };

  Window_LoginInput.prototype.deactivate = function () {
    this._active = false;
    this._domInput.blur();
    this.refresh();
  };

  Window_LoginInput.prototype.createDOMInput = function () {
    this._domInput = document.createElement("input");
    this._domInput.type = this._isPassword ? "password" : "text";
    this._domInput.style.position = "absolute";
    this._domInput.style.opacity = "0";
    this._domInput.style.zIndex = "-1";
    this._domInput.maxLength = this._maxLength;
    document.body.appendChild(this._domInput);

    this._domInput.addEventListener("input", (e) => {
      if (this._active) {
        this._value = e.target.value;
        this.refresh();
      }
    });

    this._domInput.addEventListener("keydown", (e) => {
      if (this._active && e.key === "Backspace") {
        const cursorPosition = this._domInput.selectionStart;
        if (cursorPosition > 0) {
          const newValue =
            this._value.slice(0, cursorPosition - 1) +
            this._value.slice(cursorPosition);
          this._value = newValue;
          this._domInput.value = newValue;
          this.refresh();
        }
      }
    });
  };

  Window_LoginInput.prototype.processInput = function () {
    if (!this._active) return;
    this._domInput.focus();

    // 同步DOM输入框的值到游戏内输入框
    if (this._domInput.value !== this._value) {
      this._value = this._domInput.value;
      this.refresh();
    }
  };

  Window_LoginInput.prototype.value = function () {
    return this._value;
  };

  Window_LoginInput.prototype.refresh = function () {
    this.contents.clear();
    this.resetFontSettings();
    this.contents.fontSize = fontSize - 3;
    const text = this._value || (!this._active ? this._placeholder : "");
    const displayText = this._isPassword
      ? "*".repeat(this._value.length)
      : text;
    const y = (this.innerHeight - this.lineHeight()) / 2;
    this.drawText(displayText, 0, y, this.innerWidth);
    if (this._active) {
      const textWidth = this.textWidth(displayText);
      const cursorX = textWidth + 4;
      this.drawText("|", cursorX, y, this.innerWidth - cursorX);
    }
  };

  //-----------------------------------------------------------------------------
  // Window_LoginButton
  //
  // 登录按钮窗口

  function Window_LoginButton() {
    this.initialize(...arguments);
  }

  Window_LoginButton.prototype = Object.create(Window_Base.prototype);
  Window_LoginButton.prototype.constructor = Window_LoginButton;

  Window_LoginButton.prototype.initialize = function (rect, text, callback) {
    Window_Base.prototype.initialize.call(this, rect);
    this._text = text;
    this._callback = callback;
    this._pressed = false;
    this._hovered = false;
    this.refresh();
  };

  Window_LoginButton.prototype.setText = function (text) {
    this._text = text;
    this.refresh();
  };

  Window_LoginButton.prototype.update = function () {
    Window_Base.prototype.update.call(this);
    this.processTouch();
  };

  Window_LoginButton.prototype.processTouch = function () {
    if (this.isEnabled()) {
      if (TouchInput.isTriggered() && this.isTouchedInsideFrame()) {
        this.onPress();
      }
      if (TouchInput.isReleased() && this._pressed) {
        this.onClick();
        this.onRelease();
      }
    }
  };

  Window_LoginButton.prototype.isTouchedInsideFrame = function () {
    const touchPos = new Point(TouchInput.x, TouchInput.y);
    const localPos = this.worldTransform.applyInverse(touchPos);
    return this.innerRect.contains(localPos.x, localPos.y);
  };

  Window_LoginButton.prototype.isEnabled = function () {
    return true;
  };

  Window_LoginButton.prototype.onPress = function () {
    this._pressed = true;
    this.opacity = 150;
  };

  Window_LoginButton.prototype.onRelease = function () {
    this._pressed = false;
    this.opacity = 255;
  };

  Window_LoginButton.prototype.onClick = function () {
    if (this._callback) {
      this._callback();
      SoundManager.playOk();
    }
  };

  Window_LoginButton.prototype.refresh = function () {
    this.contents.clear();
    this.resetFontSettings();
    this.contents.fontSize = fontSize;
    const y = (this.innerHeight - this.lineHeight()) / 2;
    this.drawText(this._text, 0, y, this.innerWidth, "center");
  };

  //-----------------------------------------------------------------------------
  // 替换标题场景
  const _Scene_Boot_startNormalGame = Scene_Boot.prototype.startNormalGame;
  Scene_Boot.prototype.startNormalGame = function () {
    this.checkPlayerLocation();
    DataManager.setupNewGame();
    SceneManager.goto(Scene_Login);
    Window_TitleCommand.initCommandPosition();
  };
})();
