import React, { useState, useEffect, memo } from "react";
import {
  Box, Typography, Tabs, Tab, Paper
} from "@mui/material";
import useProjectStore from "../../../store/Store";
import TextLabel from "../../ui/TextLabel";

interface TextInfoGroupProps {
  selectedObject: any;
  forceUpdate: () => void;
}

// 文本元素接口（基于bitmap.elements中的text类型元素）
interface TextElement {
  type: 'text';
  text: string;
  align: string;
  lineHeight: number;
  maxWidth: number;
  x: number;
  y: number;
  bounds?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

const TextInfoGroup: React.FC<TextInfoGroupProps> = memo(({ selectedObject, forceUpdate }) => {
  // 获取Store方法
  const setSelectedObjectsProperty = useProjectStore(state => state.setSelectedObjectsProperty);

  // 当前选中的文本项索引
  const [selectedTextIndex, setSelectedTextIndex] = useState<number>(0);
  // 文本元素数组
  const [textElements, setTextElements] = useState<TextElement[]>([]);

  // 当选中对象变化时，更新文本元素数组
  useEffect(() => {
    if (selectedObject && selectedObject._bitmap && selectedObject._bitmap.elements) {
      try {
        const bitmap = selectedObject._bitmap;

        // 从elements数组中筛选出type为'text'的元素
        const textElements = bitmap.elements.filter((element: any) => element.type === 'text');

        console.log("发现文本元素:", textElements);
        setTextElements(textElements);

        // 默认选中第一个文本项
        setSelectedTextIndex(0);
      } catch (error) {
        console.error("获取文本元素时出错:", error);
        setTextElements([]);
      }
    } else {
      setTextElements([]);
    }
  }, [selectedObject]);

  // 更新文本元素的text属性
  const updateTextElement = (index: number, newText: string) => {
    if (index < 0 || index >= textElements.length) return;

    try {
      if (selectedObject && selectedObject._bitmap && selectedObject._bitmap.elements) {
        // 找到对应的元素在原始elements数组中的索引
        const elementIndex = selectedObject._bitmap.elements.findIndex((element: any, i: number) => {
          return element.type === 'text' &&
            selectedObject._bitmap.elements.filter((e: any, j: number) => e.type === 'text' && j <= i).length === index + 1;
        });

        if (elementIndex >= 0) {
          // 更新元素的text属性
          selectedObject._bitmap.elements[elementIndex].text = newText;

          // 使用Store中的统一方法更新属性
          setSelectedObjectsProperty(`_bitmap.elements[${elementIndex}].text`, newText);

          // 重新绘制
          selectedObject._bitmap.redrawing();

          // 更新本地状态
          const newTextElements = [...textElements];
          newTextElements[index] = { ...newTextElements[index], text: newText };
          setTextElements(newTextElements);

          // 强制重新渲染
          forceUpdate();
        }
      }
    } catch (error) {
      console.error(`更新文本元素 ${index} 时出错:`, error);
    }
  };



  // 渲染文本项选择器
  const renderTextSelector = () => {
    if (textElements.length === 0) return null;

    return (
      <Box sx={{ mb: 1, borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={selectedTextIndex}
          onChange={(_, newValue) => setSelectedTextIndex(newValue)}
          variant="scrollable"
          scrollButtons="auto"
        >
          {textElements.map((element, index) => (
            <Tab
              key={index}
              label={element.text ? (element.text.length > 10 ? `${element.text.substring(0, 10)}...` : element.text) : `文本 ${index + 1}`}
              sx={{
                minWidth: 'auto',
                px: 2,
                fontSize: '0.75rem'
              }}
            />
          ))}
        </Tabs>
      </Box>
    );
  };

  // 渲染文本编辑表单
  const renderTextForm = () => {
    if (selectedTextIndex < 0 || selectedTextIndex >= textElements.length) return null;

    const textElement = textElements[selectedTextIndex];

    return (
      <Box sx={{ p: 1 }}>
        {/* 文本内容 */}
        <TextLabel
          label="文本内容"
          value={textElement.text}
          multiline={true}
          rows={2}
          onChange={(newText) => updateTextElement(selectedTextIndex, newText)}
        />
      </Box>
    );
  };



  return (
    <Box sx={{ mt: 1 }}>
      {/* 文本选择器 */}
      {textElements.length > 0 ? (
        <Paper variant="outlined" sx={{ mb: 1 }}>
          {renderTextSelector()}
          {renderTextForm()}
        </Paper>
      ) : (
        <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            没有找到文本元素
          </Typography>
        </Paper>
      )}
    </Box>
  );
}, (prevProps, nextProps) => {
  // 只有当selectedObject发生变化时才重新渲染
  return prevProps.selectedObject === nextProps.selectedObject;
});

export default TextInfoGroup;


