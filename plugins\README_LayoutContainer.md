# LayoutContainer 布局容器插件

## 简介

LayoutContainer 是一个为 RPG Maker MZ 开发的布局容器插件，它提供了一种简单而强大的方式来管理游戏中UI元素的布局。通过继承自 PIXI.Container 类，LayoutContainer 实现了多种布局方法，使UI元素的排列更加灵活和简单。

## 特性

- **多种布局类型**：支持列布局、行布局、网格布局和多种对齐方式
- **灵活的对齐选项**：支持主轴和交叉轴的多种对齐方式
- **自动布局更新**：当容器尺寸或子元素变化时自动更新布局
- **百分比支持**：支持使用百分比设置间距和内边距
- **简单易用的API**：提供简洁明了的方法来应用和更新布局

## 安装

1. 将 `LayoutContainer.js` 文件放入你的游戏项目的 `plugins` 文件夹中
2. 在 RPG Maker MZ 的插件管理器中启用 LayoutContainer 插件

## 使用方法

### 基本用法

```javascript
// 创建一个LayoutContainer实例
const container = new LayoutContainer();
container.width = 300;
container.height = 200;

// 添加子元素
const sprite1 = new Sprite(bitmap1);
const sprite2 = new Sprite(bitmap2);
container.addChild(sprite1, sprite2);

// 应用布局
container.applyLayout('row', {
    mainAxisAlignment: 'space-between',
    crossAxisAlignment: 'center',
    padding: 10
});

// 添加到场景
this.addChild(container);
```

### 布局类型

LayoutContainer 支持以下布局类型：

- `column`：垂直排列子元素
- `row`：水平排列子元素
- `grid`：网格排列子元素
- `topLeft`：对齐到容器的左上角
- `topRight`：对齐到容器的右上角
- `bottomLeft`：对齐到容器的左下角
- `bottomRight`：对齐到容器的右下角
- `topCenter`：对齐到容器的顶部中心
- `bottomCenter`：对齐到容器的底部中心
- `leftCenter`：对齐到容器的左侧中心
- `rightCenter`：对齐到容器的右侧中心
- `center`：居中对齐

### 布局选项

所有布局类型都支持以下选项：

- `padding`：内边距，可以是数字、百分比字符串或 `{top, right, bottom, left}` 对象
- `spacing`：元素间距，可以是数字或百分比字符串

行布局和列布局还支持以下选项：

- `mainAxisAlignment`：主轴对齐方式，可选值：
  - `start`：起始对齐（默认）
  - `center`：居中对齐
  - `end`：末尾对齐
  - `space-between`：两端对齐，元素之间间距相等
  - `space-around`：环绕对齐，元素两侧间距相等
- `crossAxisAlignment`：交叉轴对齐方式，可选值：
  - `start`：起始对齐（默认）
  - `center`：居中对齐
  - `end`：末尾对齐
  - `stretch`：拉伸填充

网格布局还支持以下选项：

- `columns`：列数，默认为2

### 示例

#### 列布局（垂直排列）

```javascript
container.applyLayout('column', {
    mainAxisAlignment: 'space-between',
    crossAxisAlignment: 'center',
    padding: 10,
    spacing: 5
});
```

#### 行布局（水平排列）

```javascript
container.applyLayout('row', {
    mainAxisAlignment: 'center',
    crossAxisAlignment: 'end',
    padding: {top: 10, right: 20, bottom: 10, left: 20},
    spacing: '5%'
});
```

#### 网格布局

```javascript
container.applyLayout('grid', {
    columns: 3,
    mainAxisAlignment: 'center',
    crossAxisAlignment: 'center',
    padding: 10,
    spacing: 5
});
```

#### 居中对齐

```javascript
container.applyLayout('center', {
    padding: 10
});
```

## 高级用法

### 自动更新布局

当添加或移除子元素时，可以使用以下方法自动更新布局：

```javascript
// 添加子元素并更新布局
container.addChildAndUpdateLayout(sprite);

// 移除子元素并更新布局
container.removeChildAndUpdateLayout(sprite);

// 手动更新布局
container.updateLayout();
```

### 百分比值

间距和内边距可以使用百分比值：

```javascript
container.applyLayout('row', {
    padding: '5%',        // 相对于容器尺寸的5%
    spacing: '10%'        // 相对于容器宽度的10%
});

// 或者使用对象形式
container.applyLayout('column', {
    padding: {
        top: '5%',        // 相对于容器高度的5%
        right: '10%',     // 相对于容器宽度的10%
        bottom: '5%',     // 相对于容器高度的5%
        left: '10%'       // 相对于容器宽度的10%
    }
});
```

## 示例插件

查看 `LayoutContainerExample.js` 插件，了解更多使用示例。这个示例插件在标题界面添加了多种布局的UI元素，展示了LayoutContainer的各种功能。

## 注意事项

- 容器的宽度和高度必须设置，否则布局可能不会按预期工作
- 对于复杂的UI，建议使用嵌套的LayoutContainer来实现
- 如果子元素的尺寸或位置在布局后被手动修改，需要调用`updateLayout()`方法重新应用布局

## 许可证

MIT License
