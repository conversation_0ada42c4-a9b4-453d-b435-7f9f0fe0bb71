use crate::save::api::router::OperationRequest;
use crate::save::types::{Modification, ModificationType};
use crate::utils::logging;
use std::collections::HashMap;
use tauri::AppHandle;

/// 处理对象创建操作
/// 在场景初始化中创建对象
/// 使用场景路径定位目标对象
pub fn handle_object_create(
    app_handle: AppHandle,
    request: OperationRequest,
) -> Result<String, String> {
    logging::log_info(
        "ObjectHandlers",
        &format!(
            "处理对象创建操作 - 路径: {:?}, 字段: {}, 类名: {}",
            request.target_path, request.field_name, request.class_name
        ),
    );

    // 验证场景路径
    validate_scene_path(&request.target_path)?;

    // 直接实现对象修改逻辑
    handle_object_modification(
        app_handle,
        request.target_path,
        request.class_name,
        request.field_name,
        request.field_value,
    )
}

/// 处理对象删除操作
/// 在场景初始化中删除对象
/// 使用场景路径定位目标对象
pub fn handle_object_delete(
    app_handle: AppHandle,
    request: OperationRequest,
) -> Result<String, String> {
    logging::log_info(
        "ObjectHandlers",
        &format!(
            "处理对象删除操作 - 路径: {:?}, 字段: {}, 类名: {}",
            request.target_path, request.field_name, request.class_name
        ),
    );

    // 验证场景路径
    validate_scene_path(&request.target_path)?;

    // 直接实现对象修改逻辑
    handle_object_modification(
        app_handle,
        request.target_path,
        request.class_name,
        request.field_name,
        request.field_value,
    )
}

/// 处理对象修改操作
/// 在场景初始化中修改对象属性
/// 根据路径和字段操作具体的值
pub fn handle_object_modify(
    app_handle: AppHandle,
    request: OperationRequest,
) -> Result<String, String> {
    logging::log_info(
        "ObjectHandlers",
        &format!(
            "处理对象修改操作 - 路径: {:?}, 字段: {}, 类名: {}",
            request.target_path, request.field_name, request.class_name
        ),
    );

    // 验证场景路径
    validate_scene_path(&request.target_path)?;

    // 根据路径和字段操作具体的值
    if request.target_path.len() == 1 {
        // 直接在场景上操作
        let scene_name = &request.target_path[0];
        logging::log_info(
            "ObjectHandlers",
            &format!("直接修改场景 {} 的属性", scene_name),
        );
    } else {
        // 在场景的嵌套对象上操作
        let scene_name = &request.target_path[0];
        logging::log_info(
            "ObjectHandlers",
            &format!(
                "修改场景 {} 中路径 {:?} 的属性",
                scene_name, request.target_path
            ),
        );
    }

    // 直接实现对象修改逻辑
    handle_object_modification(
        app_handle,
        request.target_path,
        request.class_name,
        request.field_name,
        request.field_value,
    )
}

/// 验证场景路径的有效性
fn validate_scene_path(target_path: &[String]) -> Result<(), String> {
    // 验证路径不为空
    if target_path.is_empty() {
        return Err("对象操作缺少场景路径".to_string());
    }

    // 验证场景名称格式
    let scene_name = &target_path[0];
    if !scene_name.starts_with("Scene_") {
        return Err(format!(
            "无效的场景名称: {}，对象操作需要有效的场景路径",
            scene_name
        ));
    }

    Ok(())
}

/// 统一的对象修改处理函数
/// 直接实现修改逻辑，不依赖旧的API
fn handle_object_modification(
    app_handle: AppHandle,
    object_path: Vec<String>,
    class_name: String,
    field_name: String,
    value: serde_json::Value,
) -> Result<String, String> {
    logging::log_info(
        "ObjectHandlers",
        &format!(
            "统一处理对象修改 - 路径: {:?}, 类名: {}, 字段: {}, 值: {}",
            object_path, class_name, field_name, value
        ),
    );

    // 获取当前修改列表
    let mut modifications = get_current_modifications(&app_handle)?;

    // 查找是否已存在相同路径的修改
    let mut found = false;
    for modification in &mut modifications {
        if modification.scene_path == object_path {
            // 更新现有修改的属性
            modification
                .properties
                .insert(field_name.clone(), value.clone());
            found = true;
            break;
        }
    }

    // 如果没有找到相同路径的修改，创建新的修改
    if !found {
        let mut properties = HashMap::new();
        properties.insert(field_name.clone(), value.clone());

        modifications.push(Modification {
            class_name,
            scene_path: object_path,
            properties,
            modification_type: ModificationType::ObjectInstance,
        });
    }

    // 更新缓存
    update_cached_modifications(modifications)?;

    Ok(format!("属性 {} 已记录", field_name))
}

/// 获取当前修改列表
fn get_current_modifications(_app_handle: &AppHandle) -> Result<Vec<Modification>, String> {
    // 从缓存中获取修改列表
    use crate::utils::cache;
    Ok(cache::get_cached_modifications().unwrap_or_default())
}

/// 更新缓存的修改列表
fn update_cached_modifications(modifications: Vec<Modification>) -> Result<(), String> {
    use crate::utils::cache;
    cache::update_cached_modifications(modifications)
}
