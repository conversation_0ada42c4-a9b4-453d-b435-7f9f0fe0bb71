
/**
 * 检查对象是否继承自指定类型
 * @param obj 要检查的对象
 * @param className 类名称
 * @returns 是否继承自指定类型
 */
export function isInstanceOf(obj: any, className: string): boolean {
  // 如果对象为空，直接返回false
  if (!obj || !obj.constructor) {
    return false;
  }
  // 获取当前对象的构造函数名称
  let currentConstructor = obj.constructor;

  // 如果当前对象就是目标类型，直接返回true
  if (currentConstructor.name === className) {
    return true;
  }

  // 遍历原型链检查继承关系
  let prototype = Object.getPrototypeOf(obj);
  while (prototype) {
    // 获取原型的构造函数
    const constructor = prototype.constructor;
    // console.log(`检查对象 ${obj.name} 是否继承自 ${className}，当前原型: ${constructor.name}`);
    // 如果找到匹配的类名，返回true
    if (constructor && constructor.name === className) {
      return true;
    }

    // 继续向上查找原型链
    prototype = Object.getPrototypeOf(prototype);
  }

  return false;
}

/**
 * 游戏角色类型列表
 */
const CHARACTER_CLASSES = [
  'Game_CharacterBase',
  'Game_Character',
  'Game_Vehicle',
  'Game_Event',
  'Game_Follower',
  'Game_Player'
];

/**
 * 检查对象是否为游戏角色或通过_character引用游戏角色
 * @param obj 要检查的对象
 * @param className 要查找的类名称
 * @returns 找到的游戏角色对象数组
 */
export function findCharacterObjects(obj: any, className: string): any[] {
  const results: any[] = [];

  // 如果对象为空，直接返回空数组
  if (!obj) {
    return results;
  }

  // 检查是否为游戏角色类型
  const isCharacterType = CHARACTER_CLASSES.includes(className);

  // 如果不是游戏角色类型，只检查对象本身
  if (!isCharacterType) {
    if (isInstanceOf(obj, className)) {
      results.push(obj);
    }
    return results;
  }

  // 检查对象本身是否为目标类型
  if (isInstanceOf(obj, className)) {
    results.push(obj);
  }

  // 检查对象是否有_character属性
  if (obj._character) {
    // 如果查找的是特定角色类型
    if (className !== 'Game_CharacterBase') {
      // 检查_character是否为目标类型
      if (isInstanceOf(obj._character, className)) {
        console.log(`找到包含${className}对象的显示对象:`, obj);
        // 返回显示对象本身，而不是_character属性
        if (!results.includes(obj)) {
          results.push(obj);
        }
      }
    }
    // 如果查找的是Game_CharacterBase
    else {
      // 检查_character是否为任何角色类型
      for (const charClass of CHARACTER_CLASSES) {
        if (isInstanceOf(obj._character, charClass)) {
          console.log(`找到包含${charClass}对象的显示对象:`, obj);
          // 返回显示对象本身，而不是_character属性
          if (!results.includes(obj)) {
            results.push(obj);
          }
          break; // 找到一个匹配就足够了
        }
      }
    }
  }

  return results;
}



/**
 * 查找当前场景中所有继承自指定类型的显示对象
 * @param gameWindow 游戏窗口对象
 * @param className 要查找的类名称
 * @returns 找到的所有显示对象数组
 */
export function findAllInstancesInCurrentScene(gameWindow: any, className: string): any[] {
  if (!gameWindow) {
    console.warn('游戏窗口对象不可用');
    return [];
  }

  console.log(`查找当前场景中所有继承自 ${className} 类型的显示对象...`);

  // 获取舞台对象
  let stage = null;

  // 首先尝试从SceneManager获取当前场景
  if (gameWindow.SceneManager && gameWindow.SceneManager._scene) {
    stage = gameWindow.SceneManager._scene;
    console.log(`从SceneManager获取到场景: ${stage.constructor.name}`);
  }

  // 如果没有当前场景，尝试从Graphics获取stage
  if (!stage && gameWindow.Graphics && gameWindow.Graphics._app && gameWindow.Graphics._app.stage) {
    stage = gameWindow.Graphics._app.stage;
    console.log(`从Graphics获取到舞台`);
  }

  console.log(`舞台对象是否存在: ${stage !== null}`);
  // 如果找不到舞台，直接返回空数组
  if (!stage) {
    console.warn('无法找到舞台对象');
    return [];
  }

  try {
    // 递归查找所有对象的函数
    const findAllObjects = (root: any): any[] => {
      const results: any[] = [];

      if (!root) {
        return results;
      }

      // 检查是否为游戏角色类型
      const isCharacterType = CHARACTER_CLASSES.includes(className);

      // 查找对象（包括直接匹配和通过_character引用的对象）
      const foundObjects = findCharacterObjects(root, className);

      // 将找到的对象添加到结果中（避免重复）
      for (const obj of foundObjects) {
        if (!results.includes(obj)) {
          results.push(obj);
        }
      }

      // 如果是角色类型且已找到对象，不再递归查找子对象
      if (isCharacterType && foundObjects.length > 0) {
        return results;
      }

      // 递归检查子对象
      if (root.children && Array.isArray(root.children)) {
        for (const child of root.children) {
          if (child) {
            const childResults = findAllObjects(child);
            results.push(...childResults);
          }
        }
      }

      return results;
    };

    // 从舞台开始递归查找所有对象
    const results = findAllObjects(stage);
    console.log(`总共找到 ${results.length} 个继承自 ${className} 类型的显示对象`);
    return results;
  } catch (error) {
    console.error(`查找 ${className} 类型对象时出错:`, error);
    return [];
  }
}



/**
 * 获取对象的类型名称
 * @param object 要检查的对象
 * @returns 对象的类型名称
 */
export function getObjectTypeName(object: any): string {
  if (!object || !object.constructor) return 'Unknown';
  return object.constructor.name;
}

/**
 * 检查对象是否为特定类型
 * @param object 要检查的对象
 * @param typeName 类型名称
 * @returns 是否为该类型
 */
export function isObjectOfType(object: any, typeName: string): boolean {
  return getObjectTypeName(object) === typeName;
}
