use tauri::AppHandle;
use crate::utils::logging;
use crate::save::modification_manager;
use crate::save::api::router::OperationRequest;

/// 处理类型创建操作
/// 在类型的 initialize 方法中创建对象
/// 路径顶级是当前选中的类型
pub fn handle_type_create(
    app_handle: AppHandle,
    request: OperationRequest,
) -> Result<String, String> {
    let top_level_type = request
        .top_level_type
        .as_ref()
        .ok_or("类型创建操作缺少顶级类型")?;

    logging::log_info(
        "TypeHandlers",
        &format!(
            "处理类型创建操作 - 类型: {}, 字段: {}, 路径: {:?}",
            top_level_type, request.field_name, request.target_path
        ),
    );

    // 验证路径顶级是否为类型名称
    if !request.target_path.is_empty() && request.target_path[0] != *top_level_type {
        return Err(format!(
            "类型操作路径错误：路径顶级 '{}' 与类型名称 '{}' 不匹配",
            request.target_path[0], top_level_type
        ));
    }

    // 调用类型原型修改接口
    // 这将在类型的 initialize 方法中生成对象创建代码
    modification_manager::record_class_prototype_modification(
        app_handle,
        top_level_type.clone(),
        request.field_name,
        request.field_value,
    )
}

/// 处理类型删除操作
/// 在类型的 initialize 方法中删除对象
/// 路径顶级是当前选中的类型
pub fn handle_type_delete(
    app_handle: AppHandle,
    request: OperationRequest,
) -> Result<String, String> {
    let top_level_type = request
        .top_level_type
        .as_ref()
        .ok_or("类型删除操作缺少顶级类型")?;

    logging::log_info(
        "TypeHandlers",
        &format!(
            "处理类型删除操作 - 类型: {}, 字段: {}, 路径: {:?}",
            top_level_type, request.field_name, request.target_path
        ),
    );

    // 验证路径顶级是否为类型名称
    if !request.target_path.is_empty() && request.target_path[0] != *top_level_type {
        return Err(format!(
            "类型操作路径错误：路径顶级 '{}' 与类型名称 '{}' 不匹配",
            request.target_path[0], top_level_type
        ));
    }

    // 调用类型原型修改接口
    // 这将在类型的 initialize 方法中生成对象删除代码
    modification_manager::record_class_prototype_modification(
        app_handle,
        top_level_type.clone(),
        request.field_name,
        request.field_value,
    )
}

/// 处理类型修改操作
/// 在类型的 initialize 方法中修改属性
/// 根据路径和字段操作具体的值
pub fn handle_type_modify(
    app_handle: AppHandle,
    request: OperationRequest,
) -> Result<String, String> {
    let top_level_type = request
        .top_level_type
        .as_ref()
        .ok_or("类型修改操作缺少顶级类型")?;

    logging::log_info(
        "TypeHandlers",
        &format!(
            "处理类型修改操作 - 类型: {}, 字段: {}, 路径: {:?}",
            top_level_type, request.field_name, request.target_path
        ),
    );

    // 验证路径顶级是否为类型名称
    if !request.target_path.is_empty() && request.target_path[0] != *top_level_type {
        return Err(format!(
            "类型操作路径错误：路径顶级 '{}' 与类型名称 '{}' 不匹配",
            request.target_path[0], top_level_type
        ));
    }

    // 根据路径和字段操作具体的值
    if request.target_path.len() == 1 {
        // 直接在类型上操作
        logging::log_info(
            "TypeHandlers",
            &format!("直接修改类型 {} 的属性", top_level_type),
        );
    } else {
        // 在类型的嵌套对象上操作
        logging::log_info(
            "TypeHandlers",
            &format!(
                "修改类型 {} 中路径 {:?} 的属性",
                top_level_type, request.target_path
            ),
        );
    }

    // 调用类型原型修改接口
    // 这将在类型的 initialize 方法中生成属性修改代码
    modification_manager::record_class_prototype_modification(
        app_handle,
        top_level_type.clone(),
        request.field_name,
        request.field_value,
    )
}
