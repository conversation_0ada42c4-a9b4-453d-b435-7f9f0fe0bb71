use crate::save::api::router::OperationRequest;
use crate::save::types::ClassPrototypeModification;
use crate::utils::logging;
use std::collections::HashMap;
use tauri::AppHandle;

/// 处理类型创建操作
/// 在类型的 initialize 方法中创建对象
/// 路径顶级是当前选中的类型
pub fn handle_type_create(
    app_handle: AppHandle,
    request: OperationRequest,
) -> Result<String, String> {
    let top_level_type = request
        .top_level_type
        .as_ref()
        .ok_or("类型创建操作缺少顶级类型")?;

    logging::log_info(
        "TypeHandlers",
        &format!(
            "处理类型创建操作 - 类型: {}, 字段: {}, 路径: {:?}",
            top_level_type, request.field_name, request.target_path
        ),
    );

    // 验证路径顶级是否为类型名称
    if !request.target_path.is_empty() && request.target_path[0] != *top_level_type {
        return Err(format!(
            "类型操作路径错误：路径顶级 '{}' 与类型名称 '{}' 不匹配",
            request.target_path[0], top_level_type
        ));
    }

    // 直接实现类型修改逻辑
    handle_type_modification(
        app_handle,
        top_level_type.clone(),
        request.field_name,
        request.field_value,
    )
}

/// 处理类型删除操作
/// 在类型的 initialize 方法中删除对象
/// 路径顶级是当前选中的类型
pub fn handle_type_delete(
    app_handle: AppHandle,
    request: OperationRequest,
) -> Result<String, String> {
    let top_level_type = request
        .top_level_type
        .as_ref()
        .ok_or("类型删除操作缺少顶级类型")?;

    logging::log_info(
        "TypeHandlers",
        &format!(
            "处理类型删除操作 - 类型: {}, 字段: {}, 路径: {:?}",
            top_level_type, request.field_name, request.target_path
        ),
    );

    // 验证路径顶级是否为类型名称
    if !request.target_path.is_empty() && request.target_path[0] != *top_level_type {
        return Err(format!(
            "类型操作路径错误：路径顶级 '{}' 与类型名称 '{}' 不匹配",
            request.target_path[0], top_level_type
        ));
    }

    // 直接实现类型修改逻辑
    handle_type_modification(
        app_handle,
        top_level_type.clone(),
        request.field_name,
        request.field_value,
    )
}

/// 处理类型修改操作
/// 在类型的 initialize 方法中修改属性
/// 根据路径和字段操作具体的值
pub fn handle_type_modify(
    app_handle: AppHandle,
    request: OperationRequest,
) -> Result<String, String> {
    let top_level_type = request
        .top_level_type
        .as_ref()
        .ok_or("类型修改操作缺少顶级类型")?;

    logging::log_info(
        "TypeHandlers",
        &format!(
            "处理类型修改操作 - 类型: {}, 字段: {}, 路径: {:?}",
            top_level_type, request.field_name, request.target_path
        ),
    );

    // 验证路径顶级是否为类型名称
    if !request.target_path.is_empty() && request.target_path[0] != *top_level_type {
        return Err(format!(
            "类型操作路径错误：路径顶级 '{}' 与类型名称 '{}' 不匹配",
            request.target_path[0], top_level_type
        ));
    }

    // 根据路径和字段操作具体的值
    if request.target_path.len() == 1 {
        // 直接在类型上操作
        logging::log_info(
            "TypeHandlers",
            &format!("直接修改类型 {} 的属性", top_level_type),
        );
    } else {
        // 在类型的嵌套对象上操作
        logging::log_info(
            "TypeHandlers",
            &format!(
                "修改类型 {} 中路径 {:?} 的属性",
                top_level_type, request.target_path
            ),
        );
    }

    // 直接实现类型修改逻辑
    handle_type_modification(
        app_handle,
        top_level_type.clone(),
        request.field_name,
        request.field_value,
    )
}

/// 统一的类型修改处理函数
/// 直接实现修改逻辑，不依赖旧的API
fn handle_type_modification(
    app_handle: AppHandle,
    class_name: String,
    field_name: String,
    value: serde_json::Value,
) -> Result<String, String> {
    logging::log_info(
        "TypeHandlers",
        &format!(
            "统一处理类型修改 - 类名: {}, 字段: {}, 值: {}",
            class_name, field_name, value
        ),
    );

    // 获取当前类型修改列表
    let mut modifications = get_current_class_modifications(&app_handle)?;

    // 查找是否已存在相同类名的修改
    let mut found = false;
    for modification in &mut modifications {
        if modification.class_name == class_name {
            // 更新现有修改的属性
            modification
                .properties
                .insert(field_name.clone(), value.clone());
            found = true;
            break;
        }
    }

    // 如果没有找到相同类名的修改，创建新的修改
    if !found {
        let mut properties = HashMap::new();
        properties.insert(field_name.clone(), value.clone());

        modifications.push(ClassPrototypeModification {
            class_name: class_name.clone(),
            properties,
        });
    }

    // 更新缓存
    update_cached_class_modifications(modifications)?;

    Ok(format!("类型 {} 的属性 {} 已记录", class_name, field_name))
}

/// 获取当前类型修改列表
fn get_current_class_modifications(
    app_handle: &AppHandle,
) -> Result<Vec<ClassPrototypeModification>, String> {
    // 这里需要实现获取类型修改列表的逻辑
    use crate::utils::cache;
    cache::get_cached_class_modifications()
}

/// 更新缓存的类型修改列表
fn update_cached_class_modifications(
    modifications: Vec<ClassPrototypeModification>,
) -> Result<(), String> {
    use crate::utils::cache;
    cache::update_cached_class_modifications(modifications)
}
