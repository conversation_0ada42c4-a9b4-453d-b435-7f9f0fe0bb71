import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { open } from '@tauri-apps/plugin-dialog';
import useProjectStore from '../store/Store';
import {
  Box,
  List,
  ListItem,
  ListItemText,
  Button,
  Typography,
  Dialog,
  TextField,
  IconButton,
  Tooltip,
  Paper,
  CircularProgress,
  Alert
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import FolderOpenIcon from '@mui/icons-material/FolderOpen';
import RefreshIcon from '@mui/icons-material/Refresh';

interface ProjectInfo {
  name: string;
  path: string;
  last_opened?: string;
}

// 不再需要外部传入的 onProjectSelect 函数
interface ProjectSelectorProps { }

const ProjectSelector: React.FC<ProjectSelectorProps> = () => {
  const [projects, setProjects] = useState<ProjectInfo[]>([]);
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [importPath, setImportPath] = useState('');
  const [projectName, setProjectName] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<string | null>(null);

  // 使用全局状态管理
  const setGlobalProjectName = useProjectStore(state => state.setProjectName);
  const setGlobalProjectPath = useProjectStore(state => state.setProjectPath);
  const setProjectSelected = useProjectStore(state => state.setProjectSelected);

  // 加载项目列表
  const loadProjects = async () => {
    setLoading(true);
    setError('');

    try {
      const projectList = await invoke<ProjectInfo[]>('get_project_list');
      setProjects(projectList || []);
    } catch (err) {
      console.error('加载项目列表失败:', err);
      setError(`加载项目列表失败: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadProjects();
  }, []);

  // 打开项目
  const handleProjectSelect = (project: ProjectInfo) => {
    console.log("ProjectSelector - 选择项目:", project);

    // 更新全局状态
    setGlobalProjectName(project.name);
    setGlobalProjectPath(project.path);
    setProjectSelected(true);
    // 不再需要调用外部传入的 onProjectSelect 函数

    // 更新项目的最后打开时间
    invoke('update_project_last_opened', { projectName: project.name })
      .catch(err => console.error('更新项目最后打开时间失败:', err));

    // 更新自定义资源路径插件中的项目名称
    invoke('update_custom_resource_path', { projectName: project.name })
      .then(() => {
        console.log(`已更新自定义资源路径插件，项目名称: ${project.name}`);
      })
      .catch(err => {
        console.error('更新自定义资源路径插件失败:', err);
        // 这里不阻止项目打开，只是记录错误
      });
  };

  // 导入项目
  const handleImportProject = async () => {
    if (!importPath) {
      setError('请选择项目目录');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const name = await invoke<string>('import_project', {
        sourcePath: importPath,
        projectName: projectName || undefined
      });

      setImportDialogOpen(false);
      setImportPath('');
      setProjectName('');

      // 获取项目路径
      const projectPath = await invoke<string>('get_project_path', { projectName: name });

      // 获取公共目录路径
      const publicDirPath = await invoke<string>('get_public_dir_path');

      // 重新加载项目列表
      await loadProjects();

      alert(`项目 "${name}" 已成功导入\n\n项目路径: ${projectPath}\n\n公共目录: ${publicDirPath}`);
    } catch (err) {
      console.error('导入项目失败:', err);
      setError(`导入项目失败: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  // 选择项目目录
  const handleSelectDirectory = async () => {
    try {
      const selected = await open({
        directory: true,
        multiple: false,
        title: '选择 RPG Maker 项目目录'
      });

      if (selected) {
        const path = selected as string;
        setImportPath(path);

        // 提取目录名作为默认项目名
        const dirName = path.split(/[/\\]/).pop();
        setProjectName(dirName || '');
      }
    } catch (err) {
      console.error('选择目录失败:', err);
      setError('选择目录失败');
    }
  };

  // 删除项目
  const handleDeleteProject = async (projectName: string, event: React.MouseEvent) => {
    event.stopPropagation(); // 防止触发项目选择
    setProjectToDelete(projectName);
    setDeleteConfirmOpen(true);
  };

  // 确认删除项目
  const confirmDeleteProject = async () => {
    if (!projectToDelete) return;

    setLoading(true);
    setError('');

    try {
      // 获取项目路径（用于显示）
      let projectPath = "";
      try {
        projectPath = await invoke<string>('get_project_path', { projectName: projectToDelete });
      } catch (e) {
        console.log('项目路径不存在:', e);
        projectPath = "项目目录不存在";
      }

      // 删除项目
      await invoke('delete_project', { projectName: projectToDelete });

      // 重新加载项目列表
      await loadProjects();

      alert(`项目 "${projectToDelete}" 已成功从列表中移除\n\n项目路径: ${projectPath}`);
    } catch (err) {
      console.error('删除项目失败:', err);
      setError(`删除项目失败: ${err}`);
    } finally {
      setLoading(false);
      setDeleteConfirmOpen(false);
      setProjectToDelete(null);
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom sx={{ mb: 3, textAlign: 'center' }}>
        RPG Maker 项目管理器
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="h6" sx={{ mr: 1 }}>
              项目列表
            </Typography>
            <Tooltip title="刷新项目列表">
              <IconButton
                color="primary"
                onClick={loadProjects}
                disabled={loading}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>

          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => setImportDialogOpen(true)}
          >
            导入新项目
          </Button>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : projects.length > 0 ? (
          <List>
            {projects.map((project) => (
              <ListItem
                key={project.name}
                sx={{
                  border: '1px solid #eee',
                  mb: 1,
                  borderRadius: 1,
                  '&:hover': {
                    backgroundColor: '#f5f5f5',
                    cursor: 'pointer'
                  }
                }}
              >
                <ListItemText
                  primary={project.name}
                  secondary={
                    <>
                      <div>路径: {project.path}</div>
                      <div>上次打开: {project.last_opened ? new Date(project.last_opened).toLocaleString() : '从未'}</div>
                    </>
                  }
                  onClick={() => handleProjectSelect(project)}
                  sx={{ flex: 1 }}
                />
                <Box>
                  <Tooltip title="打开项目">
                    <IconButton
                      color="primary"
                      onClick={() => handleProjectSelect(project)}
                    >
                      <FolderOpenIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="删除项目">
                    <IconButton
                      color="error"
                      onClick={(e) => handleDeleteProject(project.name, e)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </ListItem>
            ))}
          </List>
        ) : (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body1" color="text.secondary">
              没有可用的项目。请导入一个新项目。
            </Typography>
          </Box>
        )}
      </Paper>

      {/* 导入项目对话框 */}
      <Dialog
        open={importDialogOpen}
        onClose={() => setImportDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <Box sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            导入 RPG Maker 项目
          </Typography>

          <Box sx={{ mb: 3 }}>
            <Button
              variant="outlined"
              startIcon={<FolderOpenIcon />}
              onClick={handleSelectDirectory}
              fullWidth
            >
              选择项目目录
            </Button>
            {importPath && (
              <Typography variant="body2" sx={{ mt: 1, p: 1, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                已选择: {importPath}
              </Typography>
            )}
          </Box>

          <TextField
            label="项目名称"
            value={projectName}
            onChange={(e) => setProjectName(e.target.value)}
            fullWidth
            sx={{ mb: 3 }}
          />

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button onClick={() => setImportDialogOpen(false)}>
              取消
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={handleImportProject}
              disabled={!importPath || !projectName || loading}
            >
              {loading ? <CircularProgress size={24} /> : '导入'}
            </Button>
          </Box>
        </Box>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
      >
        <Box sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            确认删除
          </Typography>

          <Typography variant="body1" sx={{ mb: 3 }}>
            您确定要删除项目 "{projectToDelete}" 吗？此操作无法撤销。
          </Typography>

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button onClick={() => setDeleteConfirmOpen(false)}>
              取消
            </Button>
            <Button
              variant="contained"
              color="error"
              onClick={confirmDeleteProject}
            >
              删除
            </Button>
          </Box>
        </Box>
      </Dialog>
    </Box>
  );
};

export default ProjectSelector;
