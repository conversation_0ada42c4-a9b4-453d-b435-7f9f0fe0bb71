/**
 * 主代码生成器
 * 统一管理所有类型的代码生成，并组织输出格式
 */

import {
  OperationInfo,
  GeneratedCode,
  SceneModificationCode,
  PrototypeModificationCode,
  CodeSnippet,
  GeneratorConfig,
  OperationMode
} from './core/types';
import {
  isTypeOperation,
  isObjectOperation,
  getSceneName,
  validateOperationInfo,
  mergeCodeSnippets,
  createDebugLogger
} from './core/utils';
import { PropertyGenerator } from './property/PropertyGenerator';
import { ObjectGenerator } from './object/ObjectGenerator';

/**
 * 主代码生成器类
 */
export class CodeGenerator {
  private propertyGenerator: PropertyGenerator;
  private objectGenerator: ObjectGenerator;
  private config: GeneratorConfig;
  private debug: ReturnType<typeof createDebugLogger>;

  constructor(config: Partial<GeneratorConfig> = {}) {
    this.config = {
      debug: false,
      minify: false,
      addComments: true,
      variablePrefix: 'rpgEditor_',
      indentSize: 2,
      ...config
    };

    this.propertyGenerator = new PropertyGenerator(this.config);
    this.objectGenerator = new ObjectGenerator(this.config);
    this.debug = createDebugLogger('CodeGenerator');
  }

  /**
   * 生成所有操作的代码
   */
  public generateCode(operations: OperationInfo[]): GeneratedCode {
    this.debug('开始生成代码，操作数量:', operations.length);

    // 验证所有操作
    this.validateOperations(operations);

    // 按操作模式分类
    const { objectOperations, typeOperations } = this.categorizeOperations(operations);

    this.debug('对象操作数量:', objectOperations.length);
    this.debug('类型操作数量:', typeOperations.length);

    // 生成场景修改代码
    const sceneModifications = this.generateSceneModifications(objectOperations);

    // 生成原型修改代码
    const prototypeModifications = this.generatePrototypeModifications(typeOperations);

    const result: GeneratedCode = {
      sceneModifications,
      prototypeModifications,
      metadata: {
        generatedAt: Date.now(),
        operationCount: operations.length,
        version: '1.0.0'
      }
    };

    this.debug('代码生成完成');
    return result;
  }

  /**
   * 验证所有操作
   */
  private validateOperations(operations: OperationInfo[]): void {
    const errors: string[] = [];

    operations.forEach((operation, index) => {
      const operationErrors = validateOperationInfo(operation);
      if (operationErrors.length > 0) {
        errors.push(`操作 ${index}: ${operationErrors.join(', ')}`);
      }
    });

    if (errors.length > 0) {
      throw new Error(`操作验证失败:\n${errors.join('\n')}`);
    }
  }

  /**
   * 按操作模式分类操作
   */
  private categorizeOperations(operations: OperationInfo[]): {
    objectOperations: OperationInfo[];
    typeOperations: OperationInfo[];
  } {
    const objectOperations: OperationInfo[] = [];
    const typeOperations: OperationInfo[] = [];

    operations.forEach(operation => {
      if (isTypeOperation(operation)) {
        typeOperations.push(operation);
      } else if (isObjectOperation(operation)) {
        objectOperations.push(operation);
      } else {
        this.debug('未知操作模式:', operation);
        // 默认作为对象操作处理
        objectOperations.push(operation);
      }
    });

    return { objectOperations, typeOperations };
  }

  /**
   * 生成场景修改代码
   */
  private generateSceneModifications(operations: OperationInfo[]): SceneModificationCode[] {
    if (operations.length === 0) return [];

    // 按场景分组
    const sceneGroups = this.groupOperationsByScene(operations);

    return Array.from(sceneGroups.entries()).map(([sceneName, sceneOperations]) => {
      this.debug(`生成场景 ${sceneName} 的修改代码，操作数量:`, sceneOperations.length);

      const modifications = sceneOperations.map(operation => {
        return this.generateOperationCode(operation);
      }).filter(snippet => snippet.code.trim().length > 0);

      return {
        sceneName,
        modifications
      };
    });
  }

  /**
   * 生成原型修改代码
   */
  private generatePrototypeModifications(operations: OperationInfo[]): PrototypeModificationCode[] {
    if (operations.length === 0) return [];

    // 按类名分组
    const classGroups = this.groupOperationsByClass(operations);

    return Array.from(classGroups.entries()).map(([className, classOperations]) => {
      this.debug(`生成类 ${className} 的修改代码，操作数量:`, classOperations.length);

      const modifications = classOperations.map(operation => {
        return this.generateOperationCode(operation);
      }).filter(snippet => snippet.code.trim().length > 0);

      return {
        className,
        modifications
      };
    });
  }

  /**
   * 按场景分组操作
   */
  private groupOperationsByScene(operations: OperationInfo[]): Map<string, OperationInfo[]> {
    const groups = new Map<string, OperationInfo[]>();

    operations.forEach(operation => {
      const sceneName = getSceneName(operation.objectPath);
      if (!groups.has(sceneName)) {
        groups.set(sceneName, []);
      }
      groups.get(sceneName)!.push(operation);
    });

    return groups;
  }

  /**
   * 按类名分组操作
   */
  private groupOperationsByClass(operations: OperationInfo[]): Map<string, OperationInfo[]> {
    const groups = new Map<string, OperationInfo[]>();

    operations.forEach(operation => {
      const className = operation.className;
      if (!groups.has(className)) {
        groups.set(className, []);
      }
      groups.get(className)!.push(operation);
    });

    return groups;
  }

  /**
   * 生成单个操作的代码
   */
  private generateOperationCode(operation: OperationInfo): CodeSnippet {
    try {
      // 根据操作类型选择合适的生成器
      if (this.propertyGenerator.canHandle(operation)) {
        return this.propertyGenerator.generate(operation);
      } else if (this.objectGenerator.canHandle(operation)) {
        return this.objectGenerator.generate(operation);
      } else {
        throw new Error(`不支持的操作类型: ${operation.operationType}`);
      }
    } catch (error) {
      this.debug('生成操作代码失败:', error);
      
      // 返回错误注释
      return {
        code: `// 生成代码失败: ${error instanceof Error ? error.message : '未知错误'}`,
        description: `错误: ${operation.operationType} 操作生成失败`,
        dependencies: []
      };
    }
  }

  /**
   * 批量生成代码（优化版本）
   */
  public generateCodeBatch(operationBatches: OperationInfo[][]): GeneratedCode[] {
    this.debug('开始批量生成代码，批次数量:', operationBatches.length);

    return operationBatches.map((operations, index) => {
      this.debug(`处理批次 ${index + 1}/${operationBatches.length}`);
      return this.generateCode(operations);
    });
  }

  /**
   * 生成代码预览
   */
  public generatePreview(operations: OperationInfo[]): string {
    const generated = this.generateCode(operations);
    
    const preview: string[] = [];
    
    // 场景修改预览
    if (generated.sceneModifications.length > 0) {
      preview.push('=== 场景修改 ===');
      generated.sceneModifications.forEach(scene => {
        preview.push(`\n场景: ${scene.sceneName}`);
        scene.modifications.forEach((mod, index) => {
          preview.push(`  ${index + 1}. ${mod.description || '未知操作'}`);
        });
      });
    }

    // 原型修改预览
    if (generated.prototypeModifications.length > 0) {
      preview.push('\n=== 原型修改 ===');
      generated.prototypeModifications.forEach(proto => {
        preview.push(`\n类: ${proto.className}`);
        proto.modifications.forEach((mod, index) => {
          preview.push(`  ${index + 1}. ${mod.description || '未知操作'}`);
        });
      });
    }

    return preview.join('\n');
  }

  /**
   * 获取生成器统计信息
   */
  public getStats() {
    return {
      propertyGenerator: this.propertyGenerator.getStats(),
      objectGenerator: this.objectGenerator.getStats(),
      config: this.config
    };
  }

  /**
   * 重置所有生成器
   */
  public reset(): void {
    this.propertyGenerator.reset();
    this.objectGenerator.reset();
    this.debug('所有生成器已重置');
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<GeneratorConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 重新创建生成器以应用新配置
    this.propertyGenerator = new PropertyGenerator(this.config);
    this.objectGenerator = new ObjectGenerator(this.config);
    
    this.debug('配置已更新');
  }
}
