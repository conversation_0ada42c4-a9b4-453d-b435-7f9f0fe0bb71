import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardHeader,
  CardContent,
  Paper,
  Chip,
  IconButton,
  Grid
} from '@mui/material';
import TheaterComedyIcon from '@mui/icons-material/TheaterComedy';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import RefreshIcon from '@mui/icons-material/Refresh';
import DraggableBase from '../drag/DraggableBase';

// 创建自定义Grid组件以解决TypeScript类型问题
const Item = (props: any) => <Grid item {...props} />;

// 场景信息接口
interface SceneInfo {
  id: string;
  name: string;
  displayName: string;
  description: string;
  color: string;
  methodName: string;
  methodCode: string; // 事件方法的字符串表示
  lastTriggered: Date | null;
  triggerCount: number;
}

const GameEvents: React.FC = () => {
  // 场景列表
  const [scenes, setScenes] = useState<SceneInfo[]>([
    {
      id: 'scene_title',
      name: 'Scene_Title',
      displayName: '标题场景',
      description: '游戏标题和主菜单场景',
      color: '#3f51b5',
      methodName: 'goto',
      methodCode: 'SceneManager.goto(Scene_Title);',
      lastTriggered: null,
      triggerCount: 0
    },
    {
      id: 'scene_map',
      name: 'Scene_Map',
      displayName: '地图场景',
      description: '游戏地图和角色移动场景',
      color: '#2196f3',
      methodName: 'goto',
      methodCode: 'SceneManager.goto(Scene_Map);',
      lastTriggered: null,
      triggerCount: 0
    },
    {
      id: 'scene_battle',
      name: 'Scene_Battle',
      displayName: '战斗场景',
      description: '游戏战斗场景',
      color: '#f44336',
      methodName: 'goto',
      methodCode: 'SceneManager.goto(Scene_Battle);',
      lastTriggered: null,
      triggerCount: 0
    },
    {
      id: 'scene_menu',
      name: 'Scene_Menu',
      displayName: '菜单场景',
      description: '游戏主菜单场景',
      color: '#ff9800',
      methodName: 'goto',
      methodCode: 'SceneManager.goto(Scene_Menu);',
      lastTriggered: null,
      triggerCount: 0
    },
    {
      id: 'scene_item',
      name: 'Scene_Item',
      displayName: '物品场景',
      description: '游戏物品菜单场景',
      color: '#ff5722',
      methodName: 'goto',
      methodCode: 'SceneManager.goto(Scene_Item);',
      lastTriggered: null,
      triggerCount: 0
    },
    {
      id: 'scene_skill',
      name: 'Scene_Skill',
      displayName: '技能场景',
      description: '游戏技能菜单场景',
      color: '#9c27b0',
      methodName: 'goto',
      methodCode: 'SceneManager.goto(Scene_Skill);',
      lastTriggered: null,
      triggerCount: 0
    },
    {
      id: 'scene_status',
      name: 'Scene_Status',
      displayName: '状态场景',
      description: '游戏角色状态场景',
      color: '#673ab7',
      methodName: 'goto',
      methodCode: 'SceneManager.goto(Scene_Status);',
      lastTriggered: null,
      triggerCount: 0
    },
    {
      id: 'scene_options',
      name: 'Scene_Options',
      displayName: '选项场景',
      description: '游戏选项设置场景',
      color: '#009688',
      methodName: 'goto',
      methodCode: 'SceneManager.goto(Scene_Options);',
      lastTriggered: null,
      triggerCount: 0
    },
    {
      id: 'scene_save',
      name: 'Scene_Save',
      displayName: '保存场景',
      description: '游戏保存场景',
      color: '#4caf50',
      methodName: 'goto',
      methodCode: 'SceneManager.goto(Scene_Save);',
      lastTriggered: null,
      triggerCount: 0
    },
    {
      id: 'scene_load',
      name: 'Scene_Load',
      displayName: '读取场景',
      description: '游戏读取场景',
      color: '#8bc34a',
      methodName: 'goto',
      methodCode: 'SceneManager.goto(Scene_Load);',
      lastTriggered: null,
      triggerCount: 0
    },
    {
      id: 'scene_gameover',
      name: 'Scene_GameEnd',
      displayName: '游戏结束场景',
      description: '游戏结束场景',
      color: '#795548',
      methodName: 'goto',
      methodCode: 'SceneManager.goto(Scene_GameEnd);',
      lastTriggered: null,
      triggerCount: 0
    }
  ]);

  // 刷新事件状态
  const handleRefresh = () => {
    // 重置所有场景的触发状态
    setScenes(prev => prev.map(scene => ({
      ...scene,
      lastTriggered: null,
      triggerCount: 0
    })));
  };

  // 渲染场景卡片
  const renderSceneCard = (scene: SceneInfo) => (
    <Item key={scene.id}>
      {/* <div>{scene.methodCode} </div> */}
      <DraggableBase id={scene.id} data={{ type: 'cutSceneEvent', scene: scene }}>
        <Card
          variant="outlined"
          sx={{
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
            borderLeft: `3px solid ${scene.color}`,
          }}
        >
          <CardHeader
            avatar={
              <Box
                sx={{
                  width: 24,
                  height: 24,
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: scene.color,
                  color: 'white'
                }}
              >
                <TheaterComedyIcon sx={{ fontSize: '0.9rem' }} />
              </Box>
            }
            title={
              <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>
                {scene.displayName}
              </Typography>
            }
            subheader={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <PlayArrowIcon sx={{ fontSize: '0.7rem', mr: 0.3, color: scene.color }} />
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '0.7rem' }}>
                  goto({scene.name})
                </Typography>
              </Box>
            }
            sx={{
              p: 1,
              pb: 0,
              '& .MuiCardHeader-content': { minWidth: 0 }
            }}
          />
          <CardContent sx={{ p: 1, pt: 0.5, pb: '8px !important', display: 'flex', justifyContent: 'flex-end' }}>
            {scene.triggerCount > 0 && (
              <Chip
                label={scene.lastTriggered ? scene.lastTriggered.toLocaleTimeString() : `${scene.triggerCount}次`}
                size="small"
                color="primary"
                variant="outlined"
                sx={{ height: 18, '& .MuiChip-label': { px: 0.8, fontSize: '0.65rem' } }}
              />
            )}
          </CardContent>
        </Card>
      </DraggableBase>
    </Item >
  );

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{
        py: 0.5,
        px: 1,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottom: '1px solid #e0e0e0'
      }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>场景切换方法</Typography>
        <IconButton
          onClick={handleRefresh}
          title="重置事件状态"
          size="small"
        >
          <RefreshIcon fontSize="small" />
        </IconButton>
      </Box>

      <Paper
        elevation={0}
        sx={{
          flex: 1,
          overflow: 'auto',
          p: 0.5
        }}
      >
        <Grid container spacing={0.5}>
          {scenes.map(renderSceneCard)}
        </Grid>
      </Paper>
    </Box>
  );
};

export default GameEvents;
