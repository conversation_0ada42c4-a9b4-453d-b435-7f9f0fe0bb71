import { Container, DisplayObject } from 'pixi.js';
import { TreeNode, NodeType } from './TreeNodeTypes';

/**
 * 检查对象是否是容器类型
 * @param obj 要检查的对象
 * @returns 是否是容器类型
 */
export const isContainer = (obj: any): boolean => {
  if (!obj) return false;

  try {
    // 使用可选链操作符安全地访问constructor.name
    const constructorName = obj.constructor?.name || '';

    // 检查是否有 addChild 和 removeChild 方法
    const hasAddChildMethod = typeof obj.addChild === 'function';
    const hasRemoveChildMethod = typeof obj.removeChild === 'function';

    // 检查是否有 children 属性
    const hasChildrenArray = obj.children && Array.isArray(obj.children);

    // 检查是否是 RPG Maker MZ 的场景对象
    const isRPGMakerScene =
      constructorName.startsWith('Scene_') ||
      constructorName === 'Stage' ||
      constructorName === 'Spriteset_Map' ||
      constructorName === 'Spriteset_Battle' ||
      constructorName === 'WindowLayer' ||
      constructorName === 'Window';

    // 检查原型链中是否有 PIXI.Container
    let isInPrototypeChain = false;
    let proto = Object.getPrototypeOf(obj);
    while (proto) {
      if (proto.constructor && proto.constructor.name === 'Container') {
        isInPrototypeChain = true;
        break;
      }
      proto = Object.getPrototypeOf(proto);
    }

    // 综合判断是否是容器
    const isRealContainer =
      // 标准容器类型检查
      obj instanceof Container ||
      // 特定类名检查
      constructorName.includes('Container') ||
      // RPG Maker MZ 场景检查
      isRPGMakerScene ||
      // 原型链检查
      isInPrototypeChain ||
      // 方法和属性检查
      (hasChildrenArray && hasAddChildMethod && hasRemoveChildMethod);

    // 添加调试日志
    if (isRealContainer) {
      // console.log(`识别到容器: ${obj.name || constructorName}, 类型: ${constructorName}`);
      // console.log(`  - 有children数组: ${hasChildrenArray}`);
      // console.log(`  - 有addChild方法: ${hasAddChildMethod}`);
      // console.log(`  - 有removeChild方法: ${hasRemoveChildMethod}`);
      // console.log(`  - 是RPG Maker场景: ${isRPGMakerScene}`);
      // console.log(`  - 在原型链中有Container: ${isInPrototypeChain}`);
    }

    return isRealContainer;
  } catch (e) {
    console.warn("Error in isContainer:", e);
    return false;
  }
};

/**
 * 检查对象是否有事件
 * 优化版本：只检查 _events 属性，避免深度遍历和复杂检查
 * @param obj 要检查的对象
 * @returns 是否有事件
 */
export const hasEvents = (obj: any): boolean => {
  if (!obj) return false;

  try {
    // 只检查对象是否有非空的 _events 属性
    return !!(obj._events && Object.keys(obj._events).length > 0);
  } catch (e) {
    // 简化错误处理，避免不必要的日志
    return false;
  }
};

/**
 * 获取容器的子对象
 * @param container 容器对象
 * @returns 子对象数组
 */
export const getChildren = (container: Container | any): DisplayObject[] => {
  if (!container) return [];

  try {
    // 检查是否有children属性
    if (container.children && Array.isArray(container.children)) {
      return container.children;
    }

    // 特殊处理：如果是Spriteset_Map，返回其特定的子对象
    if (container.constructor?.name === "Spriteset_Map") {
      const children: DisplayObject[] = [];

      // 添加tilemap
      if (container._tilemap) {
        children.push(container._tilemap);
      }

      // 添加shadowBitmap
      if (container._shadowBitmap) {
        children.push(container._shadowBitmap);
      }

      // 添加其他可能的子对象
      if (container._characterSprites && Array.isArray(container._characterSprites)) {
        children.push(...container._characterSprites);
      }

      return children;
    }

    return [];
  } catch (e) {
    console.warn("Error in getChildren:", e);
    return [];
  }
};

/**
 * 递归构建对象树
 * @param container 容器对象
 * @param parentName 父对象名称
 * @param isRoot 是否是根容器
 * @returns 树节点数组
 */
export const buildTree = (
  container: Container | any,
  parentName: string = "",
  isRoot: boolean = true
): TreeNode[] => {
  // 如果是根容器，创建场景节点
  if (isRoot) {
    // 获取场景名称
    const sceneName = container.constructor?.name || "Scene";

    // 创建场景节点 - 使用固定ID，确保稳定性
    const sceneNode: TreeNode = {
      id: 'scene_root', // 使用固定ID，不再使用时间戳
      name: `场景 (${sceneName})`,
      object: container,
      children: [],
      visible: container.visible, // 直接使用对象的visible属性
      hasEvents: hasEvents(container),
      // 场景节点同时具有SCENE和CONTAINER类型特性
      type: NodeType.CONTAINER, // 将类型设置为CONTAINER，确保它被识别为容器
      parentId: undefined
    };

    // 获取子节点
    const children = getChildren(container);

    // 递归构建子节点
    sceneNode.children = children.map((child, index) => {
      return buildChildNode(child, index, sceneNode.id, sceneNode.name);
    });

    return [sceneNode];
  } else {
    // 非根容器，使用原来的逻辑
    const children = getChildren(container);

    return children.map((child, index) => {
      return buildChildNode(child, index, "", parentName);
    });
  }
};

/**
 * 构建子节点
 * @param child 子对象
 * @param index 索引
 * @param parentId 父节点ID
 * @param parentName 父节点名称
 * @returns 树节点
 */
const buildChildNode = (
  child: DisplayObject,
  index: number,
  parentId: string = "",
  parentName: string = ""
): TreeNode => {
  // 安全地获取子对象名称
  let childName = child.name;
  if (!childName) {
    // 安全地获取对象的构造函数名称
    const constructorName = child?.constructor?.name || 'Unknown';
    childName = `${constructorName}_${index}`;
  }
  const fullName = parentName ? `${parentName}/${childName}` : childName;

  // 检查对象是否有事件
  const objectHasEvents = hasEvents(child);

  // 确定节点类型
  let nodeType = NodeType.OTHER;
  const constructorName = child?.constructor?.name || '';

  // 检查是否是LayoutContainer
  if (constructorName === 'LayoutContainer' || (child as any)._layoutType !== undefined) {
    nodeType = NodeType.LAYOUT_CONTAINER;
  } else if (constructorName.includes('Sprite')) {
    nodeType = NodeType.SPRITE;
  } else if (constructorName.includes('Text') || (child as any)._bitmap?.text) {
    nodeType = NodeType.LABEL;
  } else if (isContainer(child)) {
    nodeType = NodeType.CONTAINER;
  }

  // 创建节点
  const node: TreeNode = {
    id: `${fullName}_${index}`,
    name: childName,
    object: child,
    children: [],
    visible: child.visible, // 直接使用对象的visible属性
    hasEvents: objectHasEvents,
    type: nodeType,
    parentId: parentId
  };

  // 如果是容器，递归构建子节点
  if (isContainer(child)) {
    // 添加调试日志
    // console.log(`构建子节点: ${childName}, 子节点数量: ${(child as Container).children?.length || 0}`);

    // 递归构建子节点，确保传递正确的参数
    const childNodes = buildTree(child, fullName, false);

    // 添加调试日志
    // console.log(`子节点构建完成: ${childName}, 返回子节点数量: ${childNodes.length}`);

    node.children = childNodes;
  }

  return node;
};

/**
 * 查找树节点
 * @param nodes 节点数组
 * @param nodeId 要查找的节点ID
 * @returns 找到的节点或null
 */
export const findNode = (nodes: TreeNode[], nodeId: string): TreeNode | null => {
  for (const node of nodes) {
    if (node.id === nodeId) {
      return node;
    }

    if (node.children.length > 0) {
      const found = findNode(node.children, nodeId);
      if (found) {
        return found;
      }
    }
  }

  return null;
};

/**
 * 切换节点的可见性
 * @param nodes 节点数组
 * @param nodeId 要切换的节点ID
 * @returns 更新后的节点数组
 */
export const toggleNodeVisibility = (nodes: TreeNode[], nodeId: string): TreeNode[] => {
  return nodes.map(node => {
    if (node.id === nodeId) {
      // 切换当前节点的可见性
      const updatedNode = {
        ...node,
        visible: !node.visible
      };

      // 更新对象的可见性
      if (updatedNode.object) {
        updatedNode.object.visible = updatedNode.visible;
      }

      return updatedNode;
    } else if (node.children.length > 0) {
      // 递归处理子节点
      return {
        ...node,
        children: toggleNodeVisibility(node.children, nodeId)
      };
    }

    return node;
  });
};
