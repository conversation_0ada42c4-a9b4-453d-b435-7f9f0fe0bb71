//=============================================================================
// vorbisdecoder.js v1.0.1
// This code is based on: http://nothings.org/stb_vorbis/
//=============================================================================
var VorbisDecoderModule={};VorbisDecoderModule["wasmBinary"]=Uint8Array.from(atob("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"),function(c){return c.charCodeAt(0)});
var __dirname=null;var Module=typeof VorbisDecoderModule!=="undefined"?VorbisDecoderModule:{};var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}var arguments_=[];var thisProgram="./this.program";var quit_=function(status,toThrow){throw toThrow};var ENVIRONMENT_IS_WEB=false;var ENVIRONMENT_IS_WORKER=false;var ENVIRONMENT_IS_NODE=false;var ENVIRONMENT_IS_SHELL=false;ENVIRONMENT_IS_WEB=typeof window==="object";ENVIRONMENT_IS_WORKER=typeof importScripts==="function";ENVIRONMENT_IS_NODE=typeof process==="object"&&typeof process.versions==="object"&&typeof process.versions.node==="string";ENVIRONMENT_IS_SHELL=!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_NODE&&!ENVIRONMENT_IS_WORKER;var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;var nodeFS;var nodePath;if(ENVIRONMENT_IS_NODE){if(ENVIRONMENT_IS_WORKER){scriptDirectory=require("path").dirname(scriptDirectory)+"/"}else{scriptDirectory=__dirname+"/"}read_=function shell_read(filename,binary){if(!nodeFS)nodeFS=require("fs");if(!nodePath)nodePath=require("path");filename=nodePath["normalize"](filename);return nodeFS["readFileSync"](filename,binary?null:"utf8")};readBinary=function readBinary(filename){var ret=read_(filename,true);if(!ret.buffer){ret=new Uint8Array(ret)}assert(ret.buffer);return ret};if(process["argv"].length>1){thisProgram=process["argv"][1].replace(/\\/g,"/")}arguments_=process["argv"].slice(2);if(typeof module!=="undefined"){module["exports"]=Module}process["on"]("uncaughtException",function(ex){if(!(ex instanceof ExitStatus)){throw ex}});process["on"]("unhandledRejection",abort);quit_=function(status){process["exit"](status)};Module["inspect"]=function(){return"[Emscripten Module object]"}}else if(ENVIRONMENT_IS_SHELL){if(typeof read!="undefined"){read_=function shell_read(f){return read(f)}}readBinary=function readBinary(f){var data;if(typeof readbuffer==="function"){return new Uint8Array(readbuffer(f))}data=read(f,"binary");assert(typeof data==="object");return data};if(typeof scriptArgs!="undefined"){arguments_=scriptArgs}else if(typeof arguments!="undefined"){arguments_=arguments}if(typeof quit==="function"){quit_=function(status){quit(status)}}if(typeof print!=="undefined"){if(typeof console==="undefined")console={};console.log=print;console.warn=console.error=typeof printErr!=="undefined"?printErr:print}}else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(document.currentScript){scriptDirectory=document.currentScript.src}if(scriptDirectory.indexOf("blob:")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.lastIndexOf("/")+1)}else{scriptDirectory=""}{read_=function shell_read(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=function readBinary(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=function readAsync(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=function xhr_onload(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=function(title){document.title=title}}else{}var out=Module["print"]||console.log.bind(console);var err=Module["printErr"]||console.warn.bind(console);for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=null;if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"];if(Module["quit"])quit_=Module["quit"];var wasmBinary;if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];var noExitRuntime;if(Module["noExitRuntime"])noExitRuntime=Module["noExitRuntime"];if(typeof WebAssembly!=="object"){err("no native wasm support detected")}function getValue(ptr,type,noSafe){type=type||"i8";if(type.charAt(type.length-1)==="*")type="i32";switch(type){case"i1":return HEAP8[ptr>>0];case"i8":return HEAP8[ptr>>0];case"i16":return HEAP16[ptr>>1];case"i32":return HEAP32[ptr>>2];case"i64":return HEAP32[ptr>>2];case"float":return HEAPF32[ptr>>2];case"double":return HEAPF64[ptr>>3];default:abort("invalid type for getValue: "+type)}return null}var wasmMemory;var wasmTable=new WebAssembly.Table({"initial":3,"maximum":3+0,"element":"anyfunc"});var ABORT=false;var EXITSTATUS=0;function assert(condition,text){if(!condition){abort("Assertion failed: "+text)}}var UTF8Decoder=typeof TextDecoder!=="undefined"?new TextDecoder("utf8"):undefined;function UTF8ArrayToString(heap,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heap[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heap.subarray&&UTF8Decoder){return UTF8Decoder.decode(heap.subarray(idx,endPtr))}else{var str="";while(idx<endPtr){var u0=heap[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heap[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heap[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heap[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}}return str}function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):""}var WASM_PAGE_SIZE=65536;var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBufferAndViews(buf){buffer=buf;Module["HEAP8"]=HEAP8=new Int8Array(buf);Module["HEAP16"]=HEAP16=new Int16Array(buf);Module["HEAP32"]=HEAP32=new Int32Array(buf);Module["HEAPU8"]=HEAPU8=new Uint8Array(buf);Module["HEAPU16"]=HEAPU16=new Uint16Array(buf);Module["HEAPU32"]=HEAPU32=new Uint32Array(buf);Module["HEAPF32"]=HEAPF32=new Float32Array(buf);Module["HEAPF64"]=HEAPF64=new Float64Array(buf)}var DYNAMIC_BASE=72928,DYNAMICTOP_PTR=7232;var INITIAL_INITIAL_MEMORY=Module["INITIAL_MEMORY"]||1048576;if(Module["wasmMemory"]){wasmMemory=Module["wasmMemory"]}else{wasmMemory=new WebAssembly.Memory({"initial":INITIAL_INITIAL_MEMORY/WASM_PAGE_SIZE,"maximum":INITIAL_INITIAL_MEMORY/WASM_PAGE_SIZE})}if(wasmMemory){buffer=wasmMemory.buffer}INITIAL_INITIAL_MEMORY=buffer.byteLength;updateGlobalBufferAndViews(buffer);HEAP32[DYNAMICTOP_PTR>>2]=DYNAMIC_BASE;function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback=="function"){callback(Module);continue}var func=callback.func;if(typeof func==="number"){if(callback.arg===undefined){Module["dynCall_v"](func)}else{Module["dynCall_vi"](func,callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}var __ATPRERUN__=[];var __ATINIT__=[];var __ATMAIN__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function preMain(){callRuntimeCallbacks(__ATMAIN__)}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module["preloadedImages"]={};Module["preloadedAudios"]={};function abort(what){if(Module["onAbort"]){Module["onAbort"](what)}what+="";out(what);err(what);ABORT=true;EXITSTATUS=1;what="abort("+what+"). Build with -s ASSERTIONS=1 for more info.";throw new WebAssembly.RuntimeError(what)}function hasPrefix(str,prefix){return String.prototype.startsWith?str.startsWith(prefix):str.indexOf(prefix)===0}var dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(filename){return hasPrefix(filename,dataURIPrefix)}var fileURIPrefix="file://";function isFileURI(filename){return hasPrefix(filename,fileURIPrefix)}var wasmBinaryFile="vorbisdecoder.wasm";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(){try{if(wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(wasmBinaryFile)}else{throw"both async and sync fetching of the wasm failed"}}catch(err){abort(err)}}function getBinaryPromise(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)&&typeof fetch==="function"&&!isFileURI(wasmBinaryFile)){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){if(!response["ok"]){throw"failed to load wasm binary file at '"+wasmBinaryFile+"'"}return response["arrayBuffer"]()}).catch(function(){return getBinary()})}return new Promise(function(resolve,reject){resolve(getBinary())})}function createWasm(){var info={"a":asmLibraryArg};function receiveInstance(instance,module){var exports=instance.exports;Module["asm"]=exports;removeRunDependency("wasm-instantiate")}addRunDependency("wasm-instantiate");function receiveInstantiatedSource(output){receiveInstance(output["instance"])}function instantiateArrayBuffer(receiver){return getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(receiver,function(reason){err("failed to asynchronously prepare wasm: "+reason);abort(reason)})}function instantiateAsync(){if(!wasmBinary&&typeof WebAssembly.instantiateStreaming==="function"&&!isDataURI(wasmBinaryFile)&&!isFileURI(wasmBinaryFile)&&typeof fetch==="function"){fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,info);return result.then(receiveInstantiatedSource,function(reason){err("wasm streaming compile failed: "+reason);err("falling back to ArrayBuffer instantiation");instantiateArrayBuffer(receiveInstantiatedSource)})})}else{return instantiateArrayBuffer(receiveInstantiatedSource)}}if(Module["instantiateWasm"]){try{var exports=Module["instantiateWasm"](info,receiveInstance);return exports}catch(e){err("Module.instantiateWasm callback failed with error: "+e);return false}}instantiateAsync();return{}}__ATINIT__.push({func:function(){___wasm_call_ctors()}});function ___assert_fail(condition,filename,line,func){abort("Assertion failed: "+UTF8ToString(condition)+", at: "+[filename?UTF8ToString(filename):"unknown filename",line,func?UTF8ToString(func):"unknown function"])}function _emscripten_memcpy_big(dest,src,num){HEAPU8.copyWithin(dest,src,src+num)}function abortOnCannotGrowMemory(requestedSize){abort("OOM")}function _emscripten_resize_heap(requestedSize){requestedSize=requestedSize>>>0;abortOnCannotGrowMemory(requestedSize)}var asmLibraryArg={"a":___assert_fail,"b":_emscripten_memcpy_big,"c":_emscripten_resize_heap,"memory":wasmMemory,"table":wasmTable};var asm=createWasm();Module["asm"]=asm;var ___wasm_call_ctors=Module["___wasm_call_ctors"]=function(){return(___wasm_call_ctors=Module["___wasm_call_ctors"]=Module["asm"]["d"]).apply(null,arguments)};var _stb_vorbis_close=Module["_stb_vorbis_close"]=function(){return(_stb_vorbis_close=Module["_stb_vorbis_close"]=Module["asm"]["e"]).apply(null,arguments)};var _free=Module["_free"]=function(){return(_free=Module["_free"]=Module["asm"]["f"]).apply(null,arguments)};var _stb_vorbis_decode_frame_pushdata=Module["_stb_vorbis_decode_frame_pushdata"]=function(){return(_stb_vorbis_decode_frame_pushdata=Module["_stb_vorbis_decode_frame_pushdata"]=Module["asm"]["g"]).apply(null,arguments)};var _stb_vorbis_open_pushdata=Module["_stb_vorbis_open_pushdata"]=function(){return(_stb_vorbis_open_pushdata=Module["_stb_vorbis_open_pushdata"]=Module["asm"]["h"]).apply(null,arguments)};var _malloc=Module["_malloc"]=function(){return(_malloc=Module["_malloc"]=Module["asm"]["i"]).apply(null,arguments)};Module["asm"]=asm;Module["getValue"]=getValue;var calledRun;function ExitStatus(status){this.name="ExitStatus";this.message="Program terminated with exit("+status+")";this.status=status}dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(args){args=args||arguments_;if(runDependencies>0){return}preRun();if(runDependencies>0)return;function doRun(){if(calledRun)return;calledRun=true;Module["calledRun"]=true;if(ABORT)return;initRuntime();preMain();if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else{doRun()}}Module["run"]=run;if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}noExitRuntime=true;run();class VorbisDecoder{constructor(context,decodeHandler,errorHandler){VorbisDecoder.id=(VorbisDecoder.id||0)%1e3;this.id=VorbisDecoder.id++;this.context=context;this.decodeHandler=decodeHandler;this.errorHandler=errorHandler;VorbisDecoder.instances=VorbisDecoder.instances||[];VorbisDecoder.instances[this.id]=this}destroy(){const message={id:this.id,destroy:true};VorbisDecoder.worker.postMessage(message);delete VorbisDecoder.instances[this.id]}send(arrayBuffer,isLoaded){const message={id:this.id,arrayBuffer:arrayBuffer,isLoaded:isLoaded};VorbisDecoder.worker.postMessage(message)}onMessage(message){const channels=message.data.length;if(channels>0){const length=message.data[0].length;const buffer=this.context.createBuffer(channels,length,message.sampleRate);for(let i=0;i<channels;i++){buffer.getChannelData(i).set(message.data[i])}this.decodeHandler(buffer)}else{this.errorHandler()}}}if(typeof WorkerGlobalScope==="function"){class VorbisWorkerDecoder{constructor(id){this.v=null;this.id=id}onMessage(message){if(message.arrayBuffer){if(this.isOpen()){this.replace(message.arrayBuffer)}else{this.open(message.arrayBuffer)}}if(message.isLoaded){this.isLoaded=message.isLoaded}this.decode()}open(arrayBuffer){this.arrayBuffer=arrayBuffer;this.isLoaded=false;this.byteOffset=0;this.channels=2;this.bufferMax=8192;this.bufferPtr=Module._malloc(this.bufferMax);this.usedPtr=Module._malloc(4);this.outputPtr=Module._malloc(4);this.samplesPtr=Module._malloc(4);this.makeBuffer();this.v=Module._stb_vorbis_open_pushdata(this.bufferPtr,this.bufferSize,this.usedPtr,null,null);this.byteOffset+=Module.getValue(this.usedPtr,"i32");this.channels=Module.getValue(this.v+4,"i32");this.sampleRate=Module.getValue(this.v+0,"i32");this.sampleScale=1;if(this.sampleRate>=8e3&&this.sampleRate<22050){this.sampleScale=Math.ceil(22050/this.sampleRate)}this.totalSamples=0;this.arrays=[];this.data=[];for(let i=0;i<this.channels;i++){this.arrays[i]=[];this.data[i]=null}}isOpen(){return this.v!==null}close(){if(this.isOpen()){Module._stb_vorbis_close(this.v);Module._free(this.bufferPtr);Module._free(this.usedPtr);Module._free(this.outputPtr);Module._free(this.samplesPtr);this.v=null}this.arrayBuffer=null;this.dispose()}dispose(){this.totalSamples=0;for(let i=0;i<this.channels;i++){this.arrays[i]=[];this.data[i]=null}}replace(arrayBuffer){this.arrayBuffer=arrayBuffer}makeBuffer(){const totalBytes=this.arrayBuffer.byteLength;const remainingBytes=totalBytes-this.byteOffset;this.bufferSize=Math.min(remainingBytes,this.bufferMax);const array=new Uint8Array(this.arrayBuffer,this.byteOffset,this.bufferSize);Module.HEAPU8.set(array,this.bufferPtr)}decodeFrame(){this.makeBuffer();const used=Module._stb_vorbis_decode_frame_pushdata(this.v,this.bufferPtr,this.bufferSize,null,this.outputPtr,this.samplesPtr);if(used>0){const output=Module.getValue(this.outputPtr,"i32");const samples=Module.getValue(this.samplesPtr,"i32");this.byteOffset+=used;for(let i=0;i<this.channels;i++){const output_i=Module.getValue(output+i*4,"i32");const array=new Float32Array(Module.HEAPU8.buffer,output_i,samples);this.arrays[i].push(this.resample(array))}this.totalSamples+=samples;return true}else{return false}}decode(){if(this.isOpen()){for(;;){if(!this.decodeFrame()){if(this.isLoaded){this.sendBack();this.close()}break}if(this.totalSamples>=this.sampleRate*5){this.sendBack();setTimeout(this.decode.bind(this));break}}}}resample(array){if(this.sampleScale===1){return array.slice()}else{const scale=this.sampleScale;const expanded=new Float32Array(array.length*scale);for(let i=0;i<array.length;i++){for(let j=0;j<scale;j++){expanded[i*scale+j]=array[i]}}return expanded}}concatenate(){for(let i=0;i<this.channels;i++){this.data[i]=new Float32Array(this.totalSamples*this.sampleScale);let offset=0;for(const array of this.arrays[i]){this.data[i].set(array,offset);offset+=array.length}}}sendBack(){if(this.totalSamples>0){this.concatenate();const message={id:this.id,sampleRate:this.sampleRate*this.sampleScale,data:this.data};const transfer=[];for(let i=0;i<this.channels;i++){transfer.push(this.data[i].buffer)}postMessage(message,transfer);this.dispose()}}}VorbisWorkerDecoder.instances=[];self.addEventListener("message",e=>{const id=e.data.id;const instances=VorbisWorkerDecoder.instances;if(!instances[id]){instances[id]=new VorbisWorkerDecoder(id)}if(instances[id]){instances[id].onMessage(e.data)}if(e.data.destroy){instances[id].close();delete instances[id]}})}else{VorbisDecoder.instances=[];VorbisDecoder.worker=new Worker(document.currentScript.src);VorbisDecoder.worker.addEventListener("message",e=>{const instances=VorbisDecoder.instances;const id=e.data.id;if(instances[id]){instances[id].onMessage(e.data)}})}
