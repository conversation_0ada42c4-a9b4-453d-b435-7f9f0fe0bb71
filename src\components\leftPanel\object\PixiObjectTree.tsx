import { Box } from "@mui/material";
import React, { useState, useEffect, useCallback, useMemo } from "react";
import VirtualizedTreeList from "./VirtualizedTreeList";
import { TreeNode } from "../../../utils/tree/TreeNodeTypes";
import { buildTree } from "../../../utils/tree/TreeUtils";
import { flattenTree } from "../../../utils/tree/TreeFlattenUtils";
import useProjectStore from "../../../store/Store";

interface PixiObjectTreeProps { }

const PixiObjectTree: React.FC<PixiObjectTreeProps> = () => {
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  console.log("PixiObjectTree...");

  // 获取 setScene 函数
  const setScene = useProjectStore(state => state.setScene);

  // 更新对象树 - 直接从window获取游戏对象
  const updateTree = useCallback(() => {
    // console.log("更新对象树");

    try {
      // 直接从window获取SceneManager和Graphics
      const SceneManager = (window as any).SceneManager;
      const Graphics = (window as any).Graphics;

      if (!SceneManager || !SceneManager._scene) {
        console.warn("updateTree: SceneManager或当前场景不存在");
        return;
      }

      // 获取当前场景
      const currentScene = SceneManager._scene;

      if (currentScene) {
        // 更新 Store 中的 scene 属性
        setScene(currentScene);
        console.log(`更新 Store 中的场景: ${currentScene.constructor?.name}`);

        // 构建树结构
        // console.log("找到场景，构建树结构:", currentScene.constructor?.name);
        const newTreeData = buildTree(currentScene);
        // console.log("新树结构:", newTreeData);

        // 使用函数式更新，确保状态变化
        setTreeData(() => {
          // console.log("更新树数据:", newTreeData);
          // 强制创建新的数组引用，确保React检测到状态变化
          return [...newTreeData];
        });

        // 自动展开场景节点
        if (newTreeData.length > 0) {
          setExpandedNodes(prev => {
            const newSet = new Set(prev);
            newSet.add(newTreeData[0].id); // 添加场景节点ID到展开集合
            return newSet;
          });
        }
      } else {
        // 尝试从Graphics获取stage
        // console.log("未找到当前场景，尝试从Graphics获取stage");
        const pixiApp = Graphics?._app;

        if (pixiApp && pixiApp.stage) {
          // 更新 Store 中的 scene 属性为 stage
          setScene(pixiApp.stage);
          console.log(`更新 Store 中的场景为 Graphics stage`);

          const newTreeData = buildTree(pixiApp.stage);

          setTreeData(() => {
            // console.log("从Graphics更新树数据:", newTreeData);
            return [...newTreeData];
          });

          // 自动展开场景节点
          if (newTreeData.length > 0) {
            setExpandedNodes(prev => {
              const newSet = new Set(prev);
              newSet.add(newTreeData[0].id);
              return newSet;
            });
          }
        } else {
          console.warn("无法找到可用的场景或stage");
          // 清除 Store 中的 scene 属性
          setScene(null);
        }
      }
    } catch (error) {
      console.error("Error updating tree:", error);
    }
  }, [setScene]);

  // 监听游戏场景切换
  useEffect(() => {
    // 直接从window获取SceneManager
    const SceneManager = (window as any).SceneManager;

    if (!SceneManager) {
      console.warn("SceneManager不存在，无法监听场景切换");
      return;
    }

    // 保存原始goto方法
    const originalGoto = SceneManager.goto;

    // 重写goto方法以监听场景切换
    SceneManager.goto = function (...args: any[]) {
      // 调用原始方法
      originalGoto.apply(this, args);
      console.log("场景切换，更新对象树");

      // 在场景切换后多次尝试更新对象树，确保能捕获到新场景
      // 第一次更新 - 立即尝试
      updateTree();
    };

    // 保存原始update方法
    const originalUpdate = SceneManager.update;

    // 重写update方法以监听场景更新
    SceneManager.update = function (...args: any[]) {
      // 调用原始方法
      originalUpdate.apply(this, args);

      // 检查当前场景是否变化
      const currentScene = this._scene;
      if (currentScene && currentScene.constructor?.name) {
        const sceneName = currentScene.constructor.name;

        // 如果场景名称变化，更新对象树
        if (this._previousSceneName !== sceneName) {
          console.log(`场景从 ${this._previousSceneName} 变为 ${sceneName}，更新对象树`);
          this._previousSceneName = sceneName;

          // 延迟更新，确保场景完全加载
          setTimeout(() => {
            updateTree();
          }, 500);
        }
      }
    };

    // 初始化前一个场景名称
    SceneManager._previousSceneName = SceneManager._scene?.constructor?.name || '';

    // 初始加载时更新对象树
    setTimeout(() => {
      updateTree();
    }, 500);

    // 清理函数
    return () => {
      // 恢复原始方法
      if (SceneManager) {
        SceneManager.goto = originalGoto;
        SceneManager.update = originalUpdate;
      }
    };
  }, [updateTree]);

  // // 监听全局update-tree事件
  // useEffect(() => {
  //   // 注册全局update-tree事件监听器
  //   const handleUpdateTree = () => {
  //     console.log("PixiObjectTree: 收到update-tree事件，更新对象树");
  //     updateTree();
  //   };

  //   // 使用Store的事件系统注册监听器
  //   const { on, off } = useProjectStore.getState();
  //   on('update-tree', handleUpdateTree);

  //   // 清理函数
  //   return () => {
  //     off('update-tree', handleUpdateTree);
  //   };
  // }, [updateTree]);

  // 处理展开/收起
  const handleToggleExpand = useCallback((nodeId: string) => {
    setExpandedNodes((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  }, []);

  // 使用 flattenTree 将树结构扁平化，便于虚拟化渲染
  const flattenedNodes = useMemo(() => {
    return flattenTree(treeData, expandedNodes);
  }, [treeData, expandedNodes]);

  return (
    <Box sx={{ width: "100%", height: "100%", overflow: "auto" }}>
      {/* 添加刷新按钮 */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'flex-end',
        padding: '4px',
        borderBottom: '1px solid #e0e0e0'
      }}>
        <button
          onClick={() => {
            console.log("手动刷新场景树");
            updateTree();
          }}
          style={{
            padding: '4px 8px',
            fontSize: '12px',
            cursor: 'pointer',
            backgroundColor: '#f5f5f5',
            border: '1px solid #ccc',
            borderRadius: '4px'
          }}
        >
          刷新场景树
        </button>
      </Box>

      <VirtualizedTreeList
        nodes={flattenedNodes}
        expandedNodes={expandedNodes}
        onToggleExpand={handleToggleExpand}
        updateTree={updateTree}
        itemHeight={36}
        overscan={10}
      />
    </Box>
  );
};

export default PixiObjectTree;
