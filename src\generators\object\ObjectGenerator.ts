/**
 * 对象生命周期代码生成器
 * 负责生成对象创建和删除的JavaScript代码
 */

import { BaseGenerator } from '../core/BaseGenerator';
import {
  OperationInfo,
  ObjectCreationInfo,
  ObjectDeletionInfo,
  CodeSnippet,
  OperationType,
  ObjectGeneratorConfig,
  ObjectTypeNotSupportedError
} from '../core/types';
import {
  isObjectCreation,
  isObjectDeletion,
  getRealObjectType,
  isTypeOperation,
  validateOperationInfo
} from '../core/utils';

/**
 * 对象生成器主类
 */
export class ObjectGenerator extends BaseGenerator {
  private creationGenerator: CreationGenerator;
  private deletionGenerator: DeletionGenerator;

  constructor(config: Partial<ObjectGeneratorConfig> = {}) {
    super({
      addTypeMarkers: true,
      generateUniqueNames: true,
      ...config
    });

    const objectConfig: ObjectGeneratorConfig = {
      addTypeMarkers: true,
      generateUniqueNames: true,
      debug: false,
      minify: false,
      addComments: true,
      variablePrefix: 'rpgEditor_',
      indentSize: 2,
      ...config
    };

    this.creationGenerator = new CreationGenerator(objectConfig);
    this.deletionGenerator = new DeletionGenerator(objectConfig);
  }

  /**
   * 检查是否可以处理该操作
   */
  canHandle(operation: OperationInfo): boolean {
    return isObjectCreation(operation) || isObjectDeletion(operation);
  }

  /**
   * 生成对象生命周期代码
   */
  generate(operation: OperationInfo): CodeSnippet {
    if (!this.canHandle(operation)) {
      throw new Error('ObjectGenerator 只能处理对象创建和删除操作');
    }

    this.validateOperation(operation);

    if (isObjectCreation(operation)) {
      return this.creationGenerator.generate(operation as ObjectCreationInfo, this.context);
    } else if (isObjectDeletion(operation)) {
      return this.deletionGenerator.generate(operation as ObjectDeletionInfo, this.context);
    }

    throw new Error('未知的对象操作类型');
  }
}

// ==================== 对象创建生成器 ====================

/**
 * 对象创建代码生成器
 */
export class CreationGenerator {
  private config: ObjectGeneratorConfig;
  private supportedTypes: Set<string>;

  constructor(config: ObjectGeneratorConfig) {
    this.config = config;
    this.supportedTypes = new Set([
      'Sprite', 'Container', 'Graphics', 'Text', 'BitmapText',
      'Window', 'Window_Base', 'Window_Selectable', 'Window_Command'
    ]);
  }

  /**
   * 生成对象创建代码
   */
  generate(operation: ObjectCreationInfo, context: any): CodeSnippet {
    const { objectType, parentPath, initialProperties, objectName } = operation;

    if (!this.supportedTypes.has(objectType)) {
      throw new ObjectTypeNotSupportedError(objectType);
    }

    const variableName = this.generateObjectVariableName(operation, context);
    const parentVariableName = this.generateParentVariableName(parentPath);

    const code = [
      this.generateParentLookup(parentPath, parentVariableName),
      this.generateObjectCreation(objectType, variableName, initialProperties),
      this.generateTypeMarkers(operation, variableName),
      this.generateParentAttachment(parentVariableName, variableName),
      this.generateDebugLog(objectType, objectName || 'unnamed', variableName)
    ].filter(Boolean).join('\n\n');

    return {
      code,
      description: `创建 ${objectType} 对象${objectName ? ` (${objectName})` : ''}`,
      dependencies: ['findObjectByScenePath']
    };
  }

  /**
   * 生成对象变量名
   */
  private generateObjectVariableName(operation: ObjectCreationInfo, context: any): string {
    const { objectType, objectName } = operation;
    const timestamp = Date.now() % 10000;
    const baseName = objectName || objectType.toLowerCase();
    return `child_${baseName}_${timestamp}`;
  }

  /**
   * 生成父对象变量名
   */
  private generateParentVariableName(parentPath: string[]): string {
    return `parent_${parentPath.join('_').replace(/[^a-zA-Z0-9]/g, '_')}`;
  }

  /**
   * 生成父对象查找代码
   */
  private generateParentLookup(parentPath: string[], variableName: string): string {
    const pathArray = parentPath.map(p => `"${p}"`).join(', ');
    return [
      `// 查找父对象`,
      `const ${variableName}Path = [${pathArray}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`,
      `if (DEBUG) console.log('找到父对象:', ${variableName} ? ${variableName}.constructor.name : 'null');`
    ].join('\n');
  }

  /**
   * 生成对象创建代码
   */
  private generateObjectCreation(objectType: string, variableName: string, properties: Record<string, any>): string {
    const lines = [
      `// 创建 ${objectType} 对象`,
      `const ${variableName} = new ${objectType}();`
    ];

    // 设置初始属性
    if (properties && Object.keys(properties).length > 0) {
      lines.push('// 设置初始属性');
      Object.entries(properties).forEach(([key, value]) => {
        lines.push(`${variableName}.${key} = ${this.serializeValue(value)};`);
      });
    }

    return lines.join('\n');
  }

  /**
   * 生成类型标记代码
   */
  private generateTypeMarkers(operation: ObjectCreationInfo, variableName: string): string {
    if (!this.config.addTypeMarkers) return '';

    const lines = [
      '// 添加对象标记',
      `${variableName}._isCustomObject = true;`,
      `${variableName}._createdAt = new Date().toISOString();`,
      `${variableName}._objectType = "${operation.objectType}";`
    ];

    // 如果是类型操作，添加类型标记
    if (isTypeOperation(operation)) {
      const typePath = operation.targetObject._rpgEditorPath || [operation.className];
      lines.push(
        `${variableName}._rpgEditorTypeCreated = true;`,
        `${variableName}._rpgEditorParentType = "${operation.className}";`,
        `${variableName}._rpgEditorPath = [${typePath.map(p => `"${p}"`).join(', ')}];`
      );
    }

    return lines.join('\n');
  }

  /**
   * 生成父对象附加代码
   */
  private generateParentAttachment(parentVariableName: string, childVariableName: string): string {
    return [
      '// 添加到父对象',
      `if (${parentVariableName}) {`,
      `    ${parentVariableName}.addChild(${childVariableName});`,
      `    if (DEBUG) console.log('子对象已添加到父对象');`,
      '} else {',
      `    console.warn('父对象未找到，无法添加子对象');`,
      '}'
    ].join('\n');
  }

  /**
   * 生成调试日志
   */
  private generateDebugLog(objectType: string, objectName: string, variableName: string): string {
    return `if (DEBUG) console.log('创建对象完成:', '${objectType}', '${objectName}', ${variableName});`;
  }

  /**
   * 序列化值
   */
  private serializeValue(value: any): string {
    if (typeof value === 'string') return `"${value}"`;
    if (typeof value === 'number' || typeof value === 'boolean') return String(value);
    if (value === null) return 'null';
    if (value === undefined) return 'undefined';
    if (Array.isArray(value)) {
      return `[${value.map(v => this.serializeValue(v)).join(', ')}]`;
    }
    if (typeof value === 'object') {
      const pairs = Object.entries(value).map(([k, v]) => `${k}: ${this.serializeValue(v)}`);
      return `{${pairs.join(', ')}}`;
    }
    return JSON.stringify(value);
  }
}

// ==================== 对象删除生成器 ====================

/**
 * 对象删除代码生成器
 */
export class DeletionGenerator {
  private config: ObjectGeneratorConfig;

  constructor(config: ObjectGeneratorConfig) {
    this.config = config;
  }

  /**
   * 生成对象删除代码
   */
  generate(operation: ObjectDeletionInfo, context: any): CodeSnippet {
    const { targetPath, className } = operation;
    const variableName = this.generateVariableName(targetPath, className);
    const parentPath = targetPath.slice(0, -1);
    const parentVariableName = parentPath.length > 0 ?
      this.generateParentVariableName(parentPath) : null;

    const code = [
      this.generateObjectLookup(targetPath, variableName),
      parentVariableName ? this.generateParentLookup(parentPath, parentVariableName) : null,
      this.generateDeletionCode(variableName, parentVariableName),
      this.generateDebugLog(variableName)
    ].filter(Boolean).join('\n\n');

    return {
      code,
      description: `删除 ${className} 对象`,
      dependencies: ['findObjectByScenePath']
    };
  }

  /**
   * 生成变量名
   */
  private generateVariableName(targetPath: string[], className: string): string {
    return `target_${className.toLowerCase()}_${targetPath.join('_').replace(/[^a-zA-Z0-9]/g, '_')}`;
  }

  /**
   * 生成父对象变量名
   */
  private generateParentVariableName(parentPath: string[]): string {
    return `parent_${parentPath.join('_').replace(/[^a-zA-Z0-9]/g, '_')}`;
  }

  /**
   * 生成对象查找代码
   */
  private generateObjectLookup(targetPath: string[], variableName: string): string {
    const pathArray = targetPath.map(p => `"${p}"`).join(', ');
    return [
      `// 查找要删除的对象`,
      `const ${variableName}Path = [${pathArray}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`,
      `if (DEBUG) console.log('找到要删除的对象:', ${variableName} ? ${variableName}.constructor.name : 'null');`
    ].join('\n');
  }

  /**
   * 生成父对象查找代码
   */
  private generateParentLookup(parentPath: string[], variableName: string): string {
    const pathArray = parentPath.map(p => `"${p}"`).join(', ');
    return [
      `// 查找父对象`,
      `const ${variableName}Path = [${pathArray}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`
    ].join('\n');
  }

  /**
   * 生成删除代码
   */
  private generateDeletionCode(variableName: string, parentVariableName: string | null): string {
    const lines = [
      `// 删除对象`,
      `if (${variableName}) {`
    ];

    if (parentVariableName) {
      lines.push(
        `    // 从父对象中移除`,
        `    if (${parentVariableName} && ${parentVariableName}.removeChild) {`,
        `        ${parentVariableName}.removeChild(${variableName});`,
        `    }`
      );
    }

    lines.push(
      `    // 清理对象引用`,
      `    if (${variableName}.destroy) {`,
      `        ${variableName}.destroy();`,
      `    }`,
      `    if (DEBUG) console.log('对象已删除');`,
      `} else {`,
      `    console.warn('要删除的对象未找到');`,
      `}`
    );

    return lines.join('\n');
  }

  /**
   * 生成调试日志
   */
  private generateDebugLog(variableName: string): string {
    return `if (DEBUG) console.log('删除操作完成:', ${variableName});`;
  }
}
