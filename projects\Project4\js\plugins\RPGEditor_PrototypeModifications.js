/*:
 * @target MZ
 * @plugindesc RPG Editor - 自动生成的原型链修改插件
 * <AUTHOR> Editor
 * @help
 * 此插件由RPG Editor自动生成，包含对游戏对象原型链的修改。
 * 修改仅应用于满足特定条件的对象，而不是所有同类型的对象。
 */

(function() {
    'use strict';
    // 保存原始的drawText方法
    // 添加调试标志
    const DEBUG = true;


    // 调试日志函数
    function log(...args) {
        if (DEBUG) {
            console.log('[RPGEditor]', ...args);
        }
    }
    Scene_Base.prototype.createWindowLayer = function () {
        this._windowLayer = new WindowLayer();
        if (this._windowLayer.x === 4) this._windowLayer.x = (Graphics.width - Graphics.boxWidth) / 2;
        if (this._windowLayer.y === 4) this._windowLayer.y = (Graphics.height - Graphics.boxHeight) / 2;
        this.addChild(this._windowLayer);
    };
    // 根据场景路径查找对象
    function findObjectByScenePath(path) {
        if (DEBUG) log('查找对象，路径:', path);
        if (!path || path.length < 1) return null;

        // 获取当前场景
        const scene = SceneManager._scene;
        if (!scene) {
            if (DEBUG) log('当前场景不存在');
            return null;
        }

        // 检查场景名称是否匹配
        const sceneName = path[0];
        if (scene.constructor.name !== sceneName) {
            if (DEBUG) log('场景类型不匹配:', scene.constructor.name, '!=', sceneName);
            return null;
        }

        // 如果路径只有一个元素（场景名称），则返回场景本身
        if (path.length === 1) {
            if (DEBUG) log('返回场景本身:', scene.constructor.name);
            return scene;
        }

        // 递归查找嵌套对象
        let currentObject = scene;

        // 从第二个元素开始遍历路径（都是索引）
        for (let i = 1; i < path.length; i++) {
            const index = parseInt(path[i]);

            // 检查索引是否有效
            if (isNaN(index) || !currentObject.children || index >= currentObject.children.length) {
                if (DEBUG) log('索引无效或超出范围:', index, '子对象数量:', currentObject.children ? currentObject.children.length : 0, '在路径位置', i);
                return null;
            }

            // 获取下一级对象
            currentObject = currentObject.children[index];

            // 如果对象为空，则中断查找
            if (!currentObject) {
                if (DEBUG) log('在索引', index, '处找不到对象，在路径位置', i);
                return null;
            }

            if (DEBUG) log('找到中间对象:', currentObject.constructor.name, '在索引', index, '在路径位置', i);
        }

        if (DEBUG) log('最终找到对象:', currentObject ? currentObject.constructor.name : 'null', '完整路径:', path.join('/'));
        return currentObject;
    }

    // 原型链修改
    // 重写 WindowLayer 类型的 initialize 方法
    if (typeof WindowLayer !== 'undefined') {
        if (DEBUG) log('重写 WindowLayer 类型的 initialize 方法');
        
        const originalWindowLayer_initialize = WindowLayer.prototype.initialize;
        
        WindowLayer.prototype.initialize = function() {
            // 调用原始 initialize 方法
            originalWindowLayer_initialize.apply(this, arguments);
            
            // 为所有 WindowLayer 类型实例添加 Label 子对象: 新Label
            if (DEBUG) log('为所有 WindowLayer 实例添加 Label 子对象: 新Label');
            
            const child_label = new Sprite();
            child_label.bitmap = new Bitmap(200, 40);
            child_label.alpha = 1;
            if (child_label.anchor) child_label.anchor.x = 0;
            if (child_label.anchor) child_label.anchor.y = 0;
            child_label.height = 40;
            child_label.name = "新Label";
            child_label.rotation = 0;
            if (child_label.scale) child_label.scale.x = 1;
            if (child_label.scale) child_label.scale.y = 1;
            if (child_label.bitmap) {
                child_label.bitmap.drawText("新文本", 0, 0, child_label.bitmap.width, child_label.bitmap.height, 'left');
            }
            child_label.tint = 16777215;
            child_label.visible = true;
            child_label.width = 200;
            child_label.x = 0;
            child_label.y = 0;
            // 添加类型创建标记
            child_label._rpgEditorTypeCreated = true;
            child_label._rpgEditorParentType = 'WindowLayer';
            child_label._rpgEditorObjectName = '新Label';
            child_label._rpgEditorObjectType = 'Label';
            child_label._rpgEditorPath = ['WindowLayer']; // 顶层是类型
            
            // 添加到父对象
            this.addChild(child_label);
            if (DEBUG) log('为 WindowLayer 实例添加了 Label 子对象: 新Label (已标记为类型创建)');
            
        };
        
        if (DEBUG) log('已重写 WindowLayer 类型的相关方法');
    } else {
        if (DEBUG) log('警告: WindowLayer 类型不存在，无法重写 initialize 方法');
    }
    
    // 修改 Scene_Title 场景
    const originalScene_Title_Start = Scene_Title.prototype.start;
    Scene_Title.prototype.start = function() {
        // 调用原始方法
        originalScene_Title_Start.apply(this, arguments);
        
        if (DEBUG) log('Scene_Title.start 被调用');
        
        // 修改路径为 Scene_Title/3/0 的 Sprite 对象
        const scenePath_sprite_Scene_Title_3_0_41832 = ["Scene_Title","3","0"];
        const targetObject_sprite_Scene_Title_3_0_41832 = findObjectByScenePath(scenePath_sprite_Scene_Title_3_0_41832);
        if (DEBUG) log('路径为 Scene_Title/3/0 的Sprite 对象:', targetObject_sprite_Scene_Title_3_0_41832 ? targetObject_sprite_Scene_Title_3_0_41832.constructor.name : 'null');
        if (targetObject_sprite_Scene_Title_3_0_41832) {
            // 应用属性修改
                // 创建 Button 对象: 新Button (类型操作已在 Rust 端过滤)
                const child_button_1834 = new Sprite();
                child_button_1834.bitmap = new Bitmap(100, 30);
                child_button_1834.alpha = 1;
                if (child_button_1834.anchor) child_button_1834.anchor.x = 0;
                if (child_button_1834.anchor) child_button_1834.anchor.y = 0;
                child_button_1834.height = 40;
                child_button_1834.name = "新Button";
                if (DEBUG) log('设置对象名称: "新Button"');
                child_button_1834.rotation = 0;
                if (child_button_1834.scale) child_button_1834.scale.x = 1;
                if (child_button_1834.scale) child_button_1834.scale.y = 1;
                child_button_1834.tint = 16777215;
                child_button_1834.visible = true;
                child_button_1834.width = 120;
                child_button_1834.x = 0;
                child_button_1834.y = 0;
                // 添加到父对象
                targetObject_sprite_Scene_Title_3_0_41832.addChild(child_button_1834);
                if (DEBUG) log('创建并添加 Button 对象: 新Button');
        }

    };

})();
