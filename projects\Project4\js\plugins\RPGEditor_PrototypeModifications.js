/*:
 * @target MZ
 * @plugindesc RPG Editor - 自动生成的原型链修改插件
 * <AUTHOR> Editor
 * @help
 * 此插件由RPG Editor自动生成，包含对游戏对象原型链的修改。
 * 修改仅应用于满足特定条件的对象，而不是所有同类型的对象。
 */

(function() {
    'use strict';
    // 保存原始的drawText方法
    // 添加调试标志
    const DEBUG = true;


    // 调试日志函数
    function log(...args) {
        if (DEBUG) {
            console.log('[RPGEditor]', ...args);
        }
    }
    Scene_Base.prototype.createWindowLayer = function () {
        this._windowLayer = new WindowLayer();
        if (this._windowLayer.x === 4) this._windowLayer.x = (Graphics.width - Graphics.boxWidth) / 2;
        if (this._windowLayer.y === 4) this._windowLayer.y = (Graphics.height - Graphics.boxHeight) / 2;
        this.addChild(this._windowLayer);
    };
    // 根据场景路径查找对象
    function findObjectByScenePath(path) {
        if (DEBUG) log('查找对象，路径:', path);
        if (!path || path.length < 1) return null;

        // 获取当前场景
        const scene = SceneManager._scene;
        if (!scene) {
            if (DEBUG) log('当前场景不存在');
            return null;
        }

        // 检查场景名称是否匹配
        const sceneName = path[0];
        if (scene.constructor.name !== sceneName) {
            if (DEBUG) log('场景类型不匹配:', scene.constructor.name, '!=', sceneName);
            return null;
        }

        // 如果路径只有一个元素（场景名称），则返回场景本身
        if (path.length === 1) {
            if (DEBUG) log('返回场景本身:', scene.constructor.name);
            return scene;
        }

        // 递归查找嵌套对象
        let currentObject = scene;

        // 从第二个元素开始遍历路径（都是索引）
        for (let i = 1; i < path.length; i++) {
            const index = parseInt(path[i]);

            // 检查索引是否有效
            if (isNaN(index) || !currentObject.children || index >= currentObject.children.length) {
                if (DEBUG) log('索引无效或超出范围:', index, '子对象数量:', currentObject.children ? currentObject.children.length : 0, '在路径位置', i);
                return null;
            }

            // 获取下一级对象
            currentObject = currentObject.children[index];

            // 如果对象为空，则中断查找
            if (!currentObject) {
                if (DEBUG) log('在索引', index, '处找不到对象，在路径位置', i);
                return null;
            }

            if (DEBUG) log('找到中间对象:', currentObject.constructor.name, '在索引', index, '在路径位置', i);
        }

        if (DEBUG) log('最终找到对象:', currentObject ? currentObject.constructor.name : 'null', '完整路径:', path.join('/'));
        return currentObject;
    }

    // 原型链修改
    // 重写 WindowLayer 类型的 initialize 方法
    if (typeof WindowLayer !== 'undefined') {
        if (DEBUG) log('重写 WindowLayer 类型的 initialize 方法');
        
        const originalWindowLayer_initialize = WindowLayer.prototype.initialize;
        
        WindowLayer.prototype.initialize = function() {
            // 调用原始 initialize 方法
            originalWindowLayer_initialize.apply(this, arguments);
            
            // 为所有 WindowLayer 类型实例添加 Button 子对象: 新Button
            if (DEBUG) log('为所有 WindowLayer 实例添加 Button 子对象: 新Button');
            
            const child_button = new Sprite();
            child_button.bitmap = new Bitmap(100, 30);
            child_button.alpha = 1;
            if (child_button.anchor) child_button.anchor.x = 0;
            if (child_button.anchor) child_button.anchor.y = 0;
            child_button.height = 40;
            child_button.name = "新Button";
            child_button.rotation = 0;
            if (child_button.scale) child_button.scale.x = 1;
            if (child_button.scale) child_button.scale.y = 1;
            child_button.tint = 16777215;
            child_button.visible = true;
            child_button.width = 120;
            child_button.x = 0;
            child_button.y = 0;
            // 添加到父对象
            this.addChild(child_button);
            if (DEBUG) log('为 WindowLayer 实例添加了 Button 子对象: 新Button');
            
        };
        
        if (DEBUG) log('已重写 WindowLayer 类型的相关方法');
    } else {
        if (DEBUG) log('警告: WindowLayer 类型不存在，无法重写 initialize 方法');
    }
    
    // 修改 Scene_Title 场景
    const originalScene_Title_Start = Scene_Title.prototype.start;
    Scene_Title.prototype.start = function() {
        // 调用原始方法
        originalScene_Title_Start.apply(this, arguments);
        
        if (DEBUG) log('Scene_Title.start 被调用');
        
        // 修改场景本身
            // Label 对象 '新Label' 的创建和删除操作已抵消，无需生成代码
            if (DEBUG) log('对象生命周期操作抵消: Label - 新Label');

        
        // 修改路径为 Scene_Title/2 的 Sprite 对象
        const scenePath_sprite_Scene_Title_2_11224 = ["Scene_Title","2"];
        const targetObject_sprite_Scene_Title_2_11224 = findObjectByScenePath(scenePath_sprite_Scene_Title_2_11224);
        if (DEBUG) log('路径为 Scene_Title/2 的Sprite 对象:', targetObject_sprite_Scene_Title_2_11224 ? targetObject_sprite_Scene_Title_2_11224.constructor.name : 'null');
        if (targetObject_sprite_Scene_Title_2_11224) {
            // 应用属性修改
                // Button 对象 '新Button' 的创建和删除操作已抵消，无需生成代码
                if (DEBUG) log('对象生命周期操作抵消: Button - 新Button');
        }

        // 修改路径为 Scene_Title/3/0 的 Sprite 对象
        const scenePath_sprite_Scene_Title_3_0_11226 = ["Scene_Title","3","0"];
        const targetObject_sprite_Scene_Title_3_0_11226 = findObjectByScenePath(scenePath_sprite_Scene_Title_3_0_11226);
        if (DEBUG) log('路径为 Scene_Title/3/0 的Sprite 对象:', targetObject_sprite_Scene_Title_3_0_11226 ? targetObject_sprite_Scene_Title_3_0_11226.constructor.name : 'null');
        if (targetObject_sprite_Scene_Title_3_0_11226) {
            // 应用属性修改
            targetObject_sprite_Scene_Title_3_0_11226.height = 34;
            if (DEBUG) log('设置属性 height =', 34);
            targetObject_sprite_Scene_Title_3_0_11226.x = 15;
            if (DEBUG) log('设置属性 x =', 15);
            targetObject_sprite_Scene_Title_3_0_11226.y = 46;
            if (DEBUG) log('设置属性 y =', 46);
            targetObject_sprite_Scene_Title_3_0_11226.width = 191;
            if (DEBUG) log('设置属性 width =', 191);        }

        // 修改路径为 Scene_Title/3 的 WindowLayer 对象
        const scenePath_windowlayer_Scene_Title_3_11230 = ["Scene_Title","3"];
        const targetObject_windowlayer_Scene_Title_3_11230 = findObjectByScenePath(scenePath_windowlayer_Scene_Title_3_11230);
        if (DEBUG) log('路径为 Scene_Title/3 的WindowLayer 对象:', targetObject_windowlayer_Scene_Title_3_11230 ? targetObject_windowlayer_Scene_Title_3_11230.constructor.name : 'null');
        if (targetObject_windowlayer_Scene_Title_3_11230) {
            // 应用属性修改
                // 智能删除 Sprite 对象: 新Button
                (function() {
                    // 1. 首先检查是否有对应的创建缓存
                    let foundInCache = false;
                    const parentObject = targetObject_windowlayer_Scene_Title_3_11230;
                    if (!parentObject) {
                        if (DEBUG) log('父对象不存在，无法删除 Sprite 对象: 新Button');
                        return;
                    }
                
                    // 2. 查找最近创建的同类型对象（缓存检查）
                    const children = parentObject.children || [];
                    let targetChild = null;
                
                    // 3. 优先按名称精确匹配
                    for (let i = children.length - 1; i >= 0; i--) {
                        const child = children[i];
                        if (child && child.constructor.name === 'Sprite' && child.name === '新Button') {
                            targetChild = child;
                            foundInCache = true;
                            if (DEBUG) log('找到缓存中的对象（按名称）: Sprite - 新Button');
                            break;
                        }
                    }
                
                
                    // 5. 执行删除操作
                    if (targetChild) {
                        const parent = targetChild.parent;
                        if (parent && parent.removeChild) {
                            parent.removeChild(targetChild);
                            if (DEBUG) log('成功删除 Sprite 对象: 新Button (' + (foundInCache ? '缓存中的' : '路径中的') + ')');
                        } else {
                            if (DEBUG) log('无法删除对象，父对象无效: 新Button');
                        }
                    } else {
                        if (DEBUG) log('未找到要删除的 Sprite 对象: 新Button');
                    }
                })();
        }

    };

})();
