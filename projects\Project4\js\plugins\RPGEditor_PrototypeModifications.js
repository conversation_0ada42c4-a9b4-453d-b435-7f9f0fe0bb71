/*:
 * @target MZ
 * @plugindesc RPG Editor - 自动生成的原型链修改插件
 * <AUTHOR> Editor
 * @help
 * 此插件由RPG Editor自动生成，包含对游戏对象原型链的修改。
 * 修改仅应用于满足特定条件的对象，而不是所有同类型的对象。
 */

(function() {
    'use strict';
    // 保存原始的drawText方法
    // 添加调试标志
    const DEBUG = true;


    // 调试日志函数
    function log(...args) {
        if (DEBUG) {
            console.log('[RPGEditor]', ...args);
        }
    }

    // 根据场景路径查找对象
    function findObjectByScenePath(path) {
        if (DEBUG) log('查找对象，路径:', path);
        if (!path || path.length < 1) return null;

        // 获取当前场景
        const scene = SceneManager._scene;
        if (!scene) {
            if (DEBUG) log('当前场景不存在');
            return null;
        }

        // 检查场景名称是否匹配
        const sceneName = path[0];
        if (scene.constructor.name !== sceneName) {
            if (DEBUG) log('场景类型不匹配:', scene.constructor.name, '!=', sceneName);
            return null;
        }

        // 如果路径只有一个元素（场景名称），则返回场景本身
        if (path.length === 1) {
            if (DEBUG) log('返回场景本身:', scene.constructor.name);
            return scene;
        }

        // 递归查找嵌套对象
        let currentObject = scene;

        // 从第二个元素开始遍历路径（都是索引）
        for (let i = 1; i < path.length; i++) {
            const index = parseInt(path[i]);

            // 检查索引是否有效
            if (isNaN(index) || !currentObject.children || index >= currentObject.children.length) {
                if (DEBUG) log('索引无效或超出范围:', index, '子对象数量:', currentObject.children ? currentObject.children.length : 0, '在路径位置', i);
                return null;
            }

            // 获取下一级对象
            currentObject = currentObject.children[index];

            // 如果对象为空，则中断查找
            if (!currentObject) {
                if (DEBUG) log('在索引', index, '处找不到对象，在路径位置', i);
                return null;
            }

            if (DEBUG) log('找到中间对象:', currentObject.constructor.name, '在索引', index, '在路径位置', i);
        }

        if (DEBUG) log('最终找到对象:', currentObject ? currentObject.constructor.name : 'null', '完整路径:', path.join('/'));
        return currentObject;
    }

    // 原型链修改
    // 重写 WindowLayer 类型的 initialize 方法
    if (typeof WindowLayer !== 'undefined') {
        if (DEBUG) log('重写 WindowLayer 类型的 initialize 方法');
        
        const originalWindowLayer_initialize = WindowLayer.prototype.initialize;
        
        WindowLayer.prototype.initialize = function() {
            // 调用原始 initialize 方法
            originalWindowLayer_initialize.apply(this, arguments);
            
            // 设置自定义 x 属性值
            if (DEBUG) log('WindowLayer initialize: 设置前 x =', this.x);
            this.x = -52;
            if (DEBUG) log('WindowLayer initialize: 设置后 x =', this.x);
            
            // 延迟验证属性值
            setTimeout(() => {
                if (DEBUG) log('WindowLayer 延迟检查: x =', this.x);
                if (this.x !== -52) {
                    if (DEBUG) log('WindowLayer 属性被覆盖，重新设置');
                    this.x = -52;
                }
            }, 0);
        };
        
        // 重写 move 方法以保持自定义位置
        const originalWindowLayer_move = WindowLayer.prototype.move;
        WindowLayer.prototype.move = function(x, y, width, height) {
            if (DEBUG) log('WindowLayer move 被调用: x=', x, ', y=', y, ', w=', width, ', h=', height);
            // 保持自定义 x 值
            const originalx = x;
            // 调用原始 move 方法
            originalWindowLayer_move.apply(this, arguments);
            // 重新设置自定义 x 值
            this.x = -52;
            if (DEBUG) log('WindowLayer move: 重新设置 x =', -52);
        };
        
        // 重写 Scene_Base.createWindowLayer 以防止位置被重置
        if (typeof Scene_Base !== 'undefined') {
            const originalScene_Base_createWindowLayer = Scene_Base.prototype.createWindowLayer;
            Scene_Base.prototype.createWindowLayer = function() {
                if (DEBUG) log('Scene_Base.createWindowLayer 被调用');
                // 调用原始方法
                originalScene_Base_createWindowLayer.call(this);
                // 重新应用自定义位置
                if (this._windowLayer) {
                    this._windowLayer.x = -52;
                    if (DEBUG) log('Scene_Base.createWindowLayer: 重新设置 WindowLayer.x =', -52);
                }
            };
        }
        
        if (DEBUG) log('已重写 WindowLayer 类型的相关方法');
    } else {
        if (DEBUG) log('警告: WindowLayer 类型不存在，无法重写 initialize 方法');
    }
    
    // 修改 Scene_Title 场景
    const originalScene_Title_Start = Scene_Title.prototype.start;
    Scene_Title.prototype.start = function() {
        // 调用原始方法
        originalScene_Title_Start.apply(this, arguments);
        
        if (DEBUG) log('Scene_Title.start 被调用');
        
        // 修改场景本身
            // Button 对象 '新Button' 的创建和删除操作已抵消，无需生成代码
            if (DEBUG) log('对象生命周期操作抵消: Button - 新Button');

        
        // 修改路径为 Scene_Title/3 的 WindowLayer 对象
        const scenePath_windowlayer_Scene_Title_3_33101 = ["Scene_Title","3"];
        const targetObject_windowlayer_Scene_Title_3_33101 = findObjectByScenePath(scenePath_windowlayer_Scene_Title_3_33101);
        if (DEBUG) log('路径为 Scene_Title/3 的WindowLayer 对象:', targetObject_windowlayer_Scene_Title_3_33101 ? targetObject_windowlayer_Scene_Title_3_33101.constructor.name : 'null');
        if (targetObject_windowlayer_Scene_Title_3_33101) {
            // 应用属性修改
                // Label 对象 '新Label' 的创建和删除操作已抵消，无需生成代码
                if (DEBUG) log('对象生命周期操作抵消: Label - 新Label');
        }

    };

})();
