var $plugins =
[
  {
    "description": "通过ID管理游戏对象的插件",
    "name": "ObjectManager",
    "parameters": {},
    "status": true
  },
  {
    "description": "ObjectManager插件的测试插件",
    "name": "ObjectManagerTest",
    "parameters": {},
    "status": true
  },
  {
    "description": "RPG Editor - 自动生成的原型链修改插件",
    "name": "RPGEditor_PrototypeModifications",
    "parameters": {},
    "status": true
  },
  {
    "description": "跟踪并记录Bitmap上的文本和图像绘制操作",
    "name": "RPGEditor_BitmapTracker",
    "parameters": {},
    "status": true
  },
  {
    "description": "显示鼠标悬停对象的继承关系",
    "name": "showClasses",
    "parameters": {
      "backgroundColor": "rgba(0, 0, 0, 0.7)",
      "displayDuration": "60",
      "textColor": "rgba(255, 255, 255, 1.0)",
      "textSize": "16"
    },
    "status": true
  },
  {
    "description": "RPG Editor - 自动生成的插件",
    "name": "RPGEditor_UnifiedModifications",
    "parameters": {},
    "status": true
  }
];
