import React, { useState, useRef, useEffect } from "react";
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  SelectChangeEvent,
  Chip,
  Paper,
  Tooltip,
  IconButton,
  Fade
} from "@mui/material";
import { styled } from "@mui/material/styles";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import CheckIcon from "@mui/icons-material/Check";
import useProjectStore from "../../store/Store";
import { setIgnoreNextClick } from "../../utils/common/GlobalEventHandler";

// 选项类型定义
export interface SelectOption {
  value: string;
  label: string;
  color?: string;
  icon?: React.ReactNode;
}

// 组件属性类型定义
interface SelectInputProps {
  label?: string;
  value: string;
  options: SelectOption[];
  onChange: (value: string) => void;
  width?: string | number;
  disabled?: boolean;
  variant?: "standard" | "outlined" | "filled";
  size?: "small" | "medium";
  object?: any;
  propertyName?: string;
  showSelectedIcon?: boolean;
  compact?: boolean;
}

// 自定义样式的Select容器
const StyledFormControl = styled(FormControl)(({ theme }) => ({
  "& .MuiOutlinedInput-root": {
    transition: "all 0.2s",
    "&:hover": {
      "& .MuiOutlinedInput-notchedOutline": {
        borderColor: theme.palette.primary.main,
      },
    },
    "&.Mui-focused": {
      "& .MuiOutlinedInput-notchedOutline": {
        borderColor: theme.palette.primary.main,
        borderWidth: 2,
      },
    },
  },
  "& .MuiInputLabel-root": {
    fontSize: "0.875rem",
    "&.Mui-focused": {
      color: theme.palette.primary.main,
    },
  },
  "& .MuiSelect-select": {
    display: "flex",
    alignItems: "center",
    gap: theme.spacing(1),
  },
}));

// 自定义样式的MenuItem
const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  gap: theme.spacing(1),
  padding: theme.spacing(1, 2),
  "&:hover": {
    backgroundColor: theme.palette.action.hover,
  },
  "&.Mui-selected": {
    backgroundColor: `${theme.palette.primary.light}20`,
    "&:hover": {
      backgroundColor: `${theme.palette.primary.light}30`,
    },
  },
}));

// 紧凑模式的选择器
const CompactSelect = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  cursor: "pointer",
  padding: theme.spacing(0.5, 1),
  borderRadius: theme.shape.borderRadius,
  border: `1px solid ${theme.palette.divider}`,
  transition: "all 0.2s",
  "&:hover": {
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.action.hover,
  },
}));

// 选择器组件
const SelectInput: React.FC<SelectInputProps> = ({
  label,
  value,
  options,
  onChange,
  width = "100%",
  disabled = false,
  variant = "outlined",
  size = "small",
  object,
  propertyName,
  showSelectedIcon = true,
  compact = false,
}) => {
  // 状态
  const [open, setOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<SelectOption | null>(null);
  const [showTooltip, setShowTooltip] = useState(false);

  // 获取添加历史记录的方法
  const addToHistory = useProjectStore(state => state.addToHistory);

  // 引用
  const selectRef = useRef<HTMLDivElement>(null);
  const compactSelectRef = useRef<HTMLDivElement>(null);
  const tooltipTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 当value或options变化时，更新selectedOption
  useEffect(() => {
    const option = options.find(opt => opt.value === value);
    setSelectedOption(option || null);
  }, [value, options]);

  // 处理选择变化
  const handleChange = (event: SelectChangeEvent<string>) => {
    // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
    setIgnoreNextClick(true);
    console.debug('SelectInput: 设置忽略下一次点击事件（handleChange）');

    const newValue = event.target.value;

    // 如果有对象和属性名，记录历史
    if (object && propertyName) {
      const oldValue = object[propertyName];
      const operation = {
        id: Date.now().toString(),
        operation_type: "MODIFY_PROPERTY",
        timestamp: Date.now(),
        object_path: object._objectPath || "unknown",
        details: {
          propertyName,
          oldValue,
          newValue
        }
      };
      addToHistory(operation);
    }

    onChange(newValue);

    // 显示成功提示
    setShowTooltip(true);
    if (tooltipTimeoutRef.current) {
      clearTimeout(tooltipTimeoutRef.current);
    }
    tooltipTimeoutRef.current = setTimeout(() => {
      setShowTooltip(false);
    }, 1500);

    // 延迟一段时间后再次设置标记，以处理可能的后续点击事件
    setTimeout(() => {
      setIgnoreNextClick(true);
      console.debug('SelectInput: 再次设置忽略下一次点击事件（延迟）');
    }, 100);
  };

  // 处理打开/关闭下拉菜单
  const handleOpen = () => {
    if (!disabled) {
      // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
      setIgnoreNextClick(true);
      console.debug('SelectInput: 设置忽略下一次点击事件');

      setOpen(true);
    }
  };

  const handleClose = () => {
    // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
    setIgnoreNextClick(true);
    console.debug('SelectInput: 设置忽略下一次点击事件');

    setOpen(false);
  };

  // 紧凑模式渲染
  if (compact) {
    return (
      <Box sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        width,
        mb: 1
      }}>
        {label && (
          <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
            {label}
          </Typography>
        )}

        <Box sx={{ position: "relative" }}>
          <CompactSelect
            ref={compactSelectRef}
            onClick={(e) => {
              // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
              setIgnoreNextClick(true);
              console.debug('SelectInput: 设置忽略下一次点击事件（CompactSelect点击）');

              handleOpen();
            }}
            sx={{
              opacity: disabled ? 0.6 : 1,
              pointerEvents: disabled ? "none" : "auto",
              width: label ? "auto" : width
            }}
          >
            {selectedOption?.icon && (
              <Box sx={{ mr: 0.5, display: "flex", alignItems: "center" }}>
                {selectedOption.icon}
              </Box>
            )}
            <Typography variant="body2" noWrap>
              {selectedOption?.label || "选择..."}
            </Typography>
            <ExpandMoreIcon
              fontSize="small"
              sx={{
                ml: 0.5,
                transition: "transform 0.2s",
                transform: open ? "rotate(180deg)" : "rotate(0deg)"
              }}
            />
          </CompactSelect>

          <Tooltip
            open={showTooltip}
            title="已更新"
            placement="top"
            arrow
            TransitionComponent={Fade}
            TransitionProps={{ timeout: 300 }}
          >
            <IconButton
              size="small"
              sx={{
                position: "absolute",
                top: -10,
                right: -10,
                backgroundColor: "success.main",
                color: "white",
                width: 20,
                height: 20,
                opacity: showTooltip ? 1 : 0,
                transition: "opacity 0.3s",
                "&:hover": {
                  backgroundColor: "success.dark",
                },
                pointerEvents: "none"
              }}
            >
              <CheckIcon fontSize="small" />
            </IconButton>
          </Tooltip>

          <Select
            ref={selectRef}
            value={value}
            onChange={handleChange}
            open={open}
            onOpen={(e) => {
              // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
              setIgnoreNextClick(true);
              console.debug('SelectInput: 设置忽略下一次点击事件（紧凑模式onOpen）');

              handleOpen();
            }}
            onClose={(e) => {
              // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
              setIgnoreNextClick(true);
              console.debug('SelectInput: 设置忽略下一次点击事件（紧凑模式onClose）');

              handleClose();
            }}
            onClick={() => {
              // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
              setIgnoreNextClick(true);
              console.debug('SelectInput: 设置忽略下一次点击事件（紧凑模式Select点击）');
            }}
            sx={{
              position: "absolute",
              opacity: 0,
              pointerEvents: "none",
              width: 0,
              height: 0
            }}
            MenuProps={{
              PaperProps: {
                elevation: 3,
                sx: {
                  maxHeight: 300,
                  width: 200,
                  zIndex: 9999
                },
                onClick: () => {
                  // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
                  setIgnoreNextClick(true);
                  console.debug('SelectInput: 设置忽略下一次点击事件（紧凑模式MenuProps点击）');
                }
              },
              onClick: () => {
                // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
                setIgnoreNextClick(true);
                console.debug('SelectInput: 设置忽略下一次点击事件（紧凑模式MenuProps.onClick）');
              }
            }}
          >
            {options.map((option) => (
              <StyledMenuItem
                key={option.value}
                value={option.value}
                onClick={() => {
                  // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
                  setIgnoreNextClick(true);
                  console.debug('SelectInput: 设置忽略下一次点击事件（紧凑模式MenuItem点击）');
                }}
              >
                {option.icon && (
                  <Box
                    sx={{ display: "flex", alignItems: "center" }}
                    onClick={() => {
                      // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
                      setIgnoreNextClick(true);
                      console.debug('SelectInput: 设置忽略下一次点击事件（紧凑模式MenuItem图标点击）');
                    }}
                  >
                    {option.icon}
                  </Box>
                )}
                <Typography
                  variant="body2"
                  onClick={() => {
                    // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
                    setIgnoreNextClick(true);
                    console.debug('SelectInput: 设置忽略下一次点击事件（紧凑模式MenuItem文本点击）');
                  }}
                >
                  {option.label}
                </Typography>
                {showSelectedIcon && option.value === value && (
                  <CheckIcon
                    fontSize="small"
                    color="primary"
                    sx={{ ml: "auto" }}
                    onClick={() => {
                      // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
                      setIgnoreNextClick(true);
                      console.debug('SelectInput: 设置忽略下一次点击事件（紧凑模式MenuItem图标点击）');
                    }}
                  />
                )}
              </StyledMenuItem>
            ))}
          </Select>
        </Box>
      </Box>
    );
  }

  // 标准模式渲染
  return (
    <StyledFormControl
      variant={variant}
      size={size}
      fullWidth
      disabled={disabled}
      sx={{ width, mb: 1 }}
    >
      {label && <InputLabel>{label}</InputLabel>}
      <Select
        value={value}
        onChange={handleChange}
        label={label}
        MenuProps={{
          PaperProps: {
            elevation: 3,
            sx: { maxHeight: 300 },
            onClick: () => {
              // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
              setIgnoreNextClick(true);
              console.debug('SelectInput: 设置忽略下一次点击事件（MenuProps点击）');
            }
          }
        }}
        IconComponent={(props) => (
          <ExpandMoreIcon
            {...props}
            sx={{
              transition: "transform 0.2s",
              transform: open ? "rotate(180deg)" : "rotate(0deg)"
            }}
            onClick={(e) => {
              // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
              setIgnoreNextClick(true);
              console.debug('SelectInput: 设置忽略下一次点击事件（IconComponent点击）');

              // 阻止事件冒泡
              e.stopPropagation();
            }}
          />
        )}
        onOpen={(e) => {
          // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
          setIgnoreNextClick(true);
          console.debug('SelectInput: 设置忽略下一次点击事件（onOpen）');

          setOpen(true);
        }}
        onClose={(e) => {
          // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
          setIgnoreNextClick(true);
          console.debug('SelectInput: 设置忽略下一次点击事件（onClose）');

          setOpen(false);
        }}
        onClick={() => {
          // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
          setIgnoreNextClick(true);
          console.debug('SelectInput: 设置忽略下一次点击事件（Select点击）');
        }}
      >
        {options.map((option) => (
          <StyledMenuItem
            key={option.value}
            value={option.value}
            onClick={() => {
              // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
              setIgnoreNextClick(true);
              console.debug('SelectInput: 设置忽略下一次点击事件（标准模式MenuItem点击）');
            }}
          >
            {option.icon && (
              <Box
                sx={{ display: "flex", alignItems: "center" }}
                onClick={() => {
                  // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
                  setIgnoreNextClick(true);
                  console.debug('SelectInput: 设置忽略下一次点击事件（标准模式MenuItem图标点击）');
                }}
              >
                {option.icon}
              </Box>
            )}
            <Typography
              variant="body2"
              onClick={() => {
                // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
                setIgnoreNextClick(true);
                console.debug('SelectInput: 设置忽略下一次点击事件（标准模式MenuItem文本点击）');
              }}
            >
              {option.label}
            </Typography>
            {showSelectedIcon && option.value === value && (
              <CheckIcon
                fontSize="small"
                color="primary"
                sx={{ ml: "auto" }}
                onClick={() => {
                  // 设置标记，告诉GlobalEventHandler忽略下一次点击事件
                  setIgnoreNextClick(true);
                  console.debug('SelectInput: 设置忽略下一次点击事件（标准模式MenuItem图标点击）');
                }}
              />
            )}
          </StyledMenuItem>
        ))}
      </Select>
    </StyledFormControl>
  );
};

export default SelectInput;
