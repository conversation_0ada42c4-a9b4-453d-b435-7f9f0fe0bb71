
import { invoke } from '@tauri-apps/api/core';
import useProjectStore from '../../store/Store';
import { getObjectPath } from '../../utils/object/objectPro';

// ==================== 滤镜相关方法 ====================

/**
 * 记录滤镜参数修改
 * @param object 要修改的对象
 * @param filterIndex 滤镜在数组中的索引
 * @param paramName 参数名称
 * @param value 新值
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export const recordFilterParamModification = async (
  object: any,
  filterIndex: number,
  paramName: string,
  value: any
): Promise<string> => {
  try {
    // 获取对象的类名
    const className = object.constructor.name;

    // 获取当前场景
    const scene = useProjectStore.getState().scene;

    // 如果没有场景，则不记录修改
    if (!scene) {
      console.warn('无法获取当前场景，不记录滤镜参数修改');
      return '无法获取当前场景，不记录滤镜参数修改';
    }

    // 调用后端 API 记录滤镜参数修改
    const result = await invoke('record_filter_param_modification', {
      objectPath: getObjectPath(object),
      className: className,
      filterIndex: filterIndex,
      paramName: paramName,
      value: value
    });

    console.log('记录滤镜参数修改结果:', result);
    return result as string;
  } catch (error) {
    console.error('记录滤镜参数修改失败:', error);
    throw error;
  }
};

/**
 * 记录添加滤镜操作
 * @param object 要修改的对象
 * @param filterType 滤镜类型
 * @param filterParams 滤镜参数
 * @param insertIndex 插入位置索引（可选，默认添加到末尾）
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export const recordAddFilter = async (
  object: any,
  filterType: string,
  filterParams: Record<string, any>,
  insertIndex?: number
): Promise<string> => {
  try {
    // 获取对象的类名
    const className = object.constructor.name;

    // 获取当前场景
    const scene = useProjectStore.getState().scene;

    // 如果没有场景，则不记录修改
    if (!scene) {
      console.warn('无法获取当前场景，不记录添加滤镜操作');
      return '无法获取当前场景，不记录添加滤镜操作';
    }

    // 调用后端 API 记录添加滤镜操作
    const result = await invoke('record_add_filter', {
      objectPath: getObjectPath(object),
      className: className,
      filterType: filterType,
      filterParams: filterParams,
      insertIndex: insertIndex
    });

    console.log('记录添加滤镜操作结果:', result);
    return result as string;
  } catch (error) {
    console.error('记录添加滤镜操作失败:', error);
    throw error;
  }
};

/**
 * 记录删除滤镜操作
 * @param object 要修改的对象
 * @param filterIndex 要删除的滤镜索引
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export const recordRemoveFilter = async (
  object: any,
  filterIndex: number
): Promise<string> => {
  try {
    // 获取对象的类名
    const className = object.constructor.name;

    // 获取当前场景
    const scene = useProjectStore.getState().scene;

    // 如果没有场景，则不记录修改
    if (!scene) {
      console.warn('无法获取当前场景，不记录删除滤镜操作');
      return '无法获取当前场景，不记录删除滤镜操作';
    }

    // 调用后端 API 记录删除滤镜操作
    const result = await invoke('record_remove_filter', {
      objectPath: getObjectPath(object),
      className: className,
      filterIndex: filterIndex
    });

    console.log('记录删除滤镜操作结果:', result);
    return result as string;
  } catch (error) {
    console.error('记录删除滤镜操作失败:', error);
    throw error;
  }
};

/**
 * 记录滤镜数组重新排序操作
 * @param object 要修改的对象
 * @param newOrder 新的滤镜索引顺序数组
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export const recordReorderFilters = async (
  object: any,
  newOrder: number[]
): Promise<string> => {
  try {
    // 获取对象的类名
    const className = object.constructor.name;

    // 获取当前场景
    const scene = useProjectStore.getState().scene;

    // 如果没有场景，则不记录修改
    if (!scene) {
      console.warn('无法获取当前场景，不记录滤镜重排操作');
      return '无法获取当前场景，不记录滤镜重排操作';
    }

    // 调用后端 API 记录滤镜重排操作
    const result = await invoke('record_reorder_filters', {
      objectPath: getObjectPath(object),
      className: className,
      newOrder: newOrder
    });

    console.log('记录滤镜重排操作结果:', result);
    return result as string;
  } catch (error) {
    console.error('记录滤镜重排操作失败:', error);
    throw error;
  }
};
