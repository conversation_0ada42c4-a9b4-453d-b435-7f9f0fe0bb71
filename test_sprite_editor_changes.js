// 测试SpriteEditorDialog中的变化检测功能
console.log("测试SpriteEditorDialog变化检测功能");

// 模拟原始elements数据
const originalElements = [
  {
    type: 'text',
    text: '原始文本',
    x: 100,
    y: 50,
    maxWidth: 200,
    lineHeight: 24,
    align: 'left',
    bounds: { x: 100, y: 50, width: 200, height: 24 }
  },
  {
    type: 'image',
    dx: 200,
    dy: 100,
    dw: 144,
    dh: 96,
    sx: 0,
    sy: 0,
    sw: 144,
    sh: 96,
    source: {
      _url: '../projects/Project4/img/characters/SF_People2.png',
      width: 576,
      height: 384
    },
    bounds: { x: 200, y: 100, width: 144, height: 96 }
  }
];

// 模拟新的elements数据（有变化）
const newElements = [
  {
    type: 'text',
    text: '修改后的文本', // 文本内容变化
    x: 100,
    y: 50,
    maxWidth: 200,
    lineHeight: 24,
    align: 'left',
    bounds: { x: 100, y: 50, width: 200, height: 24 }
  },
  {
    type: 'image',
    dx: 250, // 位置变化
    dy: 120, // 位置变化
    dw: 144,
    dh: 96,
    sx: 0,
    sy: 0,
    sw: 144,
    sh: 96,
    source: {
      _url: '../projects/Project4/img/characters/SF_People2.png',
      width: 576,
      height: 384
    },
    bounds: { x: 250, y: 120, width: 144, height: 96 }
  },
  {
    type: 'text', // 新增的文本元素
    text: '新增文本',
    x: 300,
    y: 200,
    maxWidth: 150,
    lineHeight: 20,
    align: 'center',
    bounds: { x: 300, y: 200, width: 150, height: 20 }
  }
];

// 模拟cleanElementsForSerialization函数
const cleanElementsForSerialization = (elements) => {
  return elements.map(element => {
    const cleanElement = { ...element };
    
    if (element.type === 'image' && element.source) {
      // 对于图像元素，只保留必要的source信息
      cleanElement.source = {
        _url: element.source._url,
        width: element.source.width,
        height: element.source.height,
      };
    }
    
    return cleanElement;
  });
};

// 模拟detectElementsChanges函数
const detectElementsChanges = (originalElements, newElements) => {
  const changes = [];

  // 创建元素映射，用于快速查找
  const originalMap = new Map();
  const newMap = new Map();

  // 为原始元素创建唯一标识
  originalElements.forEach((element, index) => {
    const key = element.type === 'text' 
      ? `text_${element.x}_${element.y}_${element.text}` 
      : `image_${element.dx}_${element.dy}_${element.dw}_${element.dh}`;
    originalMap.set(key, { element, index });
  });

  // 为新元素创建唯一标识
  newElements.forEach((element, index) => {
    const key = element.type === 'text' 
      ? `text_${element.x}_${element.y}_${element.text}` 
      : `image_${element.dx}_${element.dy}_${element.dw}_${element.dh}`;
    newMap.set(key, { element, index });
  });

  // 检测新增的元素
  newMap.forEach((newItem, key) => {
    if (!originalMap.has(key)) {
      changes.push({
        type: 'added',
        elementType: newItem.element.type,
        index: newItem.index,
        element: newItem.element
      });
    }
  });

  // 检测删除的元素
  originalMap.forEach((originalItem, key) => {
    if (!newMap.has(key)) {
      changes.push({
        type: 'removed',
        elementType: originalItem.element.type,
        index: originalItem.index,
        element: originalItem.element
      });
    }
  });

  // 检测修改的元素（基于位置匹配）
  originalElements.forEach((originalElement, originalIndex) => {
    const matchingNewElement = newElements.find((newElement) => {
      if (originalElement.type !== newElement.type) return false;
      
      if (originalElement.type === 'text') {
        // 文本元素：基于位置匹配
        return Math.abs(originalElement.x - newElement.x) < 5 && 
               Math.abs(originalElement.y - newElement.y) < 5;
      } else {
        // 图像元素：基于位置匹配
        return Math.abs(originalElement.dx - newElement.dx) < 5 && 
               Math.abs(originalElement.dy - newElement.dy) < 5;
      }
    });

    if (matchingNewElement) {
      const changedProperties = [];
      
      // 检测属性变化
      if (originalElement.type === 'text') {
        if (originalElement.text !== matchingNewElement.text) changedProperties.push('text');
        if (originalElement.x !== matchingNewElement.x) changedProperties.push('x');
        if (originalElement.y !== matchingNewElement.y) changedProperties.push('y');
        if (originalElement.maxWidth !== matchingNewElement.maxWidth) changedProperties.push('maxWidth');
        if (originalElement.lineHeight !== matchingNewElement.lineHeight) changedProperties.push('lineHeight');
        if (originalElement.align !== matchingNewElement.align) changedProperties.push('align');
      } else {
        if (originalElement.dx !== matchingNewElement.dx) changedProperties.push('dx');
        if (originalElement.dy !== matchingNewElement.dy) changedProperties.push('dy');
        if (originalElement.dw !== matchingNewElement.dw) changedProperties.push('dw');
        if (originalElement.dh !== matchingNewElement.dh) changedProperties.push('dh');
        if (originalElement.sx !== matchingNewElement.sx) changedProperties.push('sx');
        if (originalElement.sy !== matchingNewElement.sy) changedProperties.push('sy');
        if (originalElement.sw !== matchingNewElement.sw) changedProperties.push('sw');
        if (originalElement.sh !== matchingNewElement.sh) changedProperties.push('sh');
      }

      if (changedProperties.length > 0) {
        changes.push({
          type: 'modified',
          elementType: originalElement.type,
          index: originalIndex,
          oldElement: originalElement,
          newElement: matchingNewElement,
          changedProperties
        });
      }
    }
  });

  return changes;
};

// 测试变化检测
console.log('\n=== 测试变化检测 ===');
const changes = detectElementsChanges(originalElements, newElements);
console.log('检测到的变化:', changes);

// 测试清理函数
console.log('\n=== 测试清理函数 ===');
const cleanedElements = cleanElementsForSerialization(newElements);
console.log('清理后的elements:', cleanedElements);

// 验证清理后的数据可以安全序列化
console.log('\n=== 测试JSON序列化 ===');
try {
  const serialized = JSON.stringify(cleanedElements);
  console.log('序列化成功，长度:', serialized.length);
  console.log('序列化结果预览:', serialized.substring(0, 200) + '...');
} catch (error) {
  console.error('序列化失败:', error);
}

console.log('\n=== 测试完成 ===');
