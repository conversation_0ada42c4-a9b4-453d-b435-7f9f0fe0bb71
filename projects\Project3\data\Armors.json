[null, {"id": 1, "atypeId": 1, "description": "", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 135, "name": "-----护甲", "note": "", "params": [0, 0, 0, 1, 0, 0, 0, 0], "price": 100}, {"id": 2, "atypeId": 1, "description": "【普通装备】拥有高透气性的亚麻制服装。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 135, "name": "亚麻服", "note": "", "params": [0, 0, 0, 1, 0, 0, 0, 0], "price": 100}, {"id": 3, "atypeId": 1, "description": "【普通装备】用鞣革皮制成的背心。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 135, "name": "皮革背心", "note": "", "params": [0, 0, 0, 3, 0, 0, 0, 0], "price": 200}, {"id": 4, "atypeId": 1, "description": "【普通装备】可以应付各种窘境的\n耐用服装。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 135, "name": "冒险者之服", "note": "", "params": [0, 0, 0, 7, 0, 0, 0, 0], "price": 550}, {"id": 5, "atypeId": 1, "description": "【普通装备】经胶水硬化的\n皮革制背心。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 135, "name": "硬皮革", "note": "", "params": [0, 0, 0, 15, 0, 0, 0, 0], "price": 980}, {"id": 6, "atypeId": 1, "description": "【普通装备】经钢板加固的\n皮革制服装。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 135, "name": "锁子甲", "note": "", "params": [0, 0, 0, 20, 0, 0, 0, 0], "price": 1560}, {"id": 7, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 8, "atypeId": 3, "description": "【轻装备】用坚韧布料制成的护甲。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 136, "name": "布甲", "note": "", "params": [0, 0, 0, 4, 0, 0, 0, 0], "price": 260}, {"id": 9, "atypeId": 3, "description": "【轻装备】用叠加皮层制成的护甲。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 136, "name": "皮甲", "note": "", "params": [0, 0, 0, 8, 0, 0, 0, 0], "price": 550}, {"id": 10, "atypeId": 3, "description": "【轻装备】使用青铜制成的胸甲。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 136, "name": "青铜胸甲", "note": "", "params": [0, 0, 0, 13, 0, 0, 0, 0], "price": 840}, {"id": 11, "atypeId": 3, "description": "【轻装备】使用钢铁制成的胸甲。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 136, "name": "钢铁胸甲", "note": "", "params": [0, 0, 0, 20, 0, 0, 0, 0], "price": 1280}, {"id": 12, "atypeId": 3, "description": "【轻装备】使用秘银制成的胸甲。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 136, "name": "秘银胸甲", "note": "", "params": [0, 0, 0, 27, 0, 0, 0, 0], "price": 2800}, {"id": 13, "atypeId": 3, "description": "【轻装备】使用龙鳞制成的胸甲。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 136, "name": "龙之胸甲", "note": "", "params": [0, 0, 0, 35, 0, 0, 0, 0], "price": 4680}, {"id": 14, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 15, "atypeId": 4, "description": "【重装备】用青铜制成的护甲。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 137, "name": "青铜护甲", "note": "", "params": [0, 0, 0, 15, 0, 0, 0, 0], "price": 680}, {"id": 16, "atypeId": 4, "description": "【重装备】用钢铁制成的护甲。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 137, "name": "钢铁护甲", "note": "", "params": [0, 0, 0, 24, 0, 0, 0, 0], "price": 1480}, {"id": 17, "atypeId": 4, "description": "【重装备】用秘银制成的护甲。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 137, "name": "秘银护甲", "note": "", "params": [0, 0, 0, 37, 0, 0, 0, 0], "price": 3250}, {"id": 18, "atypeId": 4, "description": "【重装备】用龙鳞制成的护甲。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 137, "name": "龙之护甲", "note": "", "params": [0, 0, 0, 50, 0, 0, 0, 0], "price": 5260}, {"id": 19, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 20, "atypeId": 2, "description": "【魔法装备】一种基本的，普遍流行的长袍。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 139, "name": "棉袍", "note": "", "params": [0, 0, 0, 4, 2, 3, 0, 0], "price": 150}, {"id": 21, "atypeId": 2, "description": "【魔法装备】由真丝制成的高质量斗篷。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 139, "name": "真丝斗篷", "note": "", "params": [0, 0, 0, 9, 4, 6, 0, 0], "price": 560}, {"id": 22, "atypeId": 2, "description": "【魔法装备】刻有符文字母的长袍。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 139, "name": "魔法师长袍", "note": "", "params": [0, 0, 0, 15, 7, 11, 0, 0], "price": 1220}, {"id": 23, "atypeId": 2, "description": "【魔法装备】与神奇力量交织在一起\n的长袍。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 139, "name": "智者长袍", "note": "", "params": [0, 0, 0, 21, 15, 18, 0, 0], "price": 2980}, {"id": 24, "atypeId": 2, "description": "【魔法装备】寄宿着精灵王之力的\n究极披风。", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 139, "name": "元素披风", "note": "", "params": [0, 0, 0, 28, 20, 32, 0, 0], "price": 5760}, {"id": 25, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 26, "atypeId": 1, "description": "", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 135, "name": "-----盾牌", "note": "", "params": [0, 0, 0, 1, 0, 0, 0, 0], "price": 100}, {"id": 27, "atypeId": 5, "description": "【小盾牌】用以抵御攻击的小盾牌。", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 129, "name": "护盾", "note": "", "params": [0, 0, 0, 5, 0, 0, 0, 0], "price": 300}, {"id": 28, "atypeId": 5, "description": "【小盾牌】以铁圈加固的\n木制圆盾。", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 129, "name": "圆形护盾", "note": "", "params": [0, 0, 0, 10, 0, 0, 0, 0], "price": 670}, {"id": 29, "atypeId": 5, "description": "【小盾牌】附有攻击性尖刺的\n圆形护盾。", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 129, "name": "尖刺护盾", "note": "", "params": [0, 0, 0, 15, 0, 0, 0, 0], "price": 1320}, {"id": 30, "atypeId": 5, "description": "【小盾牌】用秘银锻造而成的小盾牌。", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 129, "name": "秘银护盾", "note": "", "params": [0, 0, 0, 21, 0, 0, 0, 0], "price": 2750}, {"id": 31, "atypeId": 5, "description": "【小盾牌】用龙鳞锻造而成的小盾牌。", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 129, "name": "龙护盾", "note": "", "params": [0, 0, 0, 28, 0, 0, 0, 0], "price": 5280}, {"id": 32, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 33, "atypeId": 6, "description": "【大护盾】大而普通的木盾。", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 128, "name": "木盾", "note": "", "params": [0, 0, 0, 7, 0, 0, 0, 0], "price": 550}, {"id": 34, "atypeId": 6, "description": "【大护盾】由青铜板制成的大盾牌。", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 128, "name": "青铜盾牌", "note": "", "params": [0, 0, 0, 12, 0, 0, 0, 0], "price": 960}, {"id": 35, "atypeId": 6, "description": "【大护盾】由钢铁板制成的大盾牌。", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 128, "name": "钢铁盾牌", "note": "", "params": [0, 0, 0, 17, 0, 0, 0, 0], "price": 1480}, {"id": 36, "atypeId": 6, "description": "【大护盾】由秘银制成的大盾牌。", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 128, "name": "秘银盾牌", "note": "", "params": [0, 0, 0, 25, 0, 0, 0, 0], "price": 2980}, {"id": 37, "atypeId": 6, "description": "【大护盾】由龙鳞制成的大盾牌。", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 128, "name": "龙盾牌", "note": "", "params": [0, 0, 0, 38, 0, 0, 0, 0], "price": 6120}, {"id": 38, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 39, "atypeId": 2, "description": "【魔法装备】被赋予了守护魔法\n的手镯。", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 144, "name": "木手镯", "note": "", "params": [0, 0, 0, 2, 0, 3, 0, 0], "price": 300}, {"id": 40, "atypeId": 2, "description": "【魔法装备】被赋予了守护魔法\n的手镯。", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 144, "name": "青铜手镯", "note": "", "params": [0, 0, 0, 5, 0, 6, 0, 0], "price": 650}, {"id": 41, "atypeId": 2, "description": "【魔法装备】被赋予了守护魔法\n的手镯。", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 144, "name": "钢铁手镯", "note": "", "params": [0, 0, 0, 10, 0, 9, 0, 0], "price": 1280}, {"id": 42, "atypeId": 2, "description": "【魔法装备】被赋予了守护魔法\n的手镯。", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 144, "name": "秘银手镯", "note": "", "params": [0, 0, 0, 16, 0, 12, 0, 0], "price": 2680}, {"id": 43, "atypeId": 2, "description": "【魔法装备】被赋予了守护魔法\n的手镯。", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 144, "name": "龙手镯", "note": "", "params": [0, 0, 0, 23, 0, 15, 0, 0], "price": 4860}, {"id": 44, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 45, "atypeId": 1, "description": "", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 135, "name": "-----头盔", "note": "", "params": [0, 0, 0, 1, 0, 0, 0, 0], "price": 100}, {"id": 46, "atypeId": 1, "description": "【普通装备】用于保护前额的\n布头带。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 150, "name": "头巾", "note": "", "params": [0, 0, 0, 1, 0, 0, 0, 0], "price": 50}, {"id": 47, "atypeId": 1, "description": "【普通装备】用于保护前额的\n皮头带。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 150, "name": "皮革头巾", "note": "", "params": [0, 0, 0, 4, 0, 0, 0, 0], "price": 120}, {"id": 48, "atypeId": 1, "description": "【普通装备】用皮革制成的帽子。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 130, "name": "皮革帽子", "note": "", "params": [0, 0, 0, 8, 0, 0, 0, 0], "price": 480}, {"id": 49, "atypeId": 1, "description": "【普通装备】一块厚布，环绕头部进行包裹br>以对头部进行保护。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 130, "name": "包头巾", "note": "", "params": [0, 0, 0, 12, 0, 0, 0, 0], "price": 980}, {"id": 50, "atypeId": 1, "description": "【普通装备】用鸟的羽毛进行装饰的\n精美帽子。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 130, "name": "羽毛帽子", "note": "", "params": [0, 0, 0, 16, 0, 0, 0, 0], "price": 1890}, {"id": 51, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 52, "atypeId": 3, "description": "【轻装备】用鞣制的皮革制成的帽子。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 130, "name": "皮革软帽", "note": "", "params": [0, 0, 0, 3, 0, 0, 0, 0], "price": 100}, {"id": 53, "atypeId": 3, "description": "【轻装备】木制的轻头盔。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 130, "name": "木软帽", "note": "", "params": [0, 0, 0, 7, 0, 0, 0, 0], "price": 300}, {"id": 54, "atypeId": 3, "description": "【轻装备】用青铜板锻造而成的\n轻头盔。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 130, "name": "青铜软帽", "note": "", "params": [0, 0, 0, 12, 0, 0, 0, 0], "price": 720}, {"id": 55, "atypeId": 3, "description": "【轻装备】用钢铁板锻造而成的\n轻头盔。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 130, "name": "钢铁软帽", "note": "", "params": [0, 0, 0, 18, 0, 0, 0, 0], "price": 1680}, {"id": 56, "atypeId": 3, "description": "【轻装备】用秘银锻造而成的\n轻头盔。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 130, "name": "秘银软帽", "note": "", "params": [0, 0, 0, 24, 0, 0, 0, 0], "price": 3250}, {"id": 57, "atypeId": 3, "description": "【轻装备】用龙鳞锻造而成的\n轻头盔。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 130, "name": "龙软帽", "note": "", "params": [0, 0, 0, 30, 0, 0, 0, 0], "price": 5480}, {"id": 58, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 59, "atypeId": 4, "description": "【重装备】用细链编织而成的兜帽。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 132, "name": "锁子甲贴头帽", "note": "", "params": [0, 0, 0, 5, 0, 0, 0, 0], "price": 250}, {"id": 60, "atypeId": 4, "description": "【重装备】用厚青铜板制成\n的头盔。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 132, "name": "青铜头盔", "note": "", "params": [0, 0, 0, 12, 0, 0, 0, 0], "price": 750}, {"id": 61, "atypeId": 4, "description": "【重装备】用厚钢铁板制成\n的头盔。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 132, "name": "钢铁头盔", "note": "", "params": [0, 0, 0, 20, 0, 0, 0, 0], "price": 1580}, {"id": 62, "atypeId": 4, "description": "【重装备】用秘银制作\n的头盔。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 132, "name": "秘银头盔", "note": "", "params": [0, 0, 0, 28, 0, 0, 0, 0], "price": 3480}, {"id": 63, "atypeId": 4, "description": "【重装备】用龙鳞制成\n的头盔。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 132, "name": "龙头盔", "note": "", "params": [0, 0, 0, 34, 0, 0, 0, 0], "price": 6780}, {"id": 64, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 65, "atypeId": 2, "description": "【魔法装备】三角形的宽边鞣制\n皮革帽子。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 133, "name": "尖顶帽", "note": "", "params": [0, 0, 0, 2, 0, 5, 0, 0], "price": 100}, {"id": 66, "atypeId": 2, "description": "【魔法装备】被赋予了守护魔法的\n简朴头冠。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 133, "name": "环形饰品", "note": "", "params": [0, 0, 0, 5, 0, 10, 0, 0], "price": 360}, {"id": 67, "atypeId": 2, "description": "【魔法装备】被赋予了守护魔法的\n银制头冠。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 133, "name": "银质环形饰品", "note": "", "params": [0, 0, 0, 8, 0, 15, 0, 0], "price": 980}, {"id": 68, "atypeId": 2, "description": "【魔法装备】被赋予了守护魔法的\n黄金头冠。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 133, "name": "黄金环形饰品", "note": "", "params": [0, 0, 0, 16, 0, 25, 0, 0], "price": 1980}, {"id": 69, "atypeId": 2, "description": "【魔法装备】被赋予了守护魔法的\n秘银头冠。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 133, "name": "秘银环形饰品", "note": "", "params": [0, 0, 0, 20, 0, 30, 0, 0], "price": 3680}, {"id": 70, "atypeId": 2, "description": "【魔法装备】被赋予了守护魔法的\n智者头冠。", "etypeId": 3, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 133, "name": "大师环形饰品", "note": "", "params": [0, 0, 0, 25, 0, 36, 0, 0], "price": 5860}, {"id": 71, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 72, "atypeId": 1, "description": "", "etypeId": 4, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 135, "name": "装饰品", "note": "", "params": [0, 0, 0, 1, 0, 0, 0, 0], "price": 100}, {"id": 73, "atypeId": 1, "description": "【装饰品】提升防御的护符。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 147, "name": "守护石", "note": "", "params": [0, 0, 0, 5, 0, 5, 0, 0], "price": 500}, {"id": 74, "atypeId": 1, "description": "【装饰品】提升防御的指环。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 145, "name": "守护指环", "note": "", "params": [0, 0, 0, 10, 0, 10, 0, 0], "price": 1000}, {"id": 75, "atypeId": 1, "description": "【装饰品】提升攻击的护符。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 147, "name": "能量石", "note": "", "params": [0, 0, 5, 0, 5, 0, 0, 0], "price": 500}, {"id": 76, "atypeId": 1, "description": "【装饰品】提升攻击的指环。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 145, "name": "能量指环", "note": "", "params": [0, 0, 5, 0, 10, 0, 0, 0], "price": 1000}, {"id": 77, "atypeId": 1, "description": "【装饰品】提升敏捷的护符。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 147, "name": "速度石", "note": "", "params": [0, 0, 0, 0, 0, 0, 5, 0], "price": 500}, {"id": 78, "atypeId": 1, "description": "【装饰品】提升敏捷的指环。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 145, "name": "速度指环", "note": "", "params": [0, 0, 0, 0, 0, 0, 5, 0], "price": 1000}, {"id": 79, "atypeId": 1, "description": "【装饰品】提升幸运的护符。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 147, "name": "幸运石", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 5], "price": 500}, {"id": 80, "atypeId": 1, "description": "【装饰品】提升幸运的指环。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 145, "name": "幸运指环", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 10], "price": 1000}, {"id": 81, "atypeId": 1, "description": "【装饰品】提升魔法攻击的护符。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 147, "name": "魔法石", "note": "", "params": [0, 0, 0, 0, 5, 5, 0, 0], "price": 500}, {"id": 82, "atypeId": 1, "description": "【装饰品】提升魔法攻击的指环。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 145, "name": "魔法指环", "note": "", "params": [0, 0, 0, 0, 10, 10, 0, 0], "price": 1000}, {"id": 83, "atypeId": 1, "description": "【装饰品】提升攻击命中率\n的护符。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 1.5}], "iconIndex": 147, "name": "射手石", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 500}, {"id": 84, "atypeId": 1, "description": "【装饰品】提升攻击命中率的\n指环。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 2}], "iconIndex": 145, "name": "射手指环", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 100}, {"id": 85, "atypeId": 1, "description": "【装饰品】使魔法消耗减少20%的护符。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 23, "dataId": 4, "value": 0.8}], "iconIndex": 147, "name": "大师石", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 500}, {"id": 86, "atypeId": 1, "description": "【装饰品】使魔法消耗减少50%的指环。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 23, "dataId": 4, "value": 0.5}], "iconIndex": 145, "name": "大师指环", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 1000}, {"id": 87, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 88, "atypeId": 1, "description": "【装饰品】降低陷入中毒以及麻痹状态几率的\n指环。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 13, "dataId": 4, "value": 0.6}, {"code": 13, "dataId": 12, "value": 0.6}, {"code": 13, "dataId": 17, "value": 0.6}], "iconIndex": 145, "name": "中毒守护", "note": "", "params": [0, 0, 0, 1, 0, 0, 0, 0], "price": 300}, {"id": 89, "atypeId": 1, "description": "【装饰品】完全避免中毒以及麻痹状态的\n指环。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 14, "dataId": 4, "value": 1}, {"code": 14, "dataId": 12, "value": 1}, {"code": 14, "dataId": 17, "value": 1}], "iconIndex": 145, "name": "超级中毒守护", "note": "", "params": [0, 0, 0, 5, 0, 0, 0, 0], "price": 3000}, {"id": 90, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 91, "atypeId": 1, "description": "【装饰品】降低陷入睡眠、沉默、混乱、魅惑\n以及激昂状态几率的指环。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 13, "dataId": 6, "value": 0.6}, {"code": 13, "dataId": 7, "value": 0.6}, {"code": 13, "dataId": 9, "value": 0.6}, {"code": 13, "dataId": 10, "value": 0.6}, {"code": 13, "dataId": 8, "value": 0.6}], "iconIndex": 145, "name": "精神守护", "note": "", "params": [0, 0, 0, 1, 0, 0, 0, 0], "price": 500}, {"id": 92, "atypeId": 1, "description": "【装饰品】完全避免睡眠、沉默、混乱、魅惑\n以及激昂的指环。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 14, "dataId": 6, "value": 1}, {"code": 14, "dataId": 8, "value": 1}, {"code": 14, "dataId": 7, "value": 1}, {"code": 14, "dataId": 9, "value": 1}, {"code": 14, "dataId": 10, "value": 1}], "iconIndex": 145, "name": "超级精神守护", "note": "", "params": [0, 0, 0, 1, 0, 0, 0, 0], "price": 5000}, {"id": 93, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 94, "atypeId": 1, "description": "【装饰品】增加进行\n先发制人攻击几率的装饰品。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 64, "dataId": 3, "value": 1}], "iconIndex": 147, "name": "敌人雷达", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 1000}, {"id": 95, "atypeId": 1, "description": "【装饰品】防止受到突然袭击的装饰品。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 64, "dataId": 2, "value": 1}], "iconIndex": 205, "name": "警示钟", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 1000}, {"id": 96, "atypeId": 1, "description": "【装饰品】允许将TP结转至\n下一场战斗的装饰品。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 62, "dataId": 3, "value": 1}], "iconIndex": 144, "name": "存储器", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 1000}, {"id": 97, "atypeId": 1, "description": "【装饰品】提升TP补充率的指环。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 23, "dataId": 5, "value": 2}], "iconIndex": 145, "name": "毅力指环", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 1000}, {"id": 98, "atypeId": 1, "description": "【装饰品】降低MP消耗率的手镯。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 23, "dataId": 4, "value": 0.5}], "iconIndex": 144, "name": "MP守护者", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 1000}, {"id": 99, "atypeId": 1, "description": "【装饰品】得到四大精灵的加持，受到火、水、风、土属性的\n伤害减半。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 11, "dataId": 2, "value": 0.5}, {"code": 11, "dataId": 5, "value": 0.5}, {"code": 11, "dataId": 6, "value": 0.5}, {"code": 11, "dataId": 7, "value": 0.5}], "iconIndex": 145, "name": "元素守护", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 1000}, {"id": 100, "atypeId": 1, "description": "【装饰品】提升命中率的装饰品。", "etypeId": 5, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0.5}], "iconIndex": 151, "name": "护目镜", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 500}]