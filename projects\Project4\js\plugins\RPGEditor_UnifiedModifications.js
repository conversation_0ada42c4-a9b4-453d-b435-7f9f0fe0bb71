//=============================================================================
// RPGEditor_UnifiedModifications.js
// 统一修改插件 - 自动生成，请勿手动编辑
// 生成时间: 2025-05-26 01:33:58 UTC
//=============================================================================

/*:
 * @target MZ
 * @plugindesc [v1.0.0] RPG Editor 统一修改插件
 * <AUTHOR> Editor
 * @version 1.0.0
 * @description 此插件包含对象和类型的所有修改
 */

(() => {
    'use strict';
    
    console.log('RPG Editor 统一修改插件已加载');

// ========================================
// 对象修改 - 修改场景中的具体对象
// ========================================

    // 修改对象: ["Scene_Title", "2"]
    (function() {
        // 查找场景: Scene_Title
        const scene = SceneManager._scene;
        if (!scene) {
            console.warn('当前没有活动场景');
            return;
        }

        // 查找目标对象
        let targetObject = scene;
        targetObject = targetObject.2;
        if (!targetObject) {
            console.warn('无法找到对象路径: Scene_Title.2');
            return;
        }

        // 修改属性: x
        targetObject.x = -101;
    })();


})();
