/*:
 * @target MZ
 * @plugindesc RPG Maker MZ Type Definitions
 * <AUTHOR> Name
 * @help
 * This plugin provides type definitions for RPG Maker MZ.
 * It helps with type checking and code completion.
 */

(() => {
  "use strict";

  // 创建全局类型定义对象
  window.RPGMakerMZTypes = {
    // 核心类型定义
    coreTypes: {
      // PIXI 容器基类
      "PIXI.Container": {
        description: "PIXI容器基类，可以包含其他显示对象",
        children: {
          Stage: {
            description: "舞台类，是所有场景的基础",
            children: {
              Scene_Base: {
                description: "所有场景的基类",
                children: {
                  Scene_Title: { description: "标题场景" },
                  Scene_Map: { description: "地图场景" },
                  Scene_Battle: { description: "战斗场景" },
                  Scene_Menu: { description: "菜单场景" },
                  Scene_Item: { description: "物品场景" },
                  Scene_Skill: { description: "技能场景" },
                  Scene_Equip: { description: "装备场景" },
                  Scene_Status: { description: "状态场景" },
                  Scene_Options: { description: "选项场景" },
                  Scene_Save: { description: "保存场景" },
                  Scene_Load: { description: "加载场景" },
                  Scene_GameEnd: { description: "游戏结束场景" },
                  Scene_Shop: { description: "商店场景" },
                  Scene_Name: { description: "名称输入场景" },
                  Scene_Debug: { description: "调试场景" },
                },
              },
            },
          },
          Window: {
            description: "窗口基类",
            children: {
              Window_Base: {
                description: "所有窗口的基类",
                children: {
                  Window_Scrollable: {
                    description: "可滚动窗口基类",
                    children: {
                      Window_Selectable: {
                        description: "可选择窗口基类",
                        children: {
                          Window_Command: {
                            description: "命令窗口基类",
                            children: {
                              Window_MenuCommand: {
                                description: "菜单命令窗口",
                              },
                              Window_ActorCommand: {
                                description: "角色命令窗口",
                              },
                              Window_ChoiceList: {
                                description: "选择列表窗口",
                              },
                              Window_SkillType: { description: "技能类型窗口" },
                              Window_TitleCommand: {
                                description: "标题命令窗口",
                              },
                            },
                          },
                          Window_StatusBase: {
                            description: "状态窗口基类",
                            children: {
                              Window_Status: { description: "状态窗口" },
                              Window_EquipStatus: {
                                description: "装备状态窗口",
                              },
                              Window_SkillStatus: {
                                description: "技能状态窗口",
                              },
                            },
                          },
                          Window_ItemList: { description: "物品列表窗口" },
                          Window_SkillList: { description: "技能列表窗口" },
                          Window_EquipSlot: { description: "装备槽窗口" },
                          Window_SavefileList: { description: "存档列表窗口" },
                        },
                      },
                    },
                  },
                  Window_Message: { description: "消息窗口" },
                  Window_Help: { description: "帮助窗口" },
                  Window_Gold: { description: "金钱窗口" },
                  Window_NameEdit: { description: "名称编辑窗口" },
                  Window_NumberInput: { description: "数字输入窗口" },
                },
              },
            },
          },
        },
      },

      // PIXI 精灵基类
      "PIXI.Sprite": {
        description: "PIXI精灵基类，用于显示图像",
        children: {
          Sprite: {
            description: "RPG Maker MZ精灵基类",
            children: {
              Sprite_Clickable: {
                description: "可点击精灵基类",
                children: {
                  Sprite_Battler: {
                    description: "战斗者精灵基类",
                    children: {
                      Sprite_Actor: { description: "角色精灵" },
                      Sprite_Enemy: { description: "敌人精灵" },
                    },
                  },
                  Sprite_Button: { description: "按钮精灵" },
                },
              },
              Sprite_Animation: { description: "动画精灵" },
              Sprite_AnimationMV: { description: "MV样式动画精灵" },
              Sprite_Balloon: { description: "气球精灵" },
              Sprite_Character: { description: "角色精灵" },
              Sprite_Damage: { description: "伤害数字精灵" },
              Sprite_Destination: { description: "目的地精灵" },
              Sprite_Picture: { description: "图片精灵" },
              Sprite_StateIcon: { description: "状态图标精灵" },
              Sprite_Timer: { description: "计时器精灵" },
              Sprite_Weapon: { description: "武器精灵" },
            },
          },
        },
      },

      // 游戏对象基类
      Game_Object: {
        description: "游戏对象基类",
        children: {
          Game_CharacterBase: {
            description: "角色基类",
            children: {
              Game_Character: {
                description: "可移动角色基类",
                children: {
                  Game_Player: { description: "玩家角色" },
                  Game_Follower: { description: "跟随者角色" },
                  Game_Vehicle: { description: "载具角色" },
                  Game_Event: { description: "事件角色" },
                },
              },
            },
          },
          Game_BattlerBase: {
            description: "战斗者基类",
            children: {
              Game_Battler: {
                description: "战斗者类",
                children: {
                  Game_Actor: {
                    description: "角色类",
                    dataSource: "$dataActors",
                  },
                  Game_Enemy: {
                    description: "敌人类",
                    dataSource: "$dataEnemies",
                  },
                },
              },
            },
          },
          Game_Temp: { description: "临时数据类" },
          Game_System: { description: "系统数据类" },
          Game_Screen: { description: "屏幕效果类" },
          Game_Timer: { description: "计时器类" },
          Game_Message: { description: "消息类" },
          Game_Switches: { description: "开关类" },
          Game_Variables: { description: "变量类" },
          Game_SelfSwitches: { description: "独立开关类" },
          Game_Map: { description: "地图类" },
          Game_Party: { description: "队伍类" },
          Game_Troop: { description: "敌群类" },
        },
      },
    },

    // 数据类型定义
    dataTypes: {
      数据类型: {
        description: "RPG Maker MZ数据类型",
        children: {
          $dataActors: { description: "角色数据", dataSource: "$dataActors" },
          $dataClasses: { description: "职业数据", dataSource: "$dataClasses" },
          $dataSkills: { description: "技能数据", dataSource: "$dataSkills" },
          $dataItems: { description: "物品数据", dataSource: "$dataItems" },
          $dataWeapons: { description: "武器数据", dataSource: "$dataWeapons" },
          $dataArmors: { description: "防具数据", dataSource: "$dataArmors" },
          $dataEnemies: { description: "敌人数据", dataSource: "$dataEnemies" },
          $dataTroops: { description: "敌群数据", dataSource: "$dataTroops" },
          $dataStates: { description: "状态数据", dataSource: "$dataStates" },
          $dataAnimations: {
            description: "动画数据",
            dataSource: "$dataAnimations",
          },
          $dataTilesets: {
            description: "图块组数据",
            dataSource: "$dataTilesets",
          },
          $dataCommonEvents: {
            description: "公共事件数据",
            dataSource: "$dataCommonEvents",
          },
          $dataSystem: { description: "系统数据", dataSource: "$dataSystem" },
          $dataMapInfos: {
            description: "地图信息数据",
            dataSource: "$dataMapInfos",
          },
          $dataMap: { description: "当前地图数据", dataSource: "$dataMap" },
        },
      },
    },

    // 自定义类型定义
    customTypes: {
      自定义类型: {
        description: "用户创建的自定义类型",
        children: {},
      },
    },

    // 工具方法
    utils: {
      // 获取类型定义
      getTypeDefinition: function (typeName) {
        // 在核心类型中查找
        const findInTypes = (types, name) => {
          for (const key in types) {
            if (key === name) {
              return types[key];
            }
            if (types[key].children) {
              const found = findInTypes(types[key].children, name);
              if (found) return found;
            }
          }
          return null;
        };

        // 依次在各个类型集合中查找
        return (
          findInTypes(this.coreTypes, typeName) ||
          findInTypes(this.dataTypes, typeName) ||
          findInTypes(this.customTypes, typeName)
        );
      },

      // 添加自定义类型
      addCustomType: function (typeName, parentType, description) {
        const typeDef = this.getTypeDefinition(parentType);
        if (!typeDef) {
          console.error(`Parent type ${parentType} not found`);
          return false;
        }

        // 添加到自定义类型
        if (!this.customTypes["自定义类型"].children[typeName]) {
          this.customTypes["自定义类型"].children[typeName] = {
            description: description || `自定义类型 (继承自 ${parentType})`,
            parent: parentType,
            children: {},
          };
          return true;
        }
        return false;
      },

      // 获取所有类型名称
      getAllTypeNames: function () {
        const names = [];
        const collectNames = (types) => {
          for (const key in types) {
            names.push(key);
            if (types[key].children) {
              collectNames(types[key].children);
            }
          }
        };

        collectNames(this.coreTypes);
        collectNames(this.dataTypes);
        collectNames(this.customTypes);
        return names;
      },
    },
  };

  // 初始化插件
  const pluginName = "RPGMakerMZTypes";
  PluginManager.registerCommand(pluginName, "AddCustomType", (args) => {
    const typeName = args.typeName;
    const parentType = args.parentType;
    const description = args.description;
    window.RPGMakerMZTypes.utils.addCustomType(
      typeName,
      parentType,
      description
    );
  });
})();
