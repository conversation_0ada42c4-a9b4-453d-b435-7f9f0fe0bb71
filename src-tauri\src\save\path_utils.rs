/// 生成资源路径的条件代码模板
///
/// # 参数
/// * `resource_path` - 资源路径，如 "../projects/Project4/img/battlebacks2/Brick.png"
///
/// # 返回
/// 返回条件路径代码字符串，如：
/// `window.mzl ? '../projects/Project4/img/battlebacks2/Brick.png' : '/img/battlebacks2/Brick.png'`
///
/// # 示例
/// ```
/// let code = generate_resource_path_code("../projects/Project4/img/battlebacks2/Brick.png");
/// // 返回: window.mzl ? '../projects/Project4/img/battlebacks2/Brick.png' : '/img/battlebacks2/Brick.png'
/// ```
pub fn generate_resource_path_code(resource_path: &str) -> String {
    // 如果是完整路径（以 ../projects/ 开头）
    if resource_path.starts_with("../projects/") {
        // 提取相对路径部分（从 img/ 开始）
        let parts: Vec<&str> = resource_path.split('/').collect();
        let relative_path = if parts.len() >= 4 {
            // 重新组合从 img 开始的路径
            let img_path = parts[3..].join("/");
            format!("/{}", img_path)
        } else {
            // 如果路径格式不正确，使用原路径
            resource_path.to_string()
        };

        // 生成条件路径代码：完整路径 vs 相对路径
        format!("window.mzl ? '{}' : '{}'", resource_path, relative_path)
    } else {
        // 如果不是完整路径，直接使用原路径（加引号）
        format!("'{}'", resource_path)
    }
}

/// 处理图片路径转换的专用方法（保持向后兼容）
/// 输入: ../projects/Project4/img/titles1/Ruins.png
/// 输出: window.mzl ? '../projects/Project4/img/titles1/Ruins.png' : '/img/titles1/Ruins.png'
pub fn process_image_path(original_path: &str) -> String {
    generate_resource_path_code(original_path)
}
