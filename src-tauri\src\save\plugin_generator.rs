use crate::save::bitmap_generator::{generate_bitmap_code, generate_image_setting_code};
use crate::save::filter_generator::generate_filter_code;
use crate::save::object_lifecycle_generator::{
    generate_object_lifecycle_code, get_lifecycle_property_names, has_object_lifecycle_operations,
};
use crate::save::path_utils::process_image_path;
use crate::save::types::{Modification, ModificationType};

use std::sync::Mutex;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};

/// 生成空的插件模板
pub fn generate_empty_plugin() -> String {
    r#"/*:
 * @target MZ
 * @plugindesc RPG Editor - 自动生成的原型链修改插件
 * <AUTHOR> Editor
 * @help
 * 此插件由RPG Editor自动生成，包含对游戏对象原型链的修改。
 * 修改仅应用于满足特定条件的对象，而不是所有同类型的对象。
 */

(function() {
    'use strict';

    // 添加调试标志
    const DEBUG = true;

    // 调试日志函数
    function log(...args) {
        if (DEBUG) {
            console.log('[RPGEditor]', ...args);
        }
    }

    // 根据场景路径查找对象
    function findObjectByScenePath(scene, path) {
        if (DEBUG) log('查找对象，场景:', scene.constructor.name, '路径:', path);
        if (!path || path.length < 1) return null;

        // 检查场景名称是否匹配
        const sceneName = path[0];
        if (scene.constructor.name !== sceneName) {
            if (DEBUG) log('场景类型不匹配:', scene.constructor.name, '!=', sceneName);
            return null;
        }

        // 如果路径只有一个元素（场景名称），则返回场景本身
        if (path.length === 1) {
            return scene;
        }

        // 递归查找嵌套对象
        let currentObject = scene;

        // 从第二个元素开始遍历路径
        for (let i = 1; i < path.length; i++) {
            const index = parseInt(path[i]);

            // 检查索引是否有效
            if (isNaN(index) || !currentObject.children || index >= currentObject.children.length) {
                if (DEBUG) log('索引无效或超出范围:', index, '子对象数量:', currentObject.children ? currentObject.children.length : 0, '在路径位置', i);
                return null;
            }

            // 获取下一级对象
            currentObject = currentObject.children[index];

            // 如果对象为空，则中断查找
            if (!currentObject) {
                if (DEBUG) log('在索引', index, '处找不到对象，在路径位置', i);
                return null;
            }

            if (DEBUG) log('找到中间对象:', currentObject.constructor.name, '在索引', index, '在路径位置', i);
        }

        if (DEBUG) log('最终找到对象:', currentObject ? currentObject.constructor.name : 'null', '完整路径:', path.join('/'));
        return currentObject;
    }

    // 原型链修改
})();
"#.to_string()
}

/// 生成插件代码
pub fn generate_plugin_code(app_handle: &AppHandle, modifications: &[Modification]) -> String {
    println!("生成插件代码，修改数量: {}", modifications.len());

    // 获取项目名称
    let state_manager = app_handle.state::<Mutex<String>>();
    let project_name = state_manager
        .lock()
        .map(|name| name.clone())
        .unwrap_or_else(|_| "Project1".to_string());

    // 插件头部
    let mut code = generate_plugin_header();

    // 分离对象修改和类型修改
    let object_modifications: Vec<&Modification> = modifications
        .iter()
        .filter(|m| m.modification_type == ModificationType::ObjectInstance)
        .collect();

    let class_modifications: Vec<&Modification> = modifications
        .iter()
        .filter(|m| m.modification_type == ModificationType::ClassPrototype)
        .collect();

    // 生成类型原型修改代码（在场景修改之前）
    for class_mod in class_modifications {
        code.push_str(&generate_direct_class_prototype_modification_code(
            class_mod,
        ));
    }

    // 按场景分组对象修改
    let mut scene_modifications: std::collections::HashMap<String, Vec<&Modification>> =
        std::collections::HashMap::new();

    for m in object_modifications {
        if !m.scene_path.is_empty() {
            let scene_name = &m.scene_path[0];
            scene_modifications
                .entry(scene_name.clone())
                .or_insert_with(Vec::new)
                .push(m);
        }
    }

    // 为每个场景生成合并的代码
    for (scene_name, scene_mods) in scene_modifications {
        code.push_str(&generate_scene_modifications_code(&scene_name, &scene_mods));
    }

    // 插件尾部
    code.push_str("})();\n");

    println!("插件代码生成完成，长度: {} 字节", code.len());
    code
}

/// 生成插件头部
fn generate_plugin_header() -> String {
    r#"/*:
 * @target MZ
 * @plugindesc RPG Editor - 自动生成的原型链修改插件
 * <AUTHOR> Editor
 * @help
 * 此插件由RPG Editor自动生成，包含对游戏对象原型链的修改。
 * 修改仅应用于满足特定条件的对象，而不是所有同类型的对象。
 */

(function() {
    'use strict';
    // 保存原始的drawText方法
    // 添加调试标志
    const DEBUG = true;


    // 调试日志函数
    function log(...args) {
        if (DEBUG) {
            console.log('[RPGEditor]', ...args);
        }
    }
    Scene_Base.prototype.createWindowLayer = function () {
        this._windowLayer = new WindowLayer();
        if (this._windowLayer.x === 4) this._windowLayer.x = (Graphics.width - Graphics.boxWidth) / 2;
        if (this._windowLayer.y === 4) this._windowLayer.y = (Graphics.height - Graphics.boxHeight) / 2;
        this.addChild(this._windowLayer);
    };
    // 根据场景路径查找对象
    function findObjectByScenePath(path) {
        if (DEBUG) log('查找对象，路径:', path);
        if (!path || path.length < 1) return null;

        // 获取当前场景
        const scene = SceneManager._scene;
        if (!scene) {
            if (DEBUG) log('当前场景不存在');
            return null;
        }

        // 检查场景名称是否匹配
        const sceneName = path[0];
        if (scene.constructor.name !== sceneName) {
            if (DEBUG) log('场景类型不匹配:', scene.constructor.name, '!=', sceneName);
            return null;
        }

        // 如果路径只有一个元素（场景名称），则返回场景本身
        if (path.length === 1) {
            if (DEBUG) log('返回场景本身:', scene.constructor.name);
            return scene;
        }

        // 递归查找嵌套对象
        let currentObject = scene;

        // 从第二个元素开始遍历路径（都是索引）
        for (let i = 1; i < path.length; i++) {
            const index = parseInt(path[i]);

            // 检查索引是否有效
            if (isNaN(index) || !currentObject.children || index >= currentObject.children.length) {
                if (DEBUG) log('索引无效或超出范围:', index, '子对象数量:', currentObject.children ? currentObject.children.length : 0, '在路径位置', i);
                return null;
            }

            // 获取下一级对象
            currentObject = currentObject.children[index];

            // 如果对象为空，则中断查找
            if (!currentObject) {
                if (DEBUG) log('在索引', index, '处找不到对象，在路径位置', i);
                return null;
            }

            if (DEBUG) log('找到中间对象:', currentObject.constructor.name, '在索引', index, '在路径位置', i);
        }

        if (DEBUG) log('最终找到对象:', currentObject ? currentObject.constructor.name : 'null', '完整路径:', path.join('/'));
        return currentObject;
    }

    // 原型链修改
"#.to_string()
}

/// 为单个场景的所有修改生成合并的代码
fn generate_scene_modifications_code(scene_name: &str, modifications: &[&Modification]) -> String {
    let mut code = String::new();

    // 检查是否有场景级别的修改
    let scene_level_mods: Vec<&Modification> = modifications
        .iter()
        .filter(|m| m.scene_path.len() == 1)
        .cloned()
        .collect();

    // 检查是否有对象级别的修改
    let object_level_mods: Vec<&Modification> = modifications
        .iter()
        .filter(|m| m.scene_path.len() > 1)
        .cloned()
        .collect();

    if scene_level_mods.is_empty() && object_level_mods.is_empty() {
        return code;
    }

    // 生成场景原型方法重写
    code.push_str(&format!("    // 修改 {} 场景\n", scene_name));
    code.push_str(&format!(
        "    const original{}_Start = {}.prototype.start;\n",
        scene_name, scene_name
    ));
    code.push_str(&format!(
        "    {}.prototype.start = function() {{\n",
        scene_name
    ));
    code.push_str("        // 调用原始方法\n");
    code.push_str(&format!(
        "        original{}_Start.apply(this, arguments);\n",
        scene_name
    ));
    code.push_str("        \n");
    code.push_str(&format!(
        "        if (DEBUG) log('{}.start 被调用');\n",
        scene_name
    ));
    code.push_str("        \n");

    // 处理场景级别的修改
    for scene_mod in scene_level_mods {
        code.push_str("        // 修改场景本身\n");

        // 使用 generate_properties_code 来正确处理所有类型的属性修改，包括对象创建
        let scene_var_name = "scene"; // 场景对象的变量名
        let properties_code = generate_properties_code(&scene_var_name, &scene_mod.properties);

        // 将 targetObject_scene 替换为 this（因为在场景的 start 方法中，this 就是场景对象）
        let scene_properties_code =
            properties_code.replace(&format!("targetObject_{}", scene_var_name), "this");

        code.push_str(&scene_properties_code);
        code.push_str("        \n");
    }

    // 处理对象级别的修改
    for obj_mod in object_level_mods {
        code.push_str(&generate_single_object_modification_code(obj_mod));
    }

    code.push_str("    };\n\n");

    code
}

/// 生成单个对象修改的代码（在场景start方法内部）
fn generate_single_object_modification_code(m: &Modification) -> String {
    let mut code = String::new();

    // 生成变量名
    let var_name = format!(
        "{}_{}_{}",
        m.class_name.to_lowercase(),
        m.scene_path.join("_"),
        chrono::Utc::now().timestamp_millis() % 100000
    );

    // 生成场景路径
    let scene_path_json = serde_json::to_string(&m.scene_path).unwrap_or_default();

    code.push_str(&format!(
        "        // 修改路径为 {} 的 {} 对象\n",
        m.scene_path.join("/"),
        m.class_name
    ));

    code.push_str(&format!(
        "        const scenePath_{} = {};\n",
        var_name, scene_path_json
    ));

    // 生成查找和验证代码
    code.push_str(&format!(
        "        const targetObject_{} = findObjectByScenePath(scenePath_{});\n",
        var_name, var_name
    ));

    code.push_str(&format!(
        "        if (DEBUG) log('路径为 {} 的{} 对象:', targetObject_{} ? targetObject_{}.constructor.name : 'null');\n",
        m.scene_path.join("/"), m.class_name, var_name, var_name
    ));

    code.push_str(&format!("        if (targetObject_{}) {{\n", var_name));
    code.push_str("            // 应用属性修改\n");

    // 生成属性修改代码（增加缩进）
    let properties_code = generate_properties_code(&var_name, &m.properties);
    let indented_properties = properties_code
        .lines()
        .map(|line| {
            if line.trim().is_empty() {
                line.to_string()
            } else {
                format!("    {}", line)
            }
        })
        .collect::<Vec<_>>()
        .join("\n");
    code.push_str(&indented_properties);

    code.push_str("        }\n\n");

    code
}

/// 为单个修改生成代码
fn generate_modification_code(m: &Modification) -> String {
    let mut code = String::new();

    // 检查是否是场景级别的修改
    if m.scene_path.len() == 1 {
        // 场景级别的修改，直接修改场景属性
        return generate_scene_modification_code(m);
    }

    // 获取场景名称
    let scene_name = &m.scene_path[0];

    // 生成变量名
    let var_name = format!(
        "{}_{}_{}",
        m.class_name.to_lowercase(),
        m.scene_path.join("_"),
        chrono::Utc::now().timestamp_millis() % 100000
    );

    // 生成场景路径
    let scene_path_json = serde_json::to_string(&m.scene_path).unwrap_or_default();

    // 为每个场景生成原型方法重写
    code.push_str(&format!("    // 修改 {} 场景\n", scene_name));

    code.push_str(&format!(
        "    const original{}_Start = {}.prototype.start;\n",
        scene_name, scene_name
    ));

    code.push_str(&format!(
        "    {}.prototype.start = function() {{\n",
        scene_name
    ));

    code.push_str(&format!("        // 调用原始方法\n"));

    code.push_str(&format!(
        "        original{}_Start.apply(this, arguments);\n",
        scene_name
    ));

    code.push_str(&format!("        \n"));

    code.push_str(&format!(
        "        if (DEBUG) log('{}.start 被调用');\n",
        scene_name
    ));

    code.push_str(&format!("        \n"));

    code.push_str(&format!(
        "        // 修改路径为 {} 的 {} 对象\n",
        m.scene_path.join("/"),
        m.class_name
    ));

    code.push_str(&format!(
        "        const scenePath_{} = {};\n",
        var_name, scene_path_json
    ));

    // 生成查找和验证代码
    code.push_str(&format!(
        "        const targetObject_{} = findObjectByScenePath(scenePath_{});\n",
        var_name, var_name
    ));

    code.push_str(&format!(
        "        if (DEBUG) log('路径为 {} 的{} 对象:', targetObject_{} ? targetObject_{}.constructor.name : 'null');\n",
        m.scene_path.join("/"), m.class_name, var_name, var_name
    ));

    code.push_str(&format!("        if (targetObject_{}) {{\n", var_name));

    code.push_str(&format!("            // 应用属性修改\n"));

    // 生成属性修改代码（增加缩进）
    let properties_code = generate_properties_code(&var_name, &m.properties);
    let indented_properties = properties_code
        .lines()
        .map(|line| {
            if line.trim().is_empty() {
                line.to_string()
            } else {
                format!("    {}", line)
            }
        })
        .collect::<Vec<_>>()
        .join("\n");
    code.push_str(&indented_properties);

    code.push_str("        }\n\n");
    code.push_str("    };\n\n");

    code
}

/// 生成场景级别的修改代码
fn generate_scene_modification_code(m: &Modification) -> String {
    let mut code = String::new();

    let scene_name = &m.scene_path[0];

    code.push_str(&format!("    // 修改 {} 场景本身\n", scene_name));

    code.push_str(&format!(
        "    const original{}_Start = {}.prototype.start;\n",
        scene_name, scene_name
    ));

    code.push_str(&format!(
        "    {}.prototype.start = function() {{\n",
        scene_name
    ));

    code.push_str(&format!("        // 调用原始方法\n"));

    code.push_str(&format!(
        "        original{}_Start.apply(this, arguments);\n",
        scene_name
    ));

    code.push_str(&format!("        \n"));

    code.push_str(&format!(
        "        if (DEBUG) log('{}.start 被调用，修改场景本身');\n",
        scene_name
    ));

    code.push_str(&format!("        \n"));

    // 使用 generate_properties_code 来正确处理所有类型的属性修改，包括对象创建
    let scene_var_name = "scene"; // 场景对象的变量名
    let properties_code = generate_properties_code(&scene_var_name, &m.properties);

    // 将 targetObject_scene 替换为 this（因为在场景的 start 方法中，this 就是场景对象）
    let scene_properties_code =
        properties_code.replace(&format!("targetObject_{}", scene_var_name), "this");

    code.push_str(&scene_properties_code);

    code.push_str("    };\n\n");

    code
}

/// 生成属性修改代码
fn generate_properties_code(
    var_name: &str,
    properties: &std::collections::HashMap<String, serde_json::Value>,
) -> String {
    let mut code = String::new();

    // 添加调试信息
    println!(
        "生成属性修改代码，变量名: {}, 属性数量: {}",
        var_name,
        properties.len()
    );
    for (key, value) in properties {
        println!("  属性: {} = {}", key, value);
    }

    if properties.is_empty() {
        println!("  警告：属性列表为空，不会生成任何属性修改代码");
        return "            // 应用属性修改\n".to_string();
    }

    // 检查是否有对象生命周期操作
    if has_object_lifecycle_operations(properties) {
        code.push_str(&generate_object_lifecycle_code(var_name, properties));
    }

    // 检查是否有bitmap相关属性
    let has_bitmap_props = properties.keys().any(|k| k.starts_with("_bitmap."));
    let has_text = properties.contains_key("text");

    if has_bitmap_props || has_text {
        code.push_str(&generate_bitmap_code(var_name, properties));
    }

    // 检查是否有滤镜相关属性
    let has_filter_props = properties
        .keys()
        .any(|k| k.starts_with("filters") || k.contains("filters_operation"));

    if has_filter_props {
        code.push_str(&generate_filter_code(var_name, properties));
    }

    // 获取生命周期操作的属性名，用于过滤
    let lifecycle_props = get_lifecycle_property_names(properties);

    // 处理非bitmap、非滤镜、非生命周期相关的属性
    for (key, value) in properties {
        if !key.starts_with("_bitmap.")
            && key != "text"
            && key != "texture"
            && !key.starts_with("filters")
            && !key.contains("filters_operation")
            && !lifecycle_props.contains(key)
        {
            let value_str = serde_json::to_string(value).unwrap_or_else(|_| "null".to_string());

            // 特殊处理图片URL设置
            if key == "bitmap._url" {
                if let Some(url) = value.as_str() {
                    let target_object_name = format!("targetObject_{}", var_name);
                    let image_code = generate_image_setting_code(&target_object_name, url);
                    code.push_str(&format!("            // 更新 PIXI.js Sprite 纹理\n"));
                    code.push_str(&image_code);
                    code.push_str("\n");
                    code.push_str(&format!(
                        "            if (DEBUG) log('设置 Sprite 纹理 = ', {});\n",
                        process_image_path(url)
                    ));
                }
            }
            // 特殊处理 _bitmap.elements 数组修改
            else if key.starts_with("_bitmap.elements") {
                // 这些修改会在 generate_bitmap_code 中处理
                // 这里不需要额外处理，避免重复
                continue;
            }
            // 特殊处理 Sprite 颜色相关属性
            else if key == "hue" || key == "colorTone" || key == "blendColor" {
                match key.as_str() {
                    "hue" => {
                        code.push_str(&format!(
                            "        targetObject_{}.setHue({});\n",
                            var_name, value_str
                        ));
                    }
                    "colorTone" => {
                        code.push_str(&format!(
                            "        targetObject_{}.setColorTone({});\n",
                            var_name, value_str
                        ));
                    }
                    "blendColor" => {
                        code.push_str(&format!(
                            "        targetObject_{}.setBlendColor({});\n",
                            var_name, value_str
                        ));
                    }
                    _ => {}
                }
                code.push_str(&format!(
                    "        if (DEBUG) log('设置属性 {} =', {});\n",
                    key, value_str
                ));
            } else {
                code.push_str(&format!(
                    "        targetObject_{}.{} = {};\n",
                    var_name, key, value_str
                ));
                code.push_str(&format!(
                    "        if (DEBUG) log('设置属性 {} =', {});\n",
                    key, value_str
                ));
            }
        }
    }

    code
}

/// 生成类型原型修改代码
fn generate_class_prototype_modification_code(modification: &Modification) -> String {
    let mut code = String::new();

    let class_name = &modification.class_name;

    code.push_str(&format!("    // 修改 {} 类型原型\n", class_name));
    code.push_str(&format!("    (function() {{\n"));
    code.push_str(&format!(
        "        if (DEBUG) log('开始修改 {} 类型原型');\n",
        class_name
    ));
    code.push_str("        \n");

    // 为每个属性生成原型修改代码
    for (property_name, value) in &modification.properties {
        code.push_str(&generate_single_property_prototype_modification(
            class_name,
            property_name,
            value,
        ));
    }

    code.push_str("    })();\n\n");

    code
}

/// 生成单个属性的原型修改代码
fn generate_single_property_prototype_modification(
    class_name: &str,
    property_name: &str,
    value: &serde_json::Value,
) -> String {
    let mut code = String::new();

    let value_str = serde_json::to_string(value).unwrap_or_else(|_| "null".to_string());
    let custom_property = format!("_custom_{}", property_name);

    code.push_str(&format!("        // 修改 {} 属性\n", property_name));
    code.push_str(&format!(
        "        if (typeof {} !== 'undefined') {{\n",
        class_name
    ));

    // 保存原始属性描述符
    code.push_str(&format!(
        "            const originalDescriptor_{} = Object.getOwnPropertyDescriptor({}.prototype, '{}') || \n",
        property_name, class_name, property_name
    ));
    code.push_str(&format!(
        "                Object.getOwnPropertyDescriptor(Object.getPrototypeOf({}.prototype), '{}') || \n",
        class_name, property_name
    ));
    code.push_str("                { value: undefined, writable: true, enumerable: true, configurable: true };\n");
    code.push_str("            \n");

    // 重新定义属性
    code.push_str(&format!(
        "            Object.defineProperty({}.prototype, '{}', {{\n",
        class_name, property_name
    ));
    code.push_str("                get: function() {\n");
    code.push_str(&format!("                    // 优先返回实例自定义值\n"));
    code.push_str(&format!(
        "                    if (this.{} !== undefined) {{\n",
        custom_property
    ));
    code.push_str(&format!(
        "                        return this.{};\n",
        custom_property
    ));
    code.push_str("                    }\n");
    code.push_str(&format!("                    // 返回类型默认值\n"));
    code.push_str(&format!(
        "                    if ({}.prototype.{} !== undefined) {{\n",
        class_name, custom_property
    ));
    code.push_str(&format!(
        "                        return {}.prototype.{};\n",
        class_name, custom_property
    ));
    code.push_str("                    }\n");
    code.push_str(&format!("                    // 回退到原始值\n"));
    code.push_str(&format!(
        "                    if (originalDescriptor_{}.get) {{\n",
        property_name
    ));
    code.push_str(&format!(
        "                        return originalDescriptor_{}.get.call(this);\n",
        property_name
    ));
    code.push_str("                    }\n");
    code.push_str(&format!(
        "                    return originalDescriptor_{}.value;\n",
        property_name
    ));
    code.push_str("                },\n");
    code.push_str("                set: function(value) {\n");
    code.push_str(&format!(
        "                    this.{} = value;\n",
        custom_property
    ));
    code.push_str(&format!(
        "                    if (originalDescriptor_{}.set) {{\n",
        property_name
    ));
    code.push_str(&format!(
        "                        originalDescriptor_{}.set.call(this, value);\n",
        property_name
    ));
    code.push_str("                    }\n");
    code.push_str("                },\n");
    code.push_str("                enumerable: true,\n");
    code.push_str("                configurable: true\n");
    code.push_str("            });\n");
    code.push_str("            \n");

    // 设置类型默认值并立即应用
    code.push_str(&format!("            // 设置类型默认值\n"));
    code.push_str(&format!(
        "            {}.prototype.{} = {};\n",
        class_name, custom_property, value_str
    ));
    code.push_str("            \n");
    code.push_str(&format!("            // 立即应用到所有现有实例\n"));
    code.push_str(&format!(
        "            if (typeof SceneManager !== 'undefined' && SceneManager._scene) {{\n"
    ));
    code.push_str(&format!(
        "                const applyToObject = (obj) => {{\n"
    ));
    code.push_str(&format!(
        "                    if (obj instanceof {}) {{\n",
        class_name
    ));
    code.push_str(&format!(
        "                        if (obj.{} === undefined) {{\n",
        custom_property
    ));
    code.push_str(&format!(
        "                            obj.{} = {};\n",
        custom_property, value_str
    ));
    code.push_str(&format!(
        "                            if (DEBUG) log('应用类型默认值到现有实例:', obj.constructor.name, '{}', {});\n",
        property_name, value_str
    ));
    code.push_str("                        }\n");
    code.push_str("                    }\n");
    code.push_str("                    if (obj.children) {\n");
    code.push_str("                        obj.children.forEach(applyToObject);\n");
    code.push_str("                    }\n");
    code.push_str("                };\n");
    code.push_str("                applyToObject(SceneManager._scene);\n");
    code.push_str("            }\n");
    code.push_str("            \n");
    code.push_str(&format!(
        "            if (DEBUG) log('已修改 {} 类型的 {} 属性，默认值:', {});\n",
        class_name, property_name, value_str
    ));

    code.push_str("        } else {\n");
    code.push_str(&format!(
        "            if (DEBUG) log('警告: {} 类型不存在，无法修改原型');\n",
        class_name
    ));
    code.push_str("        }\n");
    code.push_str("        \n");

    code
}

/// 生成简化的类型原型修改代码（直接覆盖方案）
fn generate_simple_class_prototype_modification_code(modification: &Modification) -> String {
    let mut code = String::new();

    let class_name = &modification.class_name;

    code.push_str(&format!(
        "    // 修改 {} 类型原型（简化方案）\n",
        class_name
    ));
    code.push_str(&format!("    (function() {{\n"));
    code.push_str(&format!(
        "        if (DEBUG) log('开始修改 {} 类型原型');\n",
        class_name
    ));
    code.push_str("        \n");

    // 为每个属性生成简化的原型修改代码
    for (property_name, value) in &modification.properties {
        let value_str = serde_json::to_string(value).unwrap_or_else(|_| "null".to_string());

        code.push_str(&format!("        // 修改 {} 属性\n", property_name));
        code.push_str(&format!(
            "        if (typeof {} !== 'undefined') {{\n",
            class_name
        ));

        // 保存原始值（用于恢复）
        code.push_str(&format!(
            "            if (!{}.prototype._originalValues) {{\n",
            class_name
        ));
        code.push_str(&format!(
            "                {}.prototype._originalValues = {{}};\n",
            class_name
        ));
        code.push_str("            }\n");
        code.push_str(&format!(
            "            if ({}.prototype._originalValues['{}'] === undefined) {{\n",
            class_name, property_name
        ));
        code.push_str(&format!(
            "                {}.prototype._originalValues['{}'] = {}.prototype.{};\n",
            class_name, property_name, class_name, property_name
        ));
        code.push_str("            }\n");
        code.push_str("            \n");

        // 直接设置新值
        code.push_str(&format!("            // 直接设置新的默认值\n"));
        code.push_str(&format!(
            "            {}.prototype.{} = {};\n",
            class_name, property_name, value_str
        ));
        code.push_str("            \n");

        // 立即应用到所有现有实例
        code.push_str(&format!("            // 立即应用到所有现有实例\n"));
        code.push_str(&format!(
            "            if (typeof SceneManager !== 'undefined' && SceneManager._scene) {{\n"
        ));
        code.push_str(&format!(
            "                const applyToObject = (obj) => {{\n"
        ));
        code.push_str(&format!(
            "                    if (obj instanceof {}) {{\n",
            class_name
        ));
        code.push_str(&format!(
            "                        // 只对没有自定义值的实例应用新默认值\n"
        ));
        code.push_str(&format!(
            "                        if (!obj.hasOwnProperty('{}')) {{\n",
            property_name
        ));
        code.push_str(&format!(
            "                            obj.{} = {};\n",
            property_name, value_str
        ));
        code.push_str(&format!(
            "                            if (DEBUG) log('应用类型默认值到现有实例:', obj.constructor.name, '{}', {});\n",
            property_name, value_str
        ));
        code.push_str("                        }\n");
        code.push_str("                    }\n");
        code.push_str("                    if (obj.children) {\n");
        code.push_str("                        obj.children.forEach(applyToObject);\n");
        code.push_str("                    }\n");
        code.push_str("                };\n");
        code.push_str("                applyToObject(SceneManager._scene);\n");
        code.push_str("            }\n");
        code.push_str("            \n");
        code.push_str(&format!(
            "            if (DEBUG) log('已修改 {} 类型的 {} 属性，新默认值:', {});\n",
            class_name, property_name, value_str
        ));

        code.push_str("        } else {\n");
        code.push_str(&format!(
            "            if (DEBUG) log('警告: {} 类型不存在，无法修改原型');\n",
            class_name
        ));
        code.push_str("        }\n");
        code.push_str("        \n");
    }

    code.push_str("    })();\n\n");

    code
}

/// 生成重写类构造函数的代码（确保初始化时设置正确值）
fn generate_direct_class_prototype_modification_code(modification: &Modification) -> String {
    let mut code = String::new();

    let class_name = &modification.class_name;

    code.push_str(&format!(
        "    // 重写 {} 类型的 initialize 方法\n",
        class_name
    ));
    code.push_str(&format!(
        "    if (typeof {} !== 'undefined') {{\n",
        class_name
    ));
    code.push_str(&format!(
        "        if (DEBUG) log('重写 {} 类型的 initialize 方法');\n",
        class_name
    ));
    code.push_str("        \n");

    // 保存原始 initialize 方法
    code.push_str(&format!(
        "        const original{}_initialize = {}.prototype.initialize;\n",
        class_name, class_name
    ));
    code.push_str("        \n");

    // 重写 initialize 方法
    code.push_str(&format!(
        "        {}.prototype.initialize = function() {{\n",
        class_name
    ));
    code.push_str(&format!("            // 调用原始 initialize 方法\n"));
    code.push_str(&format!(
        "            original{}_initialize.apply(this, arguments);\n",
        class_name
    ));
    code.push_str("            \n");

    // 在初始化后设置自定义属性值和处理对象生命周期操作
    for (property_name, value) in &modification.properties {
        // 处理对象创建操作
        if property_name.starts_with("__CREATE_CHILD__") {
            let object_type = property_name.trim_start_matches("__CREATE_CHILD__");
            code.push_str(&generate_class_level_object_creation_code(
                class_name,
                object_type,
                value,
            ));
            continue;
        }

        // 跳过对象删除操作（类型修改模式下不应该有删除操作）
        if property_name.starts_with("__DELETE_CHILD__") {
            continue;
        }

        let value_str = serde_json::to_string(value).unwrap_or_else(|_| "null".to_string());

        code.push_str(&format!(
            "            // 设置自定义 {} 属性值\n",
            property_name
        ));
        code.push_str(&format!(
            "            if (DEBUG) log('{} initialize: 设置前 {} =', this.{});\n",
            class_name, property_name, property_name
        ));
        code.push_str(&format!(
            "            this.{} = {};\n",
            property_name, value_str
        ));
        code.push_str(&format!(
            "            if (DEBUG) log('{} initialize: 设置后 {} =', this.{});\n",
            class_name, property_name, property_name
        ));
        code.push_str("            \n");
    }

    code.push_str("        };\n");
    code.push_str("        \n");

    // 为位置相关属性重写可能影响位置的方法
    let position_properties = ["x", "y", "width", "height"];
    let has_position_property = modification
        .properties
        .keys()
        .any(|key| position_properties.contains(&key.as_str()));

    if has_position_property && class_name != "WindowLayer" {
        // 重写 move 方法（WindowLayer 除外）
        code.push_str(&format!("        // 重写 move 方法以保持自定义位置\n"));
        code.push_str(&format!(
            "        const original{}_move = {}.prototype.move;\n",
            class_name, class_name
        ));
        code.push_str(&format!(
            "        {}.prototype.move = function(x, y, width, height) {{\n",
            class_name
        ));
        code.push_str(&format!(
            "            if (DEBUG) log('{} move 被调用: x=', x, ', y=', y, ', w=', width, ', h=', height);\n",
            class_name
        ));

        // 为每个位置属性生成保持逻辑
        for (property_name, value) in &modification.properties {
            // 跳过对象生命周期操作属性
            if property_name.starts_with("__CREATE_CHILD__")
                || property_name.starts_with("__DELETE_CHILD__")
            {
                continue;
            }

            if position_properties.contains(&property_name.as_str()) {
                let value_str = serde_json::to_string(value).unwrap_or_else(|_| "null".to_string());
                code.push_str(&format!("            // 保持自定义 {} 值\n", property_name));
                code.push_str(&format!(
                    "            const original{} = {};\n",
                    property_name, property_name
                ));
            }
        }

        code.push_str(&format!("            // 调用原始 move 方法\n"));
        code.push_str(&format!(
            "            original{}_move.apply(this, arguments);\n",
            class_name
        ));

        // 重新设置自定义值
        for (property_name, value) in &modification.properties {
            // 跳过对象生命周期操作属性
            if property_name.starts_with("__CREATE_CHILD__")
                || property_name.starts_with("__DELETE_CHILD__")
            {
                continue;
            }

            if position_properties.contains(&property_name.as_str()) {
                let value_str = serde_json::to_string(value).unwrap_or_else(|_| "null".to_string());
                code.push_str(&format!(
                    "            // 重新设置自定义 {} 值\n",
                    property_name
                ));
                code.push_str(&format!(
                    "            this.{} = {};\n",
                    property_name, value_str
                ));
                code.push_str(&format!(
                    "            if (DEBUG) log('{} move: 重新设置 {} =', {});\n",
                    class_name, property_name, value_str
                ));
            }
        }

        code.push_str("        };\n");
        code.push_str("        \n");
    }

    // 删除了 WindowLayer 的特殊处理（Scene_Base.createWindowLayer 重写）

    code.push_str(&format!(
        "        if (DEBUG) log('已重写 {} 类型的相关方法');\n",
        class_name
    ));

    code.push_str("    } else {\n");
    code.push_str(&format!(
        "        if (DEBUG) log('警告: {} 类型不存在，无法重写 initialize 方法');\n",
        class_name
    ));
    code.push_str("    }\n");
    code.push_str("    \n");

    code
}

/// 生成类型级别的对象创建代码（简化版本，直接在initialize中创建）
fn generate_class_level_object_creation_code(
    class_name: &str,
    object_type: &str,
    creation_info: &serde_json::Value,
) -> String {
    let mut code = String::new();

    // 解析创建信息
    let object_name = creation_info
        .get("objectName")
        .and_then(|v| v.as_str())
        .unwrap_or("新对象");

    let default_properties = serde_json::Value::Object(serde_json::Map::new());
    let properties = creation_info
        .get("properties")
        .unwrap_or(&default_properties);

    code.push_str(&format!(
        "            // 为所有 {} 类型实例添加 {} 子对象: {}\n",
        class_name, object_type, object_name
    ));
    code.push_str(&format!(
        "            if (DEBUG) log('为所有 {} 实例添加 {} 子对象: {}');\n",
        class_name, object_type, object_name
    ));
    code.push_str(&format!("            \n"));

    // 生成唯一的变量名
    let child_var_name = format!("child_{}", object_type.to_lowercase());

    // 根据对象类型生成创建代码
    match object_type {
        "Sprite" => {
            code.push_str(&format!(
                "            const {} = new Sprite();\n",
                child_var_name
            ));
        }
        "Container" => {
            code.push_str(&format!(
                "            const {} = new PIXI.Container();\n",
                child_var_name
            ));
        }
        "Label" => {
            code.push_str(&format!(
                "            const {} = new Sprite();\n",
                child_var_name
            ));
            code.push_str(&format!(
                "            {}.bitmap = new Bitmap(200, 40);\n",
                child_var_name
            ));
        }
        "Text" => {
            code.push_str(&format!(
                "            const {} = new Sprite();\n",
                child_var_name
            ));
            code.push_str(&format!(
                "            {}.bitmap = new Bitmap(200, 40);\n",
                child_var_name
            ));
        }
        "Button" => {
            code.push_str(&format!(
                "            const {} = new Sprite();\n",
                child_var_name
            ));
            code.push_str(&format!(
                "            {}.bitmap = new Bitmap(100, 30);\n",
                child_var_name
            ));
        }
        "Window" => {
            code.push_str(&format!(
                "            const {} = new Window_Base(new Rectangle(0, 0, 200, 150));\n",
                child_var_name
            ));
        }
        _ => {
            // 默认创建Sprite
            code.push_str(&format!(
                "            const {} = new Sprite();\n",
                child_var_name
            ));
        }
    }

    // 设置对象属性
    if let Some(props_obj) = properties.as_object() {
        for (prop_key, prop_value) in props_obj {
            let value_str =
                serde_json::to_string(prop_value).unwrap_or_else(|_| "null".to_string());

            match prop_key.as_str() {
                "text" => {
                    // 特殊处理文本属性
                    code.push_str(&format!("            if ({}.bitmap) {{\n", child_var_name));
                    code.push_str(&format!("                {}.bitmap.drawText({}, 0, 0, {}.bitmap.width, {}.bitmap.height, 'left');\n",
                        child_var_name, value_str, child_var_name, child_var_name));
                    code.push_str(&format!("            }}\n"));
                }
                "anchorX" => {
                    code.push_str(&format!(
                        "            if ({}.anchor) {}.anchor.x = {};\n",
                        child_var_name, child_var_name, value_str
                    ));
                }
                "anchorY" => {
                    code.push_str(&format!(
                        "            if ({}.anchor) {}.anchor.y = {};\n",
                        child_var_name, child_var_name, value_str
                    ));
                }
                "scaleX" => {
                    code.push_str(&format!(
                        "            if ({}.scale) {}.scale.x = {};\n",
                        child_var_name, child_var_name, value_str
                    ));
                }
                "scaleY" => {
                    code.push_str(&format!(
                        "            if ({}.scale) {}.scale.y = {};\n",
                        child_var_name, child_var_name, value_str
                    ));
                }
                "name" => {
                    // 特殊处理name属性
                    code.push_str(&format!(
                        "            {}.name = {};\n",
                        child_var_name, value_str
                    ));
                }
                _ => {
                    // 普通属性直接设置
                    code.push_str(&format!(
                        "            {}.{} = {};\n",
                        child_var_name, prop_key, value_str
                    ));
                }
            }
        }
    }

    // 添加到父对象 - 使用 addChild 而不是 children.push
    code.push_str(&format!("            // 添加到父对象\n"));
    code.push_str(&format!("            this.addChild({});\n", child_var_name));
    code.push_str(&format!(
        "            if (DEBUG) log('为 {} 实例添加了 {} 子对象: {}');\n",
        class_name, object_type, object_name
    ));
    code.push_str(&format!("            \n"));

    code
}

/// 检查值是否为基本类型（可以安全进行相等比较）
fn is_primitive_value(value: &serde_json::Value) -> bool {
    match value {
        serde_json::Value::Null => true,
        serde_json::Value::Bool(_) => true,
        serde_json::Value::Number(_) => true,
        serde_json::Value::String(_) => true,
        serde_json::Value::Array(_) => false,
        serde_json::Value::Object(_) => false,
    }
}
