{"$message_type":"diagnostic","message":"cannot find type `PathBuf` in this scope","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"src\\save\\api\\entry.rs","byte_start":6159,"byte_end":6166,"line_start":184,"line_end":184,"column_start":59,"column_end":66,"is_primary":true,"text":[{"text":"fn get_temp_plugins_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {","highlight_start":59,"highlight_end":66}],"label":"not found in this scope","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this struct","code":null,"level":"help","spans":[{"file_name":"src\\save\\api\\entry.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use super::router;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use std::path::PathBuf;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: cannot find type `PathBuf` in this scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\api\\entry.rs:184:59\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m184\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn get_temp_plugins_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in this scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this struct\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use std::path::PathBuf;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find type `PathBuf` in this scope","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"src\\save\\api\\entry.rs","byte_start":6328,"byte_end":6335,"line_start":190,"line_end":190,"column_start":39,"column_end":46,"is_primary":true,"text":[{"text":"fn get_engine_plugins_dir() -> Result<PathBuf, String> {","highlight_start":39,"highlight_end":46}],"label":"not found in this scope","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this struct","code":null,"level":"help","spans":[{"file_name":"src\\save\\api\\entry.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use super::router;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use std::path::PathBuf;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: cannot find type `PathBuf` in this scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\api\\entry.rs:190:39\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn get_engine_plugins_dir() -> Result<PathBuf, String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in this scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this struct\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use std::path::PathBuf;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::path::PathBuf`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\api\\entry.rs","byte_start":4213,"byte_end":4231,"line_start":129,"line_end":129,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"    use std::path::PathBuf;","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\api\\entry.rs","byte_start":4209,"byte_end":4232,"line_start":129,"line_end":129,"column_start":5,"column_end":28,"is_primary":true,"text":[{"text":"    use std::path::PathBuf;","highlight_start":5,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::path::PathBuf`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\api\\entry.rs:129:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m129\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use std::path::PathBuf;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `entry::record_unified_modification`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\api\\mod.rs","byte_start":413,"byte_end":447,"line_start":15,"line_end":15,"column_start":9,"column_end":43,"is_primary":true,"text":[{"text":"pub use entry::record_unified_modification;","highlight_start":9,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\api\\mod.rs","byte_start":405,"byte_end":449,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":true,"text":[{"text":"pub use entry::record_unified_modification;","highlight_start":1,"highlight_end":45}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `entry::record_unified_modification`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\api\\mod.rs:15:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use entry::record_unified_modification;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::types`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\data\\mod.rs","byte_start":230,"byte_end":248,"line_start":9,"line_end":9,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"pub use crate::save::types;","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\data\\mod.rs","byte_start":0,"byte_end":250,"line_start":1,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"/// 数据管理模块","highlight_start":1,"highlight_end":11},{"text":"/// ","highlight_start":1,"highlight_end":5},{"text":"/// 分类：","highlight_start":1,"highlight_end":8},{"text":"/// 1. types - 数据结构定义","highlight_start":1,"highlight_end":22},{"text":"/// 2. modification_manager - 修改记录管理","highlight_start":1,"highlight_end":37},{"text":"/// 3. cache - 缓存管理（通过 utils::cache）","highlight_start":1,"highlight_end":37},{"text":"","highlight_start":1,"highlight_end":1},{"text":"// 重新导出现有的数据管理模块","highlight_start":1,"highlight_end":17},{"text":"pub use crate::save::types;","highlight_start":1,"highlight_end":28},{"text":"pub use crate::save::modification_manager;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::types`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\data\\mod.rs:9:9\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::types;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::modification_manager`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\data\\mod.rs","byte_start":258,"byte_end":291,"line_start":10,"line_end":10,"column_start":9,"column_end":42,"is_primary":true,"text":[{"text":"pub use crate::save::modification_manager;","highlight_start":9,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\data\\mod.rs","byte_start":250,"byte_end":293,"line_start":10,"line_end":10,"column_start":1,"column_end":44,"is_primary":true,"text":[{"text":"pub use crate::save::modification_manager;","highlight_start":1,"highlight_end":44}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::modification_manager`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\data\\mod.rs:10:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::modification_manager;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::plugin_generator`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\generators\\mod.rs","byte_start":317,"byte_end":346,"line_start":10,"line_end":10,"column_start":9,"column_end":38,"is_primary":true,"text":[{"text":"pub use crate::save::plugin_generator;","highlight_start":9,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\generators\\mod.rs","byte_start":0,"byte_end":348,"line_start":1,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"/// 代码生成器模块","highlight_start":1,"highlight_end":12},{"text":"/// ","highlight_start":1,"highlight_end":5},{"text":"/// 分类：","highlight_start":1,"highlight_end":8},{"text":"/// 1. plugin_generator - 主插件代码生成器","highlight_start":1,"highlight_end":35},{"text":"/// 2. object_lifecycle_generator - 对象生命周期代码生成器","highlight_start":1,"highlight_end":48},{"text":"/// 3. bitmap_generator - 位图相关代码生成器","highlight_start":1,"highlight_end":36},{"text":"/// 4. filter_generator - 滤镜相关代码生成器","highlight_start":1,"highlight_end":36},{"text":"","highlight_start":1,"highlight_end":1},{"text":"// 重新导出现有的生成器","highlight_start":1,"highlight_end":14},{"text":"pub use crate::save::plugin_generator;","highlight_start":1,"highlight_end":39},{"text":"pub use crate::save::object_lifecycle_generator;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::plugin_generator`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\generators\\mod.rs:10:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::plugin_generator;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::object_lifecycle_generator`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\generators\\mod.rs","byte_start":356,"byte_end":395,"line_start":11,"line_end":11,"column_start":9,"column_end":48,"is_primary":true,"text":[{"text":"pub use crate::save::object_lifecycle_generator;","highlight_start":9,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\generators\\mod.rs","byte_start":348,"byte_end":397,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use crate::save::object_lifecycle_generator;","highlight_start":1,"highlight_end":49},{"text":"pub use crate::save::bitmap_generator;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::object_lifecycle_generator`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\generators\\mod.rs:11:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::object_lifecycle_generator;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::bitmap_generator`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\generators\\mod.rs","byte_start":405,"byte_end":434,"line_start":12,"line_end":12,"column_start":9,"column_end":38,"is_primary":true,"text":[{"text":"pub use crate::save::bitmap_generator;","highlight_start":9,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\generators\\mod.rs","byte_start":397,"byte_end":436,"line_start":12,"line_end":13,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use crate::save::bitmap_generator;","highlight_start":1,"highlight_end":39},{"text":"pub use crate::save::filter_generator;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::bitmap_generator`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\generators\\mod.rs:12:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::bitmap_generator;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::filter_generator`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\generators\\mod.rs","byte_start":444,"byte_end":473,"line_start":13,"line_end":13,"column_start":9,"column_end":38,"is_primary":true,"text":[{"text":"pub use crate::save::filter_generator;","highlight_start":9,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\generators\\mod.rs","byte_start":436,"byte_end":475,"line_start":13,"line_end":13,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub use crate::save::filter_generator;","highlight_start":1,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::filter_generator`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\generators\\mod.rs:13:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::filter_generator;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::save`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\files\\mod.rs","byte_start":252,"byte_end":269,"line_start":10,"line_end":10,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"pub use crate::save::save;","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\files\\mod.rs","byte_start":0,"byte_end":271,"line_start":1,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"/// 文件处理模块","highlight_start":1,"highlight_end":11},{"text":"/// ","highlight_start":1,"highlight_end":5},{"text":"/// 分类：","highlight_start":1,"highlight_end":8},{"text":"/// 1. save - 保存操作主入口","highlight_start":1,"highlight_end":22},{"text":"/// 2. plugin_parser - 插件文件解析","highlight_start":1,"highlight_end":30},{"text":"/// 3. plugins_file - 插件文件操作","highlight_start":1,"highlight_end":29},{"text":"/// 4. path_utils - 路径处理工具","highlight_start":1,"highlight_end":27},{"text":"","highlight_start":1,"highlight_end":1},{"text":"// 重新导出现有的文件处理模块","highlight_start":1,"highlight_end":17},{"text":"pub use crate::save::save;","highlight_start":1,"highlight_end":27},{"text":"pub use crate::save::plugin_parser;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::save`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\files\\mod.rs:10:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::save;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::plugin_parser`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\files\\mod.rs","byte_start":279,"byte_end":305,"line_start":11,"line_end":11,"column_start":9,"column_end":35,"is_primary":true,"text":[{"text":"pub use crate::save::plugin_parser;","highlight_start":9,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\files\\mod.rs","byte_start":271,"byte_end":307,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use crate::save::plugin_parser;","highlight_start":1,"highlight_end":36},{"text":"pub use crate::save::plugins_file;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::plugin_parser`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\files\\mod.rs:11:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::plugin_parser;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::plugins_file`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\files\\mod.rs","byte_start":315,"byte_end":340,"line_start":12,"line_end":12,"column_start":9,"column_end":34,"is_primary":true,"text":[{"text":"pub use crate::save::plugins_file;","highlight_start":9,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\files\\mod.rs","byte_start":307,"byte_end":342,"line_start":12,"line_end":13,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use crate::save::plugins_file;","highlight_start":1,"highlight_end":35},{"text":"pub use crate::save::path_utils;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::plugins_file`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\files\\mod.rs:12:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::plugins_file;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::path_utils`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\files\\mod.rs","byte_start":350,"byte_end":373,"line_start":13,"line_end":13,"column_start":9,"column_end":32,"is_primary":true,"text":[{"text":"pub use crate::save::path_utils;","highlight_start":9,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\files\\mod.rs","byte_start":342,"byte_end":375,"line_start":13,"line_end":13,"column_start":1,"column_end":34,"is_primary":true,"text":[{"text":"pub use crate::save::path_utils;","highlight_start":1,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::path_utils`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\files\\mod.rs:13:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::path_utils;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::modification_manager::record_property_modification`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\save.rs","byte_start":37,"byte_end":100,"line_start":2,"line_end":2,"column_start":9,"column_end":72,"is_primary":true,"text":[{"text":"pub use crate::save::modification_manager::record_property_modification;","highlight_start":9,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\save.rs","byte_start":29,"byte_end":103,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use crate::save::modification_manager::record_property_modification;","highlight_start":1,"highlight_end":73},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::modification_manager::record_property_modification`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\save.rs:2:9\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::modification_manager::record_property_modification;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `merge_modifications`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\save.rs","byte_start":172,"byte_end":191,"line_start":4,"line_end":4,"column_start":68,"column_end":87,"is_primary":true,"text":[{"text":"use crate::save::modification_manager::{get_current_modifications, merge_modifications};","highlight_start":68,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\save\\save.rs","byte_start":170,"byte_end":191,"line_start":4,"line_end":4,"column_start":66,"column_end":87,"is_primary":true,"text":[{"text":"use crate::save::modification_manager::{get_current_modifications, merge_modifications};","highlight_start":66,"highlight_end":87}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\save\\save.rs","byte_start":144,"byte_end":145,"line_start":4,"line_end":4,"column_start":40,"column_end":41,"is_primary":true,"text":[{"text":"use crate::save::modification_manager::{get_current_modifications, merge_modifications};","highlight_start":40,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\save\\save.rs","byte_start":191,"byte_end":192,"line_start":4,"line_end":4,"column_start":87,"column_end":88,"is_primary":true,"text":[{"text":"use crate::save::modification_manager::{get_current_modifications, merge_modifications};","highlight_start":87,"highlight_end":88}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `merge_modifications`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\save.rs:4:68\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::save::modification_manager::{get_current_modifications, merge_modifications};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::plugin_parser::parse_existing_modifications`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\save.rs","byte_start":282,"byte_end":338,"line_start":6,"line_end":6,"column_start":5,"column_end":61,"is_primary":true,"text":[{"text":"use crate::save::plugin_parser::parse_existing_modifications;","highlight_start":5,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\save.rs","byte_start":278,"byte_end":341,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::save::plugin_parser::parse_existing_modifications;","highlight_start":1,"highlight_end":62},{"text":"use crate::utils::{cache, logging, path, project_state};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::plugin_parser::parse_existing_modifications`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\save.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::save::plugin_parser::parse_existing_modifications;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused macro definition: `format_error`","code":{"code":"unused_macros","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils.rs","byte_start":1450,"byte_end":1462,"line_start":45,"line_end":45,"column_start":18,"column_end":30,"is_primary":true,"text":[{"text":"    macro_rules! format_error {","highlight_start":18,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_macros)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused macro definition: `format_error`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils.rs:45:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m45\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    macro_rules! format_error {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_macros)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `format_error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils.rs","byte_start":1580,"byte_end":1592,"line_start":51,"line_end":51,"column_start":20,"column_end":32,"is_primary":true,"text":[{"text":"    pub(crate) use format_error;","highlight_start":20,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\utils.rs","byte_start":1565,"byte_end":1593,"line_start":51,"line_end":51,"column_start":5,"column_end":33,"is_primary":true,"text":[{"text":"    pub(crate) use format_error;","highlight_start":5,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `format_error`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils.rs:51:20\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub(crate) use format_error;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `app_handle`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\asset_manager.rs","byte_start":1697,"byte_end":1707,"line_start":54,"line_end":54,"column_start":25,"column_end":35,"is_primary":true,"text":[{"text":"fn get_project_root_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {","highlight_start":25,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\asset_manager.rs","byte_start":1697,"byte_end":1707,"line_start":54,"line_end":54,"column_start":25,"column_end":35,"is_primary":true,"text":[{"text":"fn get_project_root_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {","highlight_start":25,"highlight_end":35}],"label":null,"suggested_replacement":"_app_handle","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `app_handle`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\asset_manager.rs:54:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn get_project_root_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_app_handle`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `app_handle`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\project.rs","byte_start":9698,"byte_end":9708,"line_start":323,"line_end":323,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    app_handle: AppHandle,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\project.rs","byte_start":9698,"byte_end":9708,"line_start":323,"line_end":323,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    app_handle: AppHandle,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_app_handle","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `app_handle`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\project.rs:323:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m323\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    app_handle: AppHandle,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_app_handle`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `app_handle`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\api\\handlers\\type_handlers.rs","byte_start":5569,"byte_end":5579,"line_start":187,"line_end":187,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    app_handle: &AppHandle,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\save\\api\\handlers\\type_handlers.rs","byte_start":5569,"byte_end":5579,"line_start":187,"line_end":187,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    app_handle: &AppHandle,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_app_handle","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `app_handle`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\api\\handlers\\type_handlers.rs:187:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m187\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    app_handle: &AppHandle,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_app_handle`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `key`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\filter_generator.rs","byte_start":2025,"byte_end":2028,"line_start":66,"line_end":66,"column_start":10,"column_end":13,"is_primary":true,"text":[{"text":"    for (key, value) in operations {","highlight_start":10,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\save\\filter_generator.rs","byte_start":2025,"byte_end":2028,"line_start":66,"line_end":66,"column_start":10,"column_end":13,"is_primary":true,"text":[{"text":"    for (key, value) in operations {","highlight_start":10,"highlight_end":13}],"label":null,"suggested_replacement":"_key","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `key`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\filter_generator.rs:66:10\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (key, value) in operations {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_key`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `parent_path`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\object_lifecycle_generator.rs","byte_start":8481,"byte_end":8492,"line_start":230,"line_end":230,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"    let parent_path = creation_info.get(\"parentPath\").and_then(|v| v.as_array());","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\save\\object_lifecycle_generator.rs","byte_start":8481,"byte_end":8492,"line_start":230,"line_end":230,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"    let parent_path = creation_info.get(\"parentPath\").and_then(|v| v.as_array());","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":"_parent_path","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `parent_path`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\object_lifecycle_generator.rs:230:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m230\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let parent_path = creation_info.get(\"parentPath\").and_then(|v| v.as_array());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_parent_path`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `project_name`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\plugin_generator.rs","byte_start":3239,"byte_end":3251,"line_start":93,"line_end":93,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    let project_name = state_manager","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\save\\plugin_generator.rs","byte_start":3239,"byte_end":3251,"line_start":93,"line_end":93,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    let project_name = state_manager","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"_project_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `project_name`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\plugin_generator.rs:93:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m93\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let project_name = state_manager\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_project_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `value_str`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\plugin_generator.rs","byte_start":35685,"byte_end":35694,"line_start":1031,"line_end":1031,"column_start":21,"column_end":30,"is_primary":true,"text":[{"text":"                let value_str = serde_json::to_string(value).unwrap_or_else(|_| \"null\".to_string());","highlight_start":21,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\save\\plugin_generator.rs","byte_start":35685,"byte_end":35694,"line_start":1031,"line_end":1031,"column_start":21,"column_end":30,"is_primary":true,"text":[{"text":"                let value_str = serde_json::to_string(value).unwrap_or_else(|_| \"null\".to_string());","highlight_start":21,"highlight_end":30}],"label":null,"suggested_replacement":"_value_str","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `value_str`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\plugin_generator.rs:1031:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1031\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let value_str = serde_json::to_string(value).unwrap_or_else(|_| \"null\".to_string());\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_value_str`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `existing_content`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\save.rs","byte_start":2310,"byte_end":2326,"line_start":60,"line_end":60,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"    let existing_content = if plugin_file_path.exists() {","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\save\\save.rs","byte_start":2310,"byte_end":2326,"line_start":60,"line_end":60,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"    let existing_content = if plugin_file_path.exists() {","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":"_existing_content","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `existing_content`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\save.rs:60:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m60\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let existing_content = if plugin_file_path.exists() {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_existing_content`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `app_handle`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\temp_plugins.rs","byte_start":164,"byte_end":174,"line_start":8,"line_end":8,"column_start":29,"column_end":39,"is_primary":true,"text":[{"text":"pub fn get_temp_plugins_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {","highlight_start":29,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\temp_plugins.rs","byte_start":164,"byte_end":174,"line_start":8,"line_end":8,"column_start":29,"column_end":39,"is_primary":true,"text":[{"text":"pub fn get_temp_plugins_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {","highlight_start":29,"highlight_end":39}],"label":null,"suggested_replacement":"_app_handle","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `app_handle`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\temp_plugins.rs:8:29\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn get_temp_plugins_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_app_handle`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `app_handle`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\temp_plugins.rs","byte_start":6236,"byte_end":6246,"line_start":170,"line_end":170,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    app_handle: &AppHandle,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\temp_plugins.rs","byte_start":6236,"byte_end":6246,"line_start":170,"line_end":170,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    app_handle: &AppHandle,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_app_handle","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `app_handle`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\temp_plugins.rs:170:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m170\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    app_handle: &AppHandle,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_app_handle`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `api`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":11083,"byte_end":11086,"line_start":336,"line_end":336,"column_start":57,"column_end":60,"is_primary":true,"text":[{"text":"            if let tauri::WindowEvent::CloseRequested { api, .. } = event {","highlight_start":57,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try removing the field","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":11083,"byte_end":11087,"line_start":336,"line_end":336,"column_start":57,"column_end":61,"is_primary":true,"text":[{"text":"            if let tauri::WindowEvent::CloseRequested { api, .. } = event {","highlight_start":57,"highlight_end":61}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `api`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:336:57\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m336\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            if let tauri::WindowEvent::CloseRequested { api, .. } = event {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: try removing the field\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 2 previous errors; 28 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 2 previous errors; 28 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0412`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0412`.\u001b[0m\n"}
