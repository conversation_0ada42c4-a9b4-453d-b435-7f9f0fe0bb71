
/**
 * 获取对象的路径，其中场景是名字，后面都是索引
 * @param object 要获取路径的对象
 * @returns 对象路径数组，第一个元素是场景名称，后面的元素是索引
 */
export function getObjectPath(object: any): string[] {
  if (!object) {
    console.error('ProtoChange: 对象为空，无法获取路径');
    return [];
  }

  try {
    const path: string[] = [];
    let current = object;

    // 输出更详细的对象信息，帮助调试
    console.log(`ProtoChange: 获取对象路径，对象类型: ${object.constructor.name}`);
    console.log('ProtoChange: 对:', object);

    // 检查对象是否有 _objectId 属性（RPG Maker MZ 对象通常有这个属性）
    if (object._objectId) {
      console.log(`ProtoChange: 对象ID: ${object._objectId}`);
    }

    // 检查对象是否有 _scene 属性（RPG Maker MZ 对象通常有这个属性）
    if (object._scene) {
      console.log(`ProtoChange: 对象所属场景: ${object._scene.constructor.name}`);
      path.unshift(object._scene.constructor.name);
    }

    // 如果对象有父对象，获取在父对象中的索引
    if (current && current.parent) {
      // 获取对象在父对象中的索引
      const index = current.parent.children.indexOf(current);
      if (index >= 0) {
        path.push(index.toString());
        console.log(`ProtoChange: 对象在父对象中的索引: ${index}`);
      } else {
        console.error('ProtoChange: 无法获取对象在父对象中的索引');
      }

      // 向上遍历到场景
      let parent = current.parent;
      while (parent && parent.parent) {
        // 获取父对象在其父对象中的索引
        const parentIndex = parent.parent.children.indexOf(parent);
        if (parentIndex >= 0) {
          path.unshift(parentIndex.toString());
          console.log(`ProtoChange: 父对象在其父对象中的索引: ${parentIndex}`);
        }

        parent = parent.parent;
      }

      // 添加场景名称
      if (parent) {
        path.unshift(parent.constructor.name);
        console.log(`ProtoChange: 找到场景: ${parent.constructor.name}`);
      } else {
        console.error('ProtoChange: 无法找到场景');
      }
    } else {
      console.log('ProtoChange: 对象没有parent属性，尝试其他方法获取路径');

      // 如果对象没有parent属性，但有_scene属性，可以尝试从_scene获取路径
      if (object._scene) {
        // 如果对象有索引属性，可以使用它
        if (object.index !== undefined) {
          path.push(object.index.toString());
          console.log(`ProtoChange: 使用对象的index属性: ${object.index}`);
        } else if (object._index !== undefined) {
          path.push(object._index.toString());
          console.log(`ProtoChange: 使用对象的_index属性: ${object._index}`);
        }
      } else {
        // 如果没有任何可用的路径信息，检查对象的类名是否是有效的场景
        const className = object.constructor.name;
        if (className.startsWith('Scene_')) {
          // 如果是场景对象，使用场景名称作为路径
          path.push(className);
          console.log(`ProtoChange: 使用场景名称作为路径: ${className}`);
        } else {
          // 如果不是场景对象，且无法确定路径，返回空路径
          console.error(`ProtoChange: 无法确定对象的路径，对象类型: ${className}`);
          return [];
        }
      }
    }
    console.log(`ProtoChange: 获取的对象路径: ${path.join('/')}`);
    return path;
  } catch (error) {
    console.error('ProtoChange: 获取对象路径失败:', error);
    return [];
  }
}