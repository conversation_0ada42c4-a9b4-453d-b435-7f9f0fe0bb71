import React from "react";
import BlurOnIcon from '@mui/icons-material/BlurOn';
import ColorLensIcon from '@mui/icons-material/ColorLens';
import ContrastIcon from '@mui/icons-material/Contrast';
import WavesIcon from '@mui/icons-material/Waves';
import LocalFireDepartmentIcon from '@mui/icons-material/LocalFireDepartment';
import CloudIcon from '@mui/icons-material/Cloud';
import TextureIcon from '@mui/icons-material/Texture';
import FlareIcon from '@mui/icons-material/Flare';
import AcUnitIcon from '@mui/icons-material/AcUnit';

// 滤镜类型定义
export interface FilterOption {
  id: string;
  name: string;
  icon: React.ReactNode;
  filterClass: string;
  defaultParams: Record<string, any>;
}

// 滤镜参数定义
export interface FilterParam {
  name: string;
  type: 'number' | 'color' | 'boolean' | 'select';
  min?: number;
  max?: number;
  step?: number;
  options?: { value: string; label: string }[];
  defaultValue: any;
}

// 当前应用的滤镜定义
export interface AppliedFilter {
  id: string;
  type: string;
  params: Record<string, any>;
  instance?: any;
}

// 可用的滤镜选项
export const filterOptions: FilterOption[] = [
  {
    id: 'blur',
    name: '模糊',
    icon: React.createElement(BlurOnIcon),
    filterClass: 'PIXI.filters.BlurFilter',
    defaultParams: { blur: 8, quality: 4 }
  },
  {
    id: 'alpha',
    name: '透明度',
    icon: React.createElement(ContrastIcon),
    filterClass: 'PIXI.filters.AlphaFilter',
    defaultParams: { alpha: 0.5 }
  },
  {
    id: 'color',
    name: '颜色',
    icon: React.createElement(ColorLensIcon),
    filterClass: 'ColorFilter',
    defaultParams: {
      hue: 0,
      colorTone: [0, 0, 0, 0],
      blendColor: [0, 0, 0, 0],
      brightness: 255
    }
  },
  {
    id: 'advancedColor',
    name: '高级色彩',
    icon: React.createElement(ColorLensIcon),
    filterClass: 'AdvancedColorFilter',
    defaultParams: {
      contrast: 1.0,
      brightness: 1.0,
      shadows: 0.0,
      highlights: 0.0,
      saturation: 1.0,
      vibrance: 0.0
    }
  },
  {
    id: 'smoke',
    name: '烟雾',
    icon: React.createElement(CloudIcon),
    filterClass: 'SmokeFilter',
    defaultParams: { intensity: 0.5 }
  },
  {
    id: 'fire',
    name: '火焰',
    icon: React.createElement(LocalFireDepartmentIcon),
    filterClass: 'FireFilter',
    defaultParams: {
      intensity: 0.5,
      fireColor: [1.0, 0.5, 0.0]
    }
  },
  {
    id: 'water',
    name: '水波',
    icon: React.createElement(WavesIcon),
    filterClass: 'WaterFilter',
    defaultParams: {
      amplitude: 5.0,
      frequency: 10.0
    }
  },
  {
    id: 'cloth',
    name: '布料',
    icon: React.createElement(TextureIcon),
    filterClass: 'ClothFilter',
    defaultParams: {
      intensity: 5.0,
      waveFrequency: 20.0
    }
  },
  {
    id: 'glow',
    name: '光晕',
    icon: React.createElement(FlareIcon),
    filterClass: 'GlowFilter',
    defaultParams: {
      intensity: 0.5,
      glowColor: [1.0, 1.0, 0.5]
    }
  },
  {
    id: 'frost',
    name: '冰霜',
    icon: React.createElement(AcUnitIcon),
    filterClass: 'FrostFilter',
    defaultParams: { intensity: 0.5 }
  }
];

// 滤镜参数配置
export const filterParams: Record<string, FilterParam[]> = {
  'blur': [
    { name: 'blur', type: 'number', min: 0, max: 20, step: 0.1, defaultValue: 8 },
    { name: 'quality', type: 'number', min: 1, max: 10, step: 1, defaultValue: 4 }
  ],
  'alpha': [
    { name: 'alpha', type: 'number', min: 0, max: 1, step: 0.01, defaultValue: 0.5 }
  ],
  'color': [
    { name: 'hue', type: 'number', min: -360, max: 360, step: 1, defaultValue: 0 },
    { name: 'brightness', type: 'number', min: 0, max: 255, step: 1, defaultValue: 255 },
    { name: 'colorTone_r', type: 'number', min: -255, max: 255, step: 1, defaultValue: 0 },
    { name: 'colorTone_g', type: 'number', min: -255, max: 255, step: 1, defaultValue: 0 },
    { name: 'colorTone_b', type: 'number', min: -255, max: 255, step: 1, defaultValue: 0 },
    { name: 'colorTone_gray', type: 'number', min: -255, max: 255, step: 1, defaultValue: 0 },
    { name: 'blendColor_r', type: 'number', min: 0, max: 255, step: 1, defaultValue: 0 },
    { name: 'blendColor_g', type: 'number', min: 0, max: 255, step: 1, defaultValue: 0 },
    { name: 'blendColor_b', type: 'number', min: 0, max: 255, step: 1, defaultValue: 0 },
    { name: 'blendColor_a', type: 'number', min: 0, max: 1, step: 0.01, defaultValue: 0 }
  ],
  'advancedColor': [
    { name: 'contrast', type: 'number', min: 0, max: 2, step: 0.05, defaultValue: 1.0 },
    { name: 'brightness', type: 'number', min: 0, max: 2, step: 0.05, defaultValue: 1.0 },
    { name: 'shadows', type: 'number', min: -1, max: 1, step: 0.05, defaultValue: 0.0 },
    { name: 'highlights', type: 'number', min: -1, max: 1, step: 0.05, defaultValue: 0.0 },
    { name: 'saturation', type: 'number', min: 0, max: 2, step: 0.05, defaultValue: 1.0 },
    { name: 'vibrance', type: 'number', min: 0, max: 1, step: 0.05, defaultValue: 0.0 }
  ],
  'smoke': [
    { name: 'intensity', type: 'number', min: 0, max: 2, step: 0.01, defaultValue: 0.5 }
  ],
  'fire': [
    { name: 'intensity', type: 'number', min: 0, max: 2, step: 0.01, defaultValue: 0.5 },
    { name: 'fireColor_r', type: 'number', min: 0, max: 1, step: 0.01, defaultValue: 1.0 },
    { name: 'fireColor_g', type: 'number', min: 0, max: 1, step: 0.01, defaultValue: 0.5 },
    { name: 'fireColor_b', type: 'number', min: 0, max: 1, step: 0.01, defaultValue: 0.0 }
  ],
  'water': [
    { name: 'amplitude', type: 'number', min: 0, max: 20, step: 0.1, defaultValue: 5.0 },
    { name: 'frequency', type: 'number', min: 1, max: 30, step: 0.1, defaultValue: 10.0 }
  ],
  'cloth': [
    { name: 'intensity', type: 'number', min: 0, max: 20, step: 0.1, defaultValue: 5.0 },
    { name: 'waveFrequency', type: 'number', min: 1, max: 50, step: 0.1, defaultValue: 20.0 }
  ],
  'glow': [
    { name: 'intensity', type: 'number', min: 0, max: 2, step: 0.01, defaultValue: 0.5 },
    { name: 'glowColor_r', type: 'number', min: 0, max: 1, step: 0.01, defaultValue: 1.0 },
    { name: 'glowColor_g', type: 'number', min: 0, max: 1, step: 0.01, defaultValue: 1.0 },
    { name: 'glowColor_b', type: 'number', min: 0, max: 1, step: 0.01, defaultValue: 0.5 }
  ],
  'frost': [
    { name: 'intensity', type: 'number', min: 0, max: 2, step: 0.01, defaultValue: 0.5 }
  ]
};
