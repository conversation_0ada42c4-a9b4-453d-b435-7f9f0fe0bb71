var $plugins = [
  {
    description: "UI位置调整插件",
    name: "UIPositionAdjuster",
    parameters: {
      defaultPositions:
        '["{\\"targetClass\\":\\"\\",\\"targetId\\":\\"menu\\",\\"x\\":\\"50\\",\\"y\\":\\"\\"}"]',
    },
    status: false,
  },
  {
    description: "RPGEditor_BitmapTracker",
    name: "RPGEditor_BitmapTracker",
    parameters: {},
    status: true,
  },
  {
    description: "RPG Editor - 自动生成的原型链修改插件",
    name: "CustomResourcePath",
    parameters: {},
    status: true,
  },
  {
    description: "RPGEditor_PrototypeModifications插件",
    name: "RPGEditor_PrototypeModifications",
    parameters: {},
    status: true,
  },
  {
    description: "RPG Editor - 自动生成的原型链修改插件",
    name: "layout",
    parameters: {},
    status: true,
  },
  {
    description: "RPG Editor - 自动生成的原型链修改插件",
    name: "CustomAnimation",
    parameters: {},
    status: true,
  },
  {
    description: "RPG Editor - 自动生成的原型链修改插件",
    name: "RPGMakerMZTypes",
    parameters: {},
    status: true,
  },
  {
    description: "RPG Editor - 自动生成的原型链修改插件",
    name: "RPGEditor_DisableDefaultInput",
    parameters: {},
    status: true,
  }
];
