# CustomAnimation 自定义动画库插件

## 简介

CustomAnimation 是一个为 RPG Maker MZ 开发的轻量级动画库插件，它提供了一种简单而强大的方式来为游戏中的对象添加动画效果。与其他动画库不同，CustomAnimation 不依赖任何外部库，完全使用 RPG Maker MZ 内置的 update 机制进行更新，确保了最佳的性能和兼容性。

## 特性

- **轻量级设计**：不依赖任何外部库，使用 RPG Maker MZ 内置的 update 机制
- **多种动画目标**：支持对单个对象和对象数组进行动画处理
- **丰富的缓动函数**：提供多种内置缓动函数，包括线性、弹性、反弹等
- **动画序列**：支持创建连续的动画序列
- **完整的控制API**：支持动画的暂停、恢复、重启和停止
- **回调函数**：支持动画开始、更新、完成等事件的回调

## 安装

1. 将 `CustomAnimation.js` 文件放入你的游戏项目的 `plugins` 文件夹中
2. 在 RPG Maker MZ 的插件管理器中启用 CustomAnimation 插件

## 使用方法

### 基本用法

```javascript
// 获取对象
const obj = findObjectByScenePath(SceneManager._scene, ["Scene_Title", "2"]);

// 创建简单动画
AnimationManager.animate(obj, { x: 100, alpha: 0.5 }, 1000, "easeOutElastic");

// 对象数组动画
const objects = [obj1, obj2, obj3];
AnimationManager.animate(objects, { y: "+50", alpha: 0.8 }, 800, "easeInOutQuad");
```

### 动画序列

```javascript
// 创建动画序列
AnimationManager.sequence(obj, [
    { x: 100 },           // 第一步：移动到x=100
    { y: 200 },           // 第二步：移动到y=200
    { alpha: 0 }          // 第三步：淡出
], 500, "easeOutQuad");
```

### 使用回调函数

```javascript
AnimationManager.animate(obj, { x: 100 }, 1000, "linear", {
    onStart: () => console.log("动画开始"),
    onUpdate: (progress) => console.log(`进度: ${progress}`),
    onComplete: () => console.log("动画完成")
});
```

### 控制动画

```javascript
// 创建动画并保存引用
const animation = AnimationManager.animate(obj, { x: 100 }, 2000);

// 暂停动画
animation.pause();

// 恢复动画
animation.resume();

// 停止动画
animation.stop();

// 重新开始动画
animation.restart();
```

### 扩展对象

插件提供了扩展对象的方法，使对象可以直接调用动画方法：

```javascript
// 扩展对象
extendWithAnimationMethods(obj);

// 现在可以直接在对象上调用动画方法
obj.animate({ x: 100 }, 1000, "easeOutBounce");
obj.animateSequence([{ x: 100 }, { y: 200 }], 500);
obj.stopAnimations();

// 扩展容器
extendContainerWithAnimationMethods(container);

// 为容器的所有子元素添加动画
container.animateChildren({ alpha: 0.5 }, 1000);
container.stopChildrenAnimations();
```

## 缓动函数

插件提供了以下缓动函数：

- **线性**: `linear`
- **二次方**: `easeInQuad`, `easeOutQuad`, `easeInOutQuad`
- **三次方**: `easeInCubic`, `easeOutCubic`, `easeInOutCubic`
- **四次方**: `easeInQuart`, `easeOutQuart`, `easeInOutQuart`
- **弹性**: `easeInElastic`, `easeOutElastic`, `easeInOutElastic`
- **反弹**: `easeInBounce`, `easeOutBounce`, `easeInOutBounce`
- **正弦**: `easeInSine`, `easeOutSine`, `easeInOutSine`

## 高级用法

### 相对值动画

可以使用字符串形式的相对值来指定动画目标值：

```javascript
// 将x坐标增加50
obj.animate({ x: "+50" }, 1000);

// 将alpha值减少0.3
obj.animate({ alpha: "-0.3" }, 1000);
```

### 自定义缓动函数

可以传入自定义的缓动函数：

```javascript
// 自定义缓动函数
const customEasing = (t) => t * t * t * (t * (t * 6 - 15) + 10);

// 使用自定义缓动函数
obj.animate({ x: 100 }, 1000, customEasing);
```

## 注意事项

1. 确保目标对象有唯一的ID，否则插件会自动添加一个
2. 动画完成后会自动从AnimationManager中移除
3. 对于复杂的动画效果，建议使用动画序列而不是同时修改多个属性
4. 如果遇到性能问题，请减少同时运行的动画数量

## 兼容性

- 兼容 RPG Maker MZ 所有版本
- 不依赖任何外部库
- 可与其他插件一起使用
