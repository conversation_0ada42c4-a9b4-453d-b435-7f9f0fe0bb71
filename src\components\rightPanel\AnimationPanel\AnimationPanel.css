/* 确保下拉菜单显示在最上层 */
.MuiPopover-root {
  z-index: 9999 !important;
}

/* 确保对话框显示在最上层 */
.animation-editor-dialog {
  z-index: 1300 !important;
}

/* 确保下拉菜单内容可见 */
.MuiMenu-paper {
  z-index: 9999 !important;
}

/* 确保动画面板内的下拉菜单正常工作 */
.animation-panel-container .MuiSelect-select,
.animation-editor-dialog .MuiSelect-select {
  z-index: auto !important;
}

/* 紧凑的芯片样式 */
.compact-chip {
  height: 20px !important;
  font-size: 0.7rem !important;
}

/* 紧凑的按钮样式 */
.compact-button {
  padding: 2px 8px !important;
  min-width: auto !important;
}

/* 紧凑的图标按钮样式 */
.compact-icon-button {
  padding: 2px !important;
}

/* 确保动画面板内的内容不被裁剪 */
.animation-panel-container {
  overflow: visible !important;
}
