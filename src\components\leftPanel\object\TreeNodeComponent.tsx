import React, { useCallback, useState, useMemo } from "react";
import {
  Box,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Checkbox,
  Typography,
  IconButton,
} from "@mui/material";
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import EventIcon from "@mui/icons-material/Event";
import TheaterComedyIcon from "@mui/icons-material/TheaterComedy";
import ImageIcon from "@mui/icons-material/Image";
import TextFieldsIcon from "@mui/icons-material/TextFields";
import FolderIcon from "@mui/icons-material/Folder";
import WindowIcon from "@mui/icons-material/Window";
import SmartButtonIcon from "@mui/icons-material/SmartButton";
import GridViewIcon from "@mui/icons-material/GridView";
import HelpOutlineIcon from "@mui/icons-material/HelpOutline";
import { TreeNode, NodeType } from "../../../utils/tree/TreeNodeTypes";
import useProjectStore from "../../../store/Store";
import ObjectContextMenuEnhanced from "./ObjectContextMenuEnhanced";

interface TreeNodeComponentProps {
  node: TreeNode;
  level: number;
  expandedNodes: Set<string>;
  onToggleExpand: (nodeId: string) => void;
  updateTree?: () => void;
}

// 使用 React.memo 优化树节点渲染
// 获取节点类型图标
const getNodeTypeIcon = (node: TreeNode) => {
  // 根据节点类型返回对应的图标
  if (node.type) {
    // 特殊处理场景节点 - 根据名称判断是否是场景节点
    if (node.name && node.name.includes("场景")) {
      return (
        <TheaterComedyIcon fontSize="small" color="primary" sx={{ mr: 1 }} />
      );
    }

    switch (node.type) {
      case NodeType.SCENE:
        return (
          <TheaterComedyIcon fontSize="small" color="primary" sx={{ mr: 1 }} />
        );
      case NodeType.SPRITE:
        return <ImageIcon fontSize="small" color="success" sx={{ mr: 1 }} />;
      case NodeType.LABEL:
        return <TextFieldsIcon fontSize="small" color="info" sx={{ mr: 1 }} />;
      case NodeType.CONTAINER:
        return <FolderIcon fontSize="small" color="warning" sx={{ mr: 1 }} />;
      case NodeType.WINDOW:
        return <WindowIcon fontSize="small" color="info" sx={{ mr: 1 }} />;
      case NodeType.BUTTON:
        return (
          <SmartButtonIcon fontSize="small" color="secondary" sx={{ mr: 1 }} />
        );
      case NodeType.LAYOUT_CONTAINER:
        return <GridViewIcon fontSize="small" color="primary" sx={{ mr: 1 }} />;
      default:
        return (
          <HelpOutlineIcon fontSize="small" color="action" sx={{ mr: 1 }} />
        );
    }
  }

  // 如果没有指定类型，根据对象特征判断
  if (node.object) {
    const obj = node.object;
    const constructorName = obj.constructor?.name || "";

    if (constructorName.includes("Text") || (obj as any)._bitmap?.text) {
      return <TextFieldsIcon fontSize="small" color="info" sx={{ mr: 1 }} />;
    } else if (constructorName.includes("Window")) {
      return <WindowIcon fontSize="small" color="info" sx={{ mr: 1 }} />;
    } else if (
      constructorName.includes("Button") ||
      constructorName.includes("Sprite_Button")
    ) {
      return (
        <SmartButtonIcon fontSize="small" color="secondary" sx={{ mr: 1 }} />
      );
    } else if (constructorName.includes("Sprite")) {
      return <ImageIcon fontSize="small" color="success" sx={{ mr: 1 }} />;
    } else if (constructorName.includes("Container")) {
      return <FolderIcon fontSize="small" color="warning" sx={{ mr: 1 }} />;
    }
  }

  return <HelpOutlineIcon fontSize="small" color="action" sx={{ mr: 1 }} />;
};

const TreeNodeComponent = React.memo(
  ({
    node,
    level,
    expandedNodes,
    onToggleExpand,
    updateTree,
  }: TreeNodeComponentProps) => {
    // 优化方案：只订阅当前节点的选中状态
    const isSelected = useProjectStore(
      (state) => state.selectedObjects.objects.includes(node.object)
    );
    const setSelectedObjects = useProjectStore(
      (state) => state.setSelectedObjects
    );

    // 右键菜单状态
    const [contextMenu, setContextMenu] = useState<{
      open: boolean;
      position: { top: number; left: number } | null;
      nodeId: string | null;
    }>({
      open: false,
      position: null,
      nodeId: null,
    });

    const isExpanded = expandedNodes.has(node.id);
    const hasChildren = node.children.length > 0;

    // 处理选择节点 - 直接使用全局store
    const handleSelect = useCallback(() => {
      console.log("选中当前对象:", node.object);
      setSelectedObjects([node.object], "object");
    }, [node.object, setSelectedObjects]);

    // 处理可见性切换
    const handleVisibilityToggle = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>, _checked: boolean) => {
        // 阻止事件冒泡，避免触发父元素的点击事件
        e.stopPropagation();
        // 直接修改对象属性
        if (node.object) {
          const previousVisible = node.object.visible;
          node.object.visible = !previousVisible;
          console.log(
            "切换对象可见性: 从",
            previousVisible,
            "变为",
            node.object.visible
          );
          // 如果有更新树的方法，调用它
          if (updateTree) {
            updateTree();
          }
        }
      },
      [node.object, updateTree]
    );

    // 处理展开/折叠
    const handleToggleExpand = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        onToggleExpand(node.id);
      },
      [node.id, onToggleExpand]
    );

    // 处理右键菜单打开
    const handleContextMenu = useCallback(
      (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setContextMenu({
          open: true,
          position: { top: e.clientY, left: e.clientX },
          nodeId: node.id,
        });
      },
      [node.id]
    );

    // 处理右键菜单关闭
    const handleCloseContextMenu = useCallback(() => {
      setContextMenu({
        open: false,
        position: null,
        nodeId: null,
      });
    }, []);

    // 所有节点创建和删除功能已移至ContextMenu组件

    // 优化渲染，使用更简洁的结构
    return (
      <Box className="tree-node-component">
        <ListItem
          disablePadding
          sx={{
            pl: level * 2,
            height: 36, // 固定高度，便于虚拟化
          }}
        >
          <ListItemButton
            dense
            selected={isSelected}
            onClick={handleSelect}
            onContextMenu={handleContextMenu}
            className="tree-node-button"
          >
            <ListItemIcon sx={{ minWidth: 36 }}>
              <Checkbox
                edge="start"
                size="small"
                checked={node.object ? node.object.visible : false}
                onChange={handleVisibilityToggle}
                icon={<VisibilityOffIcon fontSize="small" />}
                checkedIcon={<VisibilityIcon fontSize="small" />}
              // 移除onClick事件，避免重复触发
              />
            </ListItemIcon>
            <ListItemText
              primary={
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  {/* 节点类型图标 */}
                  {getNodeTypeIcon(node)}

                  <Typography variant="body2" noWrap sx={{ mr: 1 }}>
                    {node.name}
                  </Typography>

                  {/* 事件图标 */}
                  {node.hasEvents && (
                    <EventIcon
                      fontSize="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    />
                  )}

                  {/* 文本内容 */}
                  {node.object &&
                    (node.object as any)._bitmap &&
                    (node.object as any)._bitmap.text && (
                      <Typography
                        variant="caption"
                        sx={{
                          backgroundColor: "rgba(25, 118, 210, 0.1)",
                          padding: "0px 4px",
                          borderRadius: "4px",
                          color: "primary.main",
                          maxWidth: "100px",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {(node.object as any)._bitmap.text}
                      </Typography>
                    )}
                </Box>
              }
              sx={{ margin: 0 }}
            />
            {hasChildren && (
              <IconButton
                size="small"
                onClick={handleToggleExpand}
                sx={{ padding: 0.5 }}
              >
                {isExpanded ? (
                  <ExpandLessIcon fontSize="small" />
                ) : (
                  <ExpandMoreIcon fontSize="small" />
                )}
              </IconButton>
            )}
          </ListItemButton>
        </ListItem>

        {/* 右键菜单 */}
        {contextMenu.open && (
          <ObjectContextMenuEnhanced
            open={contextMenu.open}
            anchorPosition={contextMenu.position}
            nodeId={contextMenu.nodeId}
            node={node}
            onClose={handleCloseContextMenu}
            updateTree={updateTree}
            customTypes={
              useProjectStore.getState().gameWindow
                ? Object.keys(useProjectStore.getState().gameWindow).filter(
                  (key) =>
                    useProjectStore.getState().gameWindow[key] &&
                    typeof useProjectStore.getState().gameWindow[key] ===
                    "function" &&
                    useProjectStore.getState().gameWindow[key].prototype &&
                    useProjectStore.getState().gameWindow[key].prototype
                      ._isCustomType
                )
                : []
            }
          />
        )}
      </Box>
    );
  },
  (prevProps, nextProps) => {
    // 自定义比较函数，只有在这些属性变化时才重新渲染

    // 检查 _bitmap.text 属性是否变化
    const prevText =
      prevProps.node.object && (prevProps.node.object as any)._bitmap?.text;
    const nextText =
      nextProps.node.object && (nextProps.node.object as any)._bitmap?.text;

    // 从全局store获取selectedObjects，用于比较
    const selectedObjectsState = useProjectStore.getState().selectedObjects;
    const prevIsSelected = selectedObjectsState.objects.includes(prevProps.node.object);
    const nextIsSelected = selectedObjectsState.objects.includes(nextProps.node.object);

    return (
      prevProps.node === nextProps.node &&
      prevIsSelected === nextIsSelected &&
      prevProps.expandedNodes.has(prevProps.node.id) ===
      nextProps.expandedNodes.has(nextProps.node.id) &&
      prevProps.level === nextProps.level &&
      prevProps.node.object?.visible === nextProps.node.object?.visible &&
      prevProps.node.hasEvents === nextProps.node.hasEvents &&
      prevText === nextText // 添加文本内容比较
    );
  }
);

export default TreeNodeComponent;
