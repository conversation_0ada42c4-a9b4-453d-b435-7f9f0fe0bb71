import React, { useState, useEffect, memo } from "react";
import { Box, Typography } from "@mui/material";
import ImagePreview from "../../ui/ImagePreview";
import useProjectStore, { AssetType } from "../../../store/Store";

interface ImgInfoGroupProps {
  selectedObject: any;
  forceUpdate: () => void;
}

const ImgInfoGroup: React.FC<ImgInfoGroupProps> = memo(({ selectedObject, forceUpdate }) => {
  // 获取Store方法
  const setSelectedObjectsProperty = useProjectStore(state => state.setSelectedObjectsProperty);

  // 管理纹理路径状态
  const [texturePath, setTexturePath] = useState<string>("");

  // 当选中对象变化时，更新纹理路径
  useEffect(() => {
    if (selectedObject) {
      try {
        // 尝试获取纹理路径
        let path = "";
        // 检查不同的纹理属性
        if (selectedObject.bitmap && selectedObject.bitmap._url) {
          // RPG Maker MZ Sprite 结构
          path = selectedObject.bitmap._url;
        }

        console.log("获取到的纹理路径:", path);
        setTexturePath(path);
      } catch (error) {
        console.error("获取纹理信息时出错:", error);
        setTexturePath("");
      }
    } else {
      setTexturePath("");
    }
  }, [selectedObject]);

  // 更新单一图片纹理
  const updateSingleTexture = (newSrc: string) => {
    console.log("更新单一图片纹理:", newSrc);

    try {
      if (selectedObject) {
        console.log("更新 Sprite 纹理:", newSrc);
        // 使用Store中的统一方法更新属性
        setSelectedObjectsProperty('bitmap._url', newSrc);
        // 更新本地状态
        setTexturePath(newSrc);

        // 强制重新渲染
        forceUpdate();
      }
    } catch (error) {
      console.error("更新纹理时出错:", error);
    }
  };



  // 渲染单一图片模式
  const renderSingleImageMode = () => (
    <Box>
      <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 0.5 }}>
        纹理: {texturePath || '无'}
      </Typography>
      <ImagePreview
        src={texturePath || ''}
        width={200}
        height={150}
        label="纹理"
        acceptTypes={[AssetType.Image]}
        onImageChange={updateSingleTexture}
      />
    </Box>
  );

  return (
    <Box sx={{ mt: 2 }}>
      {renderSingleImageMode()}
    </Box>
  );
}, (prevProps, nextProps) => {
  // 只有当selectedObject发生变化时才重新渲染
  return prevProps.selectedObject === nextProps.selectedObject;
});

export default ImgInfoGroup;