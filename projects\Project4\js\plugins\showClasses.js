//=============================================================================
// SimpleInheritanceViewer.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc 简化版鼠标悬停显示对象信息
 * <AUTHOR> Assistant
 *
 * @help
 * 这个插件会在鼠标悬停在游戏对象上时，显示该对象的基本信息。
 * 在游戏中按下F8键可以开启/关闭此功能。
 */

(function () {
  'use strict';

  //-----------------------------------------------------------------------------
  // SimpleInfoWindow
  //
  // 显示对象信息的简单窗口

  function SimpleInfoWindow() {
    this.initialize(...arguments);
  }

  SimpleInfoWindow.prototype = Object.create(Window_Base.prototype);
  SimpleInfoWindow.prototype.constructor = SimpleInfoWindow;

  SimpleInfoWindow.prototype.initialize = function () {
    Window_Base.prototype.initialize.call(this, new Rectangle(0, 0, 400, 100));
    this.opacity = 200;
    this.visible = false;
  };

  SimpleInfoWindow.prototype.setObject = function (obj) {
    this.contents.clear();
    if (!obj) return;

    this.visible = true;
    const typeName = obj.constructor.name;
    this.drawText("对象类型: " + typeName, 10, 10, 380);

    // 显示一些基本属性
    if (obj.x !== undefined && obj.y !== undefined) {
      this.drawText("位置: (" + Math.round(obj.x) + ", " + Math.round(obj.y) + ")", 10, 40, 380);
    }
  };

  //-----------------------------------------------------------------------------
  // Scene_Map 扩展

  const _Scene_Map_createAllWindows = Scene_Map.prototype.createAllWindows;
  Scene_Map.prototype.createAllWindows = function () {
    _Scene_Map_createAllWindows.call(this);
    this.createSimpleInfoWindow();
  };

  Scene_Map.prototype.createSimpleInfoWindow = function () {
    this._simpleInfoWindow = new SimpleInfoWindow();
    this.addWindow(this._simpleInfoWindow);
    this._infoActive = false;
    console.log("简易信息窗口已创建");
  };

  const _Scene_Map_update = Scene_Map.prototype.update;
  Scene_Map.prototype.update = function () {
    _Scene_Map_update.call(this);
    this.updateSimpleInfo();
  };

  Scene_Map.prototype.updateSimpleInfo = function () {
    // F8键切换
    if (Input.isTriggered('debug')) {
      this._infoActive = !this._infoActive;
      console.log("信息显示:", this._infoActive ? "开启" : "关闭");
      if (!this._infoActive) {
        this._simpleInfoWindow.visible = false;
      }
    }

    if (this._infoActive) {
      const mouseX = TouchInput.x;
      const mouseY = TouchInput.y;

      // 简单地检查鼠标下是否有精灵
      let found = false;
      if (this._spriteset && this._spriteset.children) {
        for (const child of this._spriteset.children) {
          if (child instanceof Sprite && child.visible) {
            const rect = new Rectangle(
              child.x - child.width * child.anchor.x,
              child.y - child.height * child.anchor.y,
              child.width,
              child.height
            );

            if (rect.contains(mouseX, mouseY)) {
              this._simpleInfoWindow.setObject(child);
              this._simpleInfoWindow.x = mouseX;
              this._simpleInfoWindow.y = mouseY + 20;
              found = true;
              break;
            }
          }
        }
      }

      if (!found) {
        this._simpleInfoWindow.visible = false;
      }
    }
  };
})();