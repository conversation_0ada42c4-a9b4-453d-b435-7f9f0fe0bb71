//=============================================================================
// FloatingTextEffect.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc 在游戏中显示悬浮文本效果
 * <AUTHOR> Agent
 * @url https://github.com/yourusername/FloatingTextEffect
 *
 * @param defaultDuration
 * @text 默认持续时间
 * @desc 悬浮文本显示的默认持续时间（帧数）
 * @default 60
 * @type number
 * @min 1
 *
 * @param defaultFontSize
 * @text 默认字体大小
 * @desc 悬浮文本的默认字体大小
 * @default 24
 * @type number
 * @min 10
 *
 * @param defaultColor
 * @text 默认颜色
 * @desc 悬浮文本的默认颜色（CSS格式）
 * @default #FFFFFF
 * @type string
 *
 * @param defaultOutlineColor
 * @text 默认描边颜色
 * @desc 悬浮文本的默认描边颜色（CSS格式）
 * @default #000000
 * @type string
 *
 * @param defaultOutlineWidth
 * @text 默认描边宽度
 * @desc 悬浮文本的默认描边宽度
 * @default 4
 * @type number
 * @min 0
 *
 * @param defaultRiseHeight
 * @text 默认上升高度
 * @desc 悬浮文本上升的默认高度（像素）
 * @default 48
 * @type number
 * @min 0
 *
 * @help
 * ============================================================================
 * 介绍
 * ============================================================================
 * 
 * 这个插件允许您在游戏中显示悬浮文本效果，例如伤害数字、恢复数值、
 * 状态信息等。文本会从指定位置上升并逐渐消失。
 * 
 * ============================================================================
 * 插件命令
 * ============================================================================
 * 
 * 在事件中，您可以使用以下插件命令：
 * 
 * ShowFloatingText 文本 x坐标 y坐标 [颜色] [持续时间] [字体大小]
 * 
 * 例如：
 * ShowFloatingText "恢复了50点HP!" 400 300 #00FF00 120 28
 * 
 * ============================================================================
 * 脚本调用
 * ============================================================================
 * 
 * 您也可以在脚本中使用以下函数：
 * 
 * FloatingTextEffect.show(text, x, y, options);
 * 
 * options是一个可选对象，可以包含以下属性：
 * - color: 文本颜色
 * - duration: 持续时间
 * - fontSize: 字体大小
 * - outlineColor: 描边颜色
 * - outlineWidth: 描边宽度
 * - riseHeight: 上升高度
 * 
 * 例如：
 * FloatingTextEffect.show("暴击！", 400, 300, {
 *   color: "#FF0000",
 *   duration: 90,
 *   fontSize: 32
 * });
 */

var Imported = Imported || {};
Imported.FloatingTextEffect = true;

var FloatingTextEffect = FloatingTextEffect || {};
FloatingTextEffect.version = 1.0;
FloatingTextEffect.parameters = PluginManager.parameters('FloatingTextEffect');

FloatingTextEffect.defaultDuration = Number(FloatingTextEffect.parameters['defaultDuration'] || 60);
FloatingTextEffect.defaultFontSize = Number(FloatingTextEffect.parameters['defaultFontSize'] || 24);
FloatingTextEffect.defaultColor = String(FloatingTextEffect.parameters['defaultColor'] || '#FFFFFF');
FloatingTextEffect.defaultOutlineColor = String(FloatingTextEffect.parameters['defaultOutlineColor'] || '#000000');
FloatingTextEffect.defaultOutlineWidth = Number(FloatingTextEffect.parameters['defaultOutlineWidth'] || 4);
FloatingTextEffect.defaultRiseHeight = Number(FloatingTextEffect.parameters['defaultRiseHeight'] || 48);

//-----------------------------------------------------------------------------
// FloatingText
//
// 悬浮文本的精灵类

function FloatingText() {
    this.initialize.apply(this, arguments);
}

FloatingText.prototype = Object.create(Sprite.prototype);
FloatingText.prototype.constructor = FloatingText;

FloatingText.prototype.initialize = function(text, x, y, options) {
    Sprite.prototype.initialize.call(this);
    
    options = options || {};
    this.text = text;
    this.targetX = x;
    this.targetY = y;
    this.duration = options.duration || FloatingTextEffect.defaultDuration;
    this.maxDuration = this.duration;
    this.fontSize = options.fontSize || FloatingTextEffect.defaultFontSize;
    this.textColor = options.color || FloatingTextEffect.defaultColor;
    this.outlineColor = options.outlineColor || FloatingTextEffect.defaultOutlineColor;
    this.outlineWidth = options.outlineWidth || FloatingTextEffect.defaultOutlineWidth;
    this.riseHeight = options.riseHeight || FloatingTextEffect.defaultRiseHeight;
    
    this.bitmap = new Bitmap(500, 100);
    this.bitmap.fontFace = $gameSystem.mainFontFace();
    this.bitmap.fontSize = this.fontSize;
    this.bitmap.outlineColor = this.outlineColor;
    this.bitmap.outlineWidth = this.outlineWidth;
    this.bitmap.textColor = this.textColor;
    this.bitmap.drawText(this.text, 0, 0, 500, 100, 'center');
    
    this.x = this.targetX;
    this.y = this.targetY;
    this.anchor.x = 0.5;
    this.anchor.y = 0.5;
    this.opacity = 255;
};

FloatingText.prototype.update = function() {
    Sprite.prototype.update.call(this);
    
    if (this.duration > 0) {
        this.duration--;
        
        // 计算上升动画
        const progress = 1 - (this.duration / this.maxDuration);
        this.y = this.targetY - (this.riseHeight * progress);
        
        // 计算淡出效果
        if (this.duration < this.maxDuration / 2) {
            this.opacity = 255 * (this.duration / (this.maxDuration / 2));
        }
        
        if (this.duration <= 0) {
            this.remove();
        }
    }
};

FloatingText.prototype.remove = function() {
    if (this.parent) {
        this.parent.removeChild(this);
    }
};

//-----------------------------------------------------------------------------
// FloatingTextEffect
//
// 插件的主要功能

FloatingTextEffect.show = function(text, x, y, options) {
    const floatingText = new FloatingText(text, x, y, options);
    SceneManager._scene.addChild(floatingText);
    return floatingText;
};

//-----------------------------------------------------------------------------
// Game_Interpreter
//
// 添加插件命令

const _Game_Interpreter_pluginCommand = Game_Interpreter.prototype.pluginCommand;
Game_Interpreter.prototype.pluginCommand = function(command, args) {
    _Game_Interpreter_pluginCommand.call(this, command, args);
    
    if (command === 'ShowFloatingText') {
        const text = args[0];
        const x = Number(args[1]);
        const y = Number(args[2]);
        const options = {};
        
        if (args[3]) options.color = args[3];
        if (args[4]) options.duration = Number(args[4]);
        if (args[5]) options.fontSize = Number(args[5]);
        
        FloatingTextEffect.show(text, x, y, options);
    }
};

//-----------------------------------------------------------------------------
// PluginManager
//
// 注册MZ风格的插件命令

if (Utils.RPGMAKER_NAME === "MZ") {
    PluginManager.registerCommand("FloatingTextEffect", "show", args => {
        const text = args.text;
        const x = Number(args.x);
        const y = Number(args.y);
        const options = {
            color: args.color,
            duration: Number(args.duration),
            fontSize: Number(args.fontSize),
            outlineColor: args.outlineColor,
            outlineWidth: Number(args.outlineWidth),
            riseHeight: Number(args.riseHeight)
        };
        
        FloatingTextEffect.show(text, x, y, options);
    });
}
