use tauri::AppHandle;
use crate::utils::logging;
use super::router;

/// 统一的修改记录入口API
/// 这是前端调用的唯一接口
/// 
/// 参数格式：{
///   operationMode: "TYPE" | "OBJECT",           // 操作模式：类型操作 vs 对象操作
///   operationType: "CREATE" | "DELETE" | "MODIFY", // 操作类型：增加、删除、修改
///   targetPath: string[],                       // 路径
///   fieldName: string,                          // 字段名字
///   fieldValue: any                             // 字段值
/// }
#[tauri::command]
pub fn record_unified_modification(
    app_handle: AppHandle,
    operation_info: serde_json::Value,
) -> Result<String, String> {
    logging::log_function_call!(
        "UnifiedAPI",
        "record_unified_modification",
        &operation_info
    );

    // 解析基本参数
    let operation_mode = operation_info
        .get("operationMode")
        .and_then(|v| v.as_str())
        .ok_or("缺少操作模式 (operationMode)")?;

    let operation_type = operation_info
        .get("operationType")
        .and_then(|v| v.as_str())
        .ok_or("缺少操作类型 (operationType)")?;

    let field_name = operation_info
        .get("fieldName")
        .and_then(|v| v.as_str())
        .ok_or("缺少字段名称 (fieldName)")?;

    let field_value = operation_info
        .get("fieldValue")
        .ok_or("缺少字段值 (fieldValue)")?
        .clone();

    let target_path = operation_info
        .get("targetPath")
        .and_then(|v| v.as_array())
        .ok_or("缺少目标路径 (targetPath)")?;

    let path_strings: Vec<String> = target_path
        .iter()
        .filter_map(|v| v.as_str())
        .map(|s| s.to_string())
        .collect();

    let class_name = operation_info
        .get("className")
        .and_then(|v| v.as_str())
        .unwrap_or("Unknown");

    // 获取额外的上下文信息
    let top_level_type = operation_info
        .get("_operationContext")
        .and_then(|ctx| ctx.get("topLevelType"))
        .and_then(|v| v.as_str());

    logging::log_info(
        "UnifiedAPI",
        &format!(
            "收到统一修改请求 - 模式: {}, 类型: {}, 路径: {:?}, 字段: {}, 类名: {}",
            operation_mode, operation_type, path_strings, field_name, class_name
        ),
    );

    // 构建操作请求结构
    let operation_request = router::OperationRequest {
        operation_mode: operation_mode.to_string(),
        operation_type: operation_type.to_string(),
        target_path: path_strings,
        field_name: field_name.to_string(),
        field_value,
        class_name: class_name.to_string(),
        top_level_type: top_level_type.map(|s| s.to_string()),
    };

    // 路由到具体的操作处理器
    router::route_operation(app_handle, operation_request)
}
