use crate::utils::logging;
use std::path::PathBuf;
use tauri::AppHandle;

/// 新的简化API：保存前端生成的插件代码
/// 前端负责代码生成，后端只负责文件写入
#[tauri::command]
pub fn save_generated_plugin_code(
    app_handle: AppHandle,
    plugin_code: String,
    project_name: Option<String>,
) -> Result<String, String> {
    println!("🚀 [简化API] 收到前端生成的插件代码！");
    println!("   项目名称: {:?}", project_name);
    println!("   代码长度: {} 字符", plugin_code.len());

    logging::log_info(
        "SimplifiedAPI",
        &format!(
            "保存前端生成的插件代码 - 项目: {:?}, 代码长度: {}",
            project_name,
            plugin_code.len()
        ),
    );

    // 直接调用插件写入逻辑
    write_plugin_file(app_handle, plugin_code, project_name)
}

/// 写入插件文件的核心逻辑
fn write_plugin_file(
    app_handle: AppHandle,
    plugin_code: String,
    project_name: Option<String>,
) -> Result<String, String> {
    use crate::utils::file_ops;
    use crate::utils::path;
    use crate::utils::project_state;
    use std::path::PathBuf;

    // 获取项目名称
    let project_name = if let Some(name) = project_name {
        name
    } else {
        project_state::get_current_project_name(&app_handle)?
    };

    logging::log_info(
        "PluginWriter",
        &format!("写入插件文件，项目: {}", project_name),
    );

    // 构建插件文件路径
    let plugin_filename = format!("RPGEditor_{}_PrototypeModifications.js", project_name);

    // 1. 写入到临时目录
    let temp_dir = get_temp_plugins_dir(&app_handle)?;
    let project_temp_dir = temp_dir.join(&project_name);
    path::ensure_dir_exists(&project_temp_dir)?;
    let temp_plugin_path = project_temp_dir.join(&plugin_filename);

    file_ops::write_file_safe(&temp_plugin_path, &plugin_code)?;
    logging::log_info(
        "PluginWriter",
        &format!("插件已写入临时目录: {:?}", temp_plugin_path),
    );

    // 2. 复制到项目目录
    let project_plugins_dir = path::get_project_plugins_dir(&project_name)?;
    path::ensure_dir_exists(&project_plugins_dir)?;
    let project_plugin_path = project_plugins_dir.join("RPGEditor_PrototypeModifications.js");

    file_ops::write_file_safe(&project_plugin_path, &plugin_code)?;
    logging::log_info(
        "PluginWriter",
        &format!("插件已复制到项目目录: {:?}", project_plugin_path),
    );

    // 3. 复制到引擎目录
    let engine_plugins_dir = get_engine_plugins_dir()?;
    path::ensure_dir_exists(&engine_plugins_dir)?;
    let engine_plugin_path = engine_plugins_dir.join("RPGEditor_PrototypeModifications.js");

    file_ops::write_file_safe(&engine_plugin_path, &plugin_code)?;
    logging::log_info(
        "PluginWriter",
        &format!("插件已复制到引擎目录: {:?}", engine_plugin_path),
    );

    Ok(format!("插件代码已成功保存到所有目录"))
}

/// 获取临时插件目录
fn get_temp_plugins_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {
    use crate::temp_plugins;
    temp_plugins::get_temp_plugins_dir(app_handle)
}

/// 获取引擎插件目录
fn get_engine_plugins_dir() -> Result<PathBuf, String> {
    use crate::utils::path;
    let project_root = path::get_project_root()?;
    Ok(project_root
        .join("src")
        .join("engine")
        .join("js")
        .join("plugins"))
}
