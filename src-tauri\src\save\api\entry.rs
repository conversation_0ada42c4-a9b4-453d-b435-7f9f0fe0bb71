use crate::utils::logging;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use tauri::AppHandle;

/// 保存插件代码的请求参数
#[derive(Debug, Serialize, Deserialize)]
pub struct SavePluginCodeRequest {
    pub plugin_code: String,
    pub project_name: Option<String>,
}

/// 新的简化API：保存前端生成的插件代码
/// 前端负责代码生成，后端只负责文件写入
#[tauri::command]
pub fn save_generated_plugin_code(
    app_handle: AppHandle,
    request: SavePluginCodeRequest,
) -> Result<String, String> {
    println!("🚀 [简化API] 收到前端生成的插件代码！");
    println!("   项目名称: {:?}", request.project_name);
    println!("   代码长度: {} 字符", request.plugin_code.len());

    logging::log_info(
        "SimplifiedAPI",
        &format!(
            "保存前端生成的插件代码 - 项目: {:?}, 代码长度: {}",
            request.project_name,
            request.plugin_code.len()
        ),
    );

    // 直接调用插件写入逻辑
    write_plugin_file(app_handle, request.plugin_code, request.project_name)
}

/// 保存到临时目录的API
#[tauri::command]
pub fn save_to_temp_plugins(
    app_handle: AppHandle,
    request: SavePluginCodeRequest,
) -> Result<String, String> {
    println!("💾 [临时保存] 保存插件代码到临时目录！");
    println!("   项目名称: {:?}", request.project_name);
    println!("   代码长度: {} 字符", request.plugin_code.len());

    logging::log_info(
        "TempSaveAPI",
        &format!(
            "保存插件代码到临时目录 - 项目: {:?}, 代码长度: {}",
            request.project_name,
            request.plugin_code.len()
        ),
    );

    // 只写入到临时目录
    write_to_temp_only(app_handle, request.plugin_code, request.project_name)
}

/// 从临时目录刷新预览的API
#[tauri::command]
pub fn refresh_preview_from_temp(
    app_handle: AppHandle,
    project_name: Option<String>,
) -> Result<String, String> {
    println!("🔄 [刷新预览] 从临时目录复制到引擎和项目目录！");
    println!("   项目名称: {:?}", project_name);

    logging::log_info(
        "RefreshAPI",
        &format!("从临时目录刷新预览 - 项目: {:?}", project_name),
    );

    // 从临时目录复制到其他目录
    copy_from_temp_to_targets(app_handle, project_name)
}

/// 兼容性API：保存所有修改（为了兼容旧的前端调用）
#[tauri::command]
pub fn save_all_modifications() -> Result<String, String> {
    println!("⚠️ [兼容性API] 调用了旧的 save_all_modifications 命令");

    logging::log_info(
        "CompatibilityAPI",
        "调用了旧的 save_all_modifications 命令，请使用新的前端代码生成服务",
    );

    // 返回提示信息，建议使用新的API
    Ok("请使用新的前端代码生成服务进行保存".to_string())
}

/// 写入插件文件的核心逻辑
fn write_plugin_file(
    app_handle: AppHandle,
    plugin_code: String,
    project_name: Option<String>,
) -> Result<String, String> {
    use crate::utils::file_ops;
    use crate::utils::path;
    use crate::utils::project_state;
    use std::path::PathBuf;

    // 获取项目名称
    let project_name = if let Some(name) = project_name {
        name
    } else {
        project_state::get_current_project_name(&app_handle)?
    };

    logging::log_info(
        "PluginWriter",
        &format!("写入插件文件，项目: {}", project_name),
    );

    // 构建插件文件路径
    let plugin_filename = format!("RPGEditor_{}_PrototypeModifications.js", project_name);

    // 1. 写入到临时目录
    let temp_dir = get_temp_plugins_dir(&app_handle)?;
    let project_temp_dir = temp_dir.join(&project_name);
    path::ensure_dir_exists(&project_temp_dir)?;
    let temp_plugin_path = project_temp_dir.join(&plugin_filename);

    file_ops::write_file_safe(&temp_plugin_path, &plugin_code)?;
    logging::log_info(
        "PluginWriter",
        &format!("插件已写入临时目录: {:?}", temp_plugin_path),
    );

    // 2. 复制到项目目录
    let project_plugins_dir = path::get_project_plugins_dir(&project_name)?;
    path::ensure_dir_exists(&project_plugins_dir)?;
    let project_plugin_path = project_plugins_dir.join("RPGEditor_PrototypeModifications.js");

    file_ops::write_file_safe(&project_plugin_path, &plugin_code)?;
    logging::log_info(
        "PluginWriter",
        &format!("插件已复制到项目目录: {:?}", project_plugin_path),
    );

    // 3. 复制到引擎目录
    let engine_plugins_dir = get_engine_plugins_dir()?;
    path::ensure_dir_exists(&engine_plugins_dir)?;
    let engine_plugin_path = engine_plugins_dir.join("RPGEditor_PrototypeModifications.js");

    file_ops::write_file_safe(&engine_plugin_path, &plugin_code)?;
    logging::log_info(
        "PluginWriter",
        &format!("插件已复制到引擎目录: {:?}", engine_plugin_path),
    );

    Ok(format!("插件代码已成功保存到所有目录"))
}

/// 获取临时插件目录
fn get_temp_plugins_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {
    use crate::temp_plugins;
    temp_plugins::get_temp_plugins_dir(app_handle)
}

/// 只写入到临时目录
fn write_to_temp_only(
    app_handle: AppHandle,
    plugin_code: String,
    project_name: Option<String>,
) -> Result<String, String> {
    use crate::utils::file_ops;
    use crate::utils::path;
    use crate::utils::project_state;

    // 获取项目名称
    let project_name = if let Some(name) = project_name {
        name
    } else {
        project_state::get_current_project_name(&app_handle)?
    };

    logging::log_info(
        "TempWriter",
        &format!("写入插件文件到临时目录，项目: {}", project_name),
    );

    // 构建插件文件路径
    let plugin_filename = format!("RPGEditor_{}_PrototypeModifications.js", project_name);

    // 写入到临时目录
    let temp_dir = get_temp_plugins_dir(&app_handle)?;
    let project_temp_dir = temp_dir.join(&project_name);
    path::ensure_dir_exists(&project_temp_dir)?;
    let temp_plugin_path = project_temp_dir.join(&plugin_filename);

    file_ops::write_file_safe(&temp_plugin_path, &plugin_code)?;
    logging::log_info(
        "TempWriter",
        &format!("插件已写入临时目录: {:?}", temp_plugin_path),
    );

    Ok(format!("插件代码已保存到临时目录"))
}

/// 从临时目录复制到目标目录
fn copy_from_temp_to_targets(
    app_handle: AppHandle,
    project_name: Option<String>,
) -> Result<String, String> {
    use crate::utils::file_ops;
    use crate::utils::path;
    use crate::utils::project_state;
    use std::fs;

    // 获取项目名称
    let project_name = if let Some(name) = project_name {
        name
    } else {
        project_state::get_current_project_name(&app_handle)?
    };

    logging::log_info(
        "PreviewRefresh",
        &format!("刷新预览，项目: {}", project_name),
    );

    // 构建文件路径
    let plugin_filename = format!("RPGEditor_{}_PrototypeModifications.js", project_name);
    let temp_dir = get_temp_plugins_dir(&app_handle)?;
    let temp_plugin_path = temp_dir.join(&project_name).join(&plugin_filename);

    // 检查临时文件是否存在
    if !temp_plugin_path.exists() {
        return Err(format!("临时插件文件不存在: {:?}", temp_plugin_path));
    }

    // 读取临时文件内容
    let plugin_code = fs::read_to_string(&temp_plugin_path)
        .map_err(|e| format!("读取临时插件文件失败: {}", e))?;

    // 复制到项目目录
    let project_plugins_dir = path::get_project_plugins_dir(&project_name)?;
    path::ensure_dir_exists(&project_plugins_dir)?;
    let project_plugin_path = project_plugins_dir.join("RPGEditor_PrototypeModifications.js");

    file_ops::write_file_safe(&project_plugin_path, &plugin_code)?;
    logging::log_info(
        "PreviewRefresh",
        &format!("插件已复制到项目目录: {:?}", project_plugin_path),
    );

    // 复制到引擎目录
    let engine_plugins_dir = get_engine_plugins_dir()?;
    path::ensure_dir_exists(&engine_plugins_dir)?;
    let engine_plugin_path = engine_plugins_dir.join("RPGEditor_PrototypeModifications.js");

    file_ops::write_file_safe(&engine_plugin_path, &plugin_code)?;
    logging::log_info(
        "PreviewRefresh",
        &format!("插件已复制到引擎目录: {:?}", engine_plugin_path),
    );

    Ok(format!("预览已刷新，插件文件已复制到引擎和项目目录"))
}

/// 获取引擎插件目录
fn get_engine_plugins_dir() -> Result<PathBuf, String> {
    use crate::utils::path;
    let project_root = path::get_project_root()?;
    Ok(project_root
        .join("src")
        .join("engine")
        .join("js")
        .join("plugins"))
}
