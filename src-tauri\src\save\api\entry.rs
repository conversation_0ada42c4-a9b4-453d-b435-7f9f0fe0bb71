use super::router;
use crate::utils::logging;
use tauri::AppHandle;

/// 统一的修改记录入口API
/// 这是前端调用的唯一接口
///
/// 参数格式：{
///   operationMode: "TYPE" | "OBJECT",           // 操作模式：类型操作 vs 对象操作
///   operationType: "CREATE" | "DELETE" | "MODIFY", // 操作类型：增加、删除、修改
///   targetPath: string[],                       // 路径
///   fieldName: string,                          // 字段名字
///   fieldValue: any                             // 字段值
/// }
#[tauri::command]
pub fn record_unified_modification(
    app_handle: AppHandle,
    operation_info: serde_json::Value,
) -> Result<String, String> {
    logging::log_function_call!("UnifiedAPI", "record_unified_modification", &operation_info);

    // 解析基本参数
    let operation_mode = operation_info
        .get("operationMode")
        .and_then(|v| v.as_str())
        .ok_or("缺少操作模式 (operationMode)")?;

    let operation_type = operation_info
        .get("operationType")
        .and_then(|v| v.as_str())
        .ok_or("缺少操作类型 (operationType)")?;

    let field_name = operation_info
        .get("fieldName")
        .and_then(|v| v.as_str())
        .ok_or("缺少字段名称 (fieldName)")?;

    let field_value = operation_info
        .get("fieldValue")
        .ok_or("缺少字段值 (fieldValue)")?
        .clone();

    let target_path = operation_info
        .get("targetPath")
        .and_then(|v| v.as_array())
        .ok_or("缺少目标路径 (targetPath)")?;

    let path_strings: Vec<String> = target_path
        .iter()
        .filter_map(|v| v.as_str())
        .map(|s| s.to_string())
        .collect();

    let class_name = operation_info
        .get("className")
        .and_then(|v| v.as_str())
        .unwrap_or("Unknown");

    // 获取额外的上下文信息
    let top_level_type = operation_info
        .get("_operationContext")
        .and_then(|ctx| ctx.get("topLevelType"))
        .and_then(|v| v.as_str());

    println!("🚀 [UnifiedAPI] 收到统一修改请求！");
    println!("   操作模式: {}", operation_mode);
    println!("   操作类型: {}", operation_type);
    println!("   路径: {:?}", path_strings);
    println!("   字段: {}", field_name);
    println!("   类名: {}", class_name);

    logging::log_info(
        "UnifiedAPI",
        &format!(
            "收到统一修改请求 - 模式: {}, 类型: {}, 路径: {:?}, 字段: {}, 类名: {}",
            operation_mode, operation_type, path_strings, field_name, class_name
        ),
    );

    // 构建操作请求结构
    let operation_request = router::OperationRequest {
        operation_mode: operation_mode.to_string(),
        operation_type: operation_type.to_string(),
        target_path: path_strings,
        field_name: field_name.to_string(),
        field_value,
        class_name: class_name.to_string(),
        top_level_type: top_level_type.map(|s| s.to_string()),
    };

    // 路由到具体的操作处理器
    router::route_operation(app_handle, operation_request)
}

/// 新的简化API：保存前端生成的插件代码
/// 前端负责代码生成，后端只负责文件写入
#[tauri::command]
pub fn save_generated_plugin_code(
    app_handle: AppHandle,
    plugin_code: String,
    project_name: Option<String>,
) -> Result<String, String> {
    println!("🚀 [简化API] 收到前端生成的插件代码！");
    println!("   项目名称: {:?}", project_name);
    println!("   代码长度: {} 字符", plugin_code.len());

    logging::log_info(
        "SimplifiedAPI",
        &format!(
            "保存前端生成的插件代码 - 项目: {:?}, 代码长度: {}",
            project_name,
            plugin_code.len()
        ),
    );

    // 直接调用插件写入逻辑
    write_plugin_file(app_handle, plugin_code, project_name)
}

/// 写入插件文件的核心逻辑
fn write_plugin_file(
    app_handle: AppHandle,
    plugin_code: String,
    project_name: Option<String>,
) -> Result<String, String> {
    use crate::utils::file_ops;
    use crate::utils::path;
    use crate::utils::project_state;
    use std::path::PathBuf;

    // 获取项目名称
    let project_name = if let Some(name) = project_name {
        name
    } else {
        project_state::get_current_project_name(&app_handle)?
    };

    logging::log_info(
        "PluginWriter",
        &format!("写入插件文件，项目: {}", project_name),
    );

    // 构建插件文件路径
    let plugin_filename = format!("RPGEditor_{}_PrototypeModifications.js", project_name);

    // 1. 写入到临时目录
    let temp_dir = get_temp_plugins_dir(&app_handle)?;
    let project_temp_dir = temp_dir.join(&project_name);
    path::ensure_dir_exists(&project_temp_dir)?;
    let temp_plugin_path = project_temp_dir.join(&plugin_filename);

    file_ops::write_file_safe(&temp_plugin_path, &plugin_code)?;
    logging::log_info(
        "PluginWriter",
        &format!("插件已写入临时目录: {:?}", temp_plugin_path),
    );

    // 2. 复制到项目目录
    let project_plugins_dir = path::get_project_plugins_dir(&project_name)?;
    path::ensure_dir_exists(&project_plugins_dir)?;
    let project_plugin_path = project_plugins_dir.join("RPGEditor_PrototypeModifications.js");

    file_ops::write_file_safe(&project_plugin_path, &plugin_code)?;
    logging::log_info(
        "PluginWriter",
        &format!("插件已复制到项目目录: {:?}", project_plugin_path),
    );

    // 3. 复制到引擎目录
    let engine_plugins_dir = get_engine_plugins_dir()?;
    path::ensure_dir_exists(&engine_plugins_dir)?;
    let engine_plugin_path = engine_plugins_dir.join("RPGEditor_PrototypeModifications.js");

    file_ops::write_file_safe(&engine_plugin_path, &plugin_code)?;
    logging::log_info(
        "PluginWriter",
        &format!("插件已复制到引擎目录: {:?}", engine_plugin_path),
    );

    Ok(format!("插件代码已成功保存到所有目录"))
}

/// 获取临时插件目录
fn get_temp_plugins_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {
    use crate::temp_plugins;
    temp_plugins::get_temp_plugins_dir(app_handle)
}

/// 获取引擎插件目录
fn get_engine_plugins_dir() -> Result<PathBuf, String> {
    use crate::utils::path;
    let project_root = path::get_project_root()?;
    Ok(project_root
        .join("src")
        .join("engine")
        .join("js")
        .join("plugins"))
}
