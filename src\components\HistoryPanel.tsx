import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, List, ListItem, ListItemText, IconButton, Tooltip, Paper } from '@mui/material';
import UndoIcon from '@mui/icons-material/Undo';
import RedoIcon from '@mui/icons-material/Redo';
import DeleteIcon from '@mui/icons-material/Delete';
import useProjectStore, { Operation, OperationType } from '../store/Store';
// import { invoke } from '@tauri-apps/api/core';

const HistoryPanel: React.FC = () => {
  // 从全局状态获取历史记录和方法
  const history = useProjectStore(state => state.history);
  const clearHistory = useProjectStore(state => state.clearHistory);
  const emit = useProjectStore(state => state.emit);
  const on = useProjectStore(state => state.on);
  const off = useProjectStore(state => state.off);

  // 本地状态
  const [currentIndex, setCurrentIndex] = useState<number>(-1);
  const [loading, setLoading] = useState<boolean>(false);

  // 计算是否可以撤销/重做
  const canUndo = currentIndex >= 0;
  const canRedo = currentIndex < (history.length - 1);

  // 初始化
  useEffect(() => {
    // 如果有历史记录，设置当前索引为最后一条
    if (history.length > 0) {
      setCurrentIndex(history.length - 1);
    }
  }, [history.length]);

  // 监听 MenuBar 中的撤销/重做事件
  useEffect(() => {
    if (!on || !off) return;

    // 处理 MenuBar 中的撤销事件
    const handleTriggerUndo = () => {
      console.log('HistoryPanel - 收到 MenuBar 撤销事件');
      if (canUndo) {
        // 获取当前操作
        const operation = history[currentIndex];

        console.log('HistoryPanel - 撤销操作:', operation);

        // 更新当前索引
        setCurrentIndex(prevIndex => prevIndex - 1);

        // 触发撤销事件，通知其他组件
        emit('undo-operation', operation);
      }
    };

    // 处理 MenuBar 中的重做事件
    const handleTriggerRedo = () => {
      console.log('HistoryPanel - 收到 MenuBar 重做事件');
      if (canRedo) {
        // 更新当前索引
        const newIndex = currentIndex + 1;
        setCurrentIndex(newIndex);

        // 获取要重做的操作
        const operation = history[newIndex];

        console.log('HistoryPanel - 重做操作:', operation);

        // 触发重做事件，通知其他组件
        emit('redo-operation', operation);
      }
    };

    // 注册事件监听器
    on('trigger-undo', handleTriggerUndo);
    on('trigger-redo', handleTriggerRedo);

    // 清理函数
    return () => {
      off('trigger-undo', handleTriggerUndo);
      off('trigger-redo', handleTriggerRedo);
    };
  }, [canUndo, canRedo, currentIndex, history, emit, on, off]);

  // 处理撤销操作
  const handleUndo = useCallback(() => {
    if (!canUndo) return;

    setLoading(true);

    try {
      // 获取当前操作
      const operation = history[currentIndex];

      console.log('HistoryPanel - 撤销操作:', operation);

      // 更新当前索引
      setCurrentIndex(prevIndex => prevIndex - 1);

      // 触发撤销事件，通知其他组件
      emit('undo-operation', operation);

      // // 执行撤销逻辑
      // if (operation.operation_type === OperationType.MODIFY_PROPERTY && operation.details.propertyName) {
      //   // 调用后端 API 修改属性（使用旧值）
      //   invoke('record_property_modification', {
      //     objectPath: operation.object_path,
      //     className: operation.object_path[0],
      //     fieldName: operation.details.propertyName,
      //     value: operation.details.oldValue
      //   }).catch(error => {
      //     console.error('调用后端 API 修改属性失败:', error);
      //   });

      //   // 触发属性变更事件
      //   window.dispatchEvent(new CustomEvent('property-changed', {
      //     detail: {
      //       objectPath: operation.object_path,
      //       propertyName: operation.details.propertyName,
      //       value: operation.details.oldValue
      //     }
      //   }));
      // }
    } catch (error) {
      console.error('撤销操作失败:', error);
    } finally {
      setLoading(false);
    }
  }, [canUndo, currentIndex, history, emit]);

  // 处理重做操作
  const handleRedo = useCallback(() => {
    if (!canRedo) return;

    setLoading(true);

    try {
      // 更新当前索引
      const newIndex = currentIndex + 1;
      setCurrentIndex(newIndex);

      // 获取要重做的操作
      const operation = history[newIndex];

      console.log('HistoryPanel - 重做操作:', operation);

      // 触发重做事件，通知其他组件
      emit('redo-operation', operation);
    } catch (error) {
      console.error('重做操作失败:', error);
    } finally {
      setLoading(false);
    }
  }, [canRedo, currentIndex, history, emit]);

  // 处理清空历史记录
  const handleClearHistory = useCallback(() => {
    try {
      clearHistory();
      setCurrentIndex(-1);
    } catch (error) {
      console.error('清空历史记录失败:', error);
    }
  }, [clearHistory]);

  // 格式化操作描述
  const formatOperation = (operation: Operation) => {
    const { operation_type, object_path, details } = operation;

    // 格式化值的显示
    const formatValue = (value: any): string => {
      if (value === undefined) return "未定义";
      if (value === null) return "空值";
      if (typeof value === 'object') {
        try {
          return JSON.stringify(value);
        } catch (e) {
          return String(value);
        }
      }
      return String(value);
    };

    switch (operation_type) {
      case OperationType.MODIFY_PROPERTY:
        return `修改 ${object_path.join('/')} 的 ${details.propertyName || '属性'} 从 ${formatValue(details.oldValue)} 到 ${formatValue(details.newValue)}`;
      case OperationType.ADD_OBJECT:
        return `添加对象 ${object_path.join('/')}`;
      case OperationType.DELETE_OBJECT:
        return `删除对象 ${object_path.join('/')}`;
      default:
        return `未知操作: ${operation_type}`;
    }
  };

  // 格式化时间戳
  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleTimeString();
  };

  return (
    <Box className="history-panel" sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ display: 'flex', alignItems: 'center', padding: 1, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" sx={{ flexGrow: 1 }}>历史记录</Typography>
        <Tooltip title="撤销 (Ctrl+Z)">
          <span>
            <IconButton
              className="undo-button"
              onClick={handleUndo}
              disabled={!canUndo}
              size="small"
            >
              <UndoIcon />
            </IconButton>
          </span>
        </Tooltip>
        <Tooltip title="重做 (Ctrl+Y)">
          <span>
            <IconButton
              className="redo-button"
              onClick={handleRedo}
              disabled={!canRedo}
              size="small"
            >
              <RedoIcon />
            </IconButton>
          </span>
        </Tooltip>
        <Tooltip title="清空历史记录">
          <span>
            <IconButton
              className="clear-button"
              onClick={handleClearHistory}
              disabled={history.length === 0}
              size="small"
            >
              <DeleteIcon />
            </IconButton>
          </span>
        </Tooltip>
      </Box>

      <Paper sx={{ flex: 1, overflow: 'auto', m: 1 }}>
        {loading ? (
          <Box sx={{ p: 2, textAlign: 'center' }}>
            <Typography>加载中...</Typography>
          </Box>
        ) : history.length === 0 ? (
          <Box sx={{ p: 2, textAlign: 'center' }}>
            <Typography>暂无操作记录</Typography>
          </Box>
        ) : (
          <List>
            {history.map((operation, index) => (
              <ListItem
                key={operation.id}
                sx={{
                  opacity: index <= currentIndex ? 1 : 0.5,
                  bgcolor: index === currentIndex ? 'rgba(25, 118, 210, 0.12)' : 'inherit',
                  '&:hover': {
                    bgcolor: index === currentIndex ? 'rgba(25, 118, 210, 0.18)' : 'rgba(0, 0, 0, 0.04)'
                  }
                }}
              >
                <ListItemText
                  primary={formatOperation(operation)}
                  secondary={formatTimestamp(operation.timestamp)}
                />
              </ListItem>
            ))}
          </List>
        )}
      </Paper>
    </Box>
  );
};

export default HistoryPanel;
