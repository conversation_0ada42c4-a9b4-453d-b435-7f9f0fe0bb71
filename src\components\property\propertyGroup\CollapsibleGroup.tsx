import React, { useState, memo } from 'react';
import { Box, Typography, IconButton, Collapse } from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';

interface CollapsibleGroupProps {
  title: React.ReactNode;
  defaultExpanded?: boolean;
  children: React.ReactNode;
  onToggle?: () => void;
  expanded?: boolean;
}

const CollapsibleGroup: React.FC<CollapsibleGroupProps> = memo(({
  title,
  defaultExpanded = true,
  children,
  onToggle,
  expanded: controlledExpanded
}) => {
  const [internalExpanded, setInternalExpanded] = useState(defaultExpanded);

  // 使用外部控制的状态或内部状态
  const expanded = controlledExpanded !== undefined ? controlledExpanded : internalExpanded;

  const toggleExpanded = () => {
    if (onToggle) {
      onToggle();
    } else {
      setInternalExpanded(!internalExpanded);
    }
  };

  return (
    <Box sx={{ mb: 1, border: '1px solid #eee', borderRadius: 1 }}>
      {/* 标题栏 */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: 0.5,
          pl: 1,
          bgcolor: '#f5f5f5',
          borderBottom: expanded ? '1px solid #eee' : 'none',
          borderRadius: '4px 4px 0 0',
          cursor: 'pointer'
        }}
        onClick={toggleExpanded}
      >
        {typeof title === 'string' ? (
          <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
            {title}
          </Typography>
        ) : (
          <Box sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>
            {title}
          </Box>
        )}
        <IconButton size="small" onClick={(e) => {
          e.stopPropagation();
          toggleExpanded();
        }}>
          {expanded ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
        </IconButton>
      </Box>

      {/* 内容区域 */}
      <Collapse in={expanded}>
        <Box sx={{ p: 1 }}>
          {children}
        </Box>
      </Collapse>
    </Box>
  );
}, (prevProps, nextProps) => {
  // 只有当title或children发生变化时才重新渲染
  return prevProps.title === nextProps.title &&
    prevProps.children === nextProps.children &&
    prevProps.defaultExpanded === nextProps.defaultExpanded;
});

export default CollapsibleGroup;
