// ==================== 新的模块化架构 ====================

/// 统一API模块 - 前端调用的唯一入口
pub mod api;

/// 数据管理模块 - 数据结构和修改记录管理
pub mod data;

/// 代码生成器模块 - 各种代码生成器
pub mod generators;

/// 文件处理模块 - 文件读写和解析
pub mod files;

// ==================== 兼容性导出（保持现有代码正常工作）====================

pub mod bitmap_generator;
pub mod filter_generator;
pub mod modification_manager;
pub mod object_lifecycle_generator;
pub mod path_utils;
pub mod plugin_generator;
pub mod plugin_parser;
pub mod plugins_file;
pub mod save;
pub mod types;

// 重新导出主要的公共接口
// pub use save::{record_property_modification, save_all_modifications};
