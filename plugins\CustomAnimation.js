/*:
 * @target MZ
 * @plugindesc 自定义动画库 - 为RPG Maker MZ提供轻量级动画功能
 * <AUTHOR> Editor
 * @help
 * ============================================================================
 * 自定义动画库插件 v1.0.0
 * ============================================================================
 *
 * 这个插件提供了一个轻量级的动画系统，不依赖任何外部库，
 * 使用RPG Maker MZ内置的update机制进行更新。
 *
 * 特性:
 * - 支持对象自身动画和对象数组动画
 * - 提供多种缓动函数
 * - 支持链式调用和动画序列
 * - 支持动画的暂停、恢复、重启和停止
 * - 轻量级设计，性能优良
 *
 * 使用方法:
 * 1. 获取对象: const obj = findObjectByScenePath(SceneManager._scene, ["Scene_Title", "2"]);
 * 2. 创建动画: AnimationManager.animate(obj, { x: 100, alpha: 0.5 }, 1000, "easeOutElastic");
 * 3. 对象数组动画: AnimationManager.animate([obj1, obj2], { y: "+50", alpha: 0.8 }, 800, "easeInOutQuad");
 *
 * 高级用法:
 * - 动画序列: AnimationManager.sequence(obj, [{ x: 100 }, { y: 200 }, { alpha: 0 }], 500);
 * - 动画回调: AnimationManager.animate(obj, { x: 100 }, 1000, "linear", {
 *     onStart: () => console.log("动画开始"),
 *     onComplete: () => console.log("动画完成")
 *   });
 *
 * ============================================================================
 */

(function () {
    'use strict';

    //=============================================================================
    // 缓动函数库
    //=============================================================================

    const Easing = {
        // 线性
        linear: function (t) {
            return t;
        },

        // 二次方
        easeInQuad: function (t) {
            return t * t;
        },
        easeOutQuad: function (t) {
            return t * (2 - t);
        },
        easeInOutQuad: function (t) {
            return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
        },

        // 三次方
        easeInCubic: function (t) {
            return t * t * t;
        },
        easeOutCubic: function (t) {
            return (--t) * t * t + 1;
        },
        easeInOutCubic: function (t) {
            return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
        },

        // 四次方
        easeInQuart: function (t) {
            return t * t * t * t;
        },
        easeOutQuart: function (t) {
            return 1 - (--t) * t * t * t;
        },
        easeInOutQuart: function (t) {
            return t < 0.5 ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t;
        },

        // 弹性
        easeInElastic: function (t) {
            return (.04 - .04 / t) * Math.sin(25 * t) + 1;
        },
        easeOutElastic: function (t) {
            return .04 * t / (--t) * Math.sin(25 * t);
        },
        easeInOutElastic: function (t) {
            return (t -= .5) < 0 ? (.02 + .01 / t) * Math.sin(50 * t) : (.02 - .01 / t) * Math.sin(50 * t) + 1;
        },

        // 反弹
        easeInBounce: function (t) {
            return 1 - Easing.easeOutBounce(1 - t);
        },
        easeOutBounce: function (t) {
            if (t < (1 / 2.75)) {
                return 7.5625 * t * t;
            } else if (t < (2 / 2.75)) {
                return 7.5625 * (t -= (1.5 / 2.75)) * t + 0.75;
            } else if (t < (2.5 / 2.75)) {
                return 7.5625 * (t -= (2.25 / 2.75)) * t + 0.9375;
            } else {
                return 7.5625 * (t -= (2.625 / 2.75)) * t + 0.984375;
            }
        },
        easeInOutBounce: function (t) {
            return t < 0.5 ? Easing.easeInBounce(t * 2) * 0.5 : Easing.easeOutBounce(t * 2 - 1) * 0.5 + 0.5;
        },

        // 正弦
        easeInSine: function (t) {
            return 1 - Math.cos(t * Math.PI / 2);
        },
        easeOutSine: function (t) {
            return Math.sin(t * Math.PI / 2);
        },
        easeInOutSine: function (t) {
            return -(Math.cos(Math.PI * t) - 1) / 2;
        }
    };

    //=============================================================================
    // Animation类 - 处理单个动画
    //=============================================================================

    class Animation {
        constructor(targets, properties, duration, easing, options = {}) {
            this.targets = Array.isArray(targets) ? targets : [targets];
            this.properties = properties;
            this.duration = duration;
            this.easing = typeof easing === 'function' ? easing : (Easing[easing] || Easing.linear);
            this.options = options;

            this.startTime = null;
            this.elapsedTime = 0;
            this.isPlaying = false;
            this.isPaused = false;
            this.isCompleted = false;

            this.startValues = {};
            this.endValues = {};
            this.deltaValues = {};

            this.id = Date.now().toString() + Math.floor(Math.random() * 1000);

            this._init();
        }

        _init() {
            // 初始化起始值和结束值
            this.targets.forEach(target => {
                if (!target) return;

                this.startValues[target.id] = {};
                this.endValues[target.id] = {};
                this.deltaValues[target.id] = {};

                for (const prop in this.properties) {
                    let startValue = target[prop];
                    let endValue = this.properties[prop];

                    // 处理相对值 ("+10", "-20")
                    if (typeof endValue === 'string' && (endValue.startsWith('+') || endValue.startsWith('-'))) {
                        endValue = startValue + parseFloat(endValue);
                    }

                    this.startValues[target.id][prop] = startValue;
                    this.endValues[target.id][prop] = endValue;
                    this.deltaValues[target.id][prop] = endValue - startValue;
                }
            });
        }

        start() {
            this.startTime = Date.now();
            this.isPlaying = true;
            this.isPaused = false;
            this.isCompleted = false;

            if (this.options.onStart) {
                this.options.onStart();
            }

            return this;
        }

        update() {
            if (!this.isPlaying || this.isPaused || this.isCompleted) return;

            const currentTime = Date.now();
            this.elapsedTime = currentTime - this.startTime;

            // 检查动画是否完成
            if (this.elapsedTime >= this.duration) {
                this._complete();
                return;
            }

            // 计算当前进度
            const progress = this.elapsedTime / this.duration;
            const easedProgress = this.easing(progress);

            // 更新目标属性
            this.targets.forEach(target => {
                if (!target) return;

                for (const prop in this.properties) {
                    const start = this.startValues[target.id][prop];
                    const delta = this.deltaValues[target.id][prop];
                    target[prop] = start + delta * easedProgress;
                }
            });

            if (this.options.onUpdate) {
                this.options.onUpdate(progress);
            }
        }

        _complete() {
            // 设置最终值
            this.targets.forEach(target => {
                if (!target) return;

                for (const prop in this.properties) {
                    target[prop] = this.endValues[target.id][prop];
                }
            });

            this.isPlaying = false;
            this.isCompleted = true;

            if (this.options.onComplete) {
                this.options.onComplete();
            }
        }

        pause() {
            if (this.isPlaying && !this.isPaused) {
                this.isPaused = true;

                if (this.options.onPause) {
                    this.options.onPause();
                }
            }
            return this;
        }

        resume() {
            if (this.isPlaying && this.isPaused) {
                this.isPaused = false;
                this.startTime = Date.now() - this.elapsedTime;

                if (this.options.onResume) {
                    this.options.onResume();
                }
            }
            return this;
        }

        stop() {
            this.isPlaying = false;
            this.isPaused = false;

            if (this.options.onStop) {
                this.options.onStop();
            }
            return this;
        }

        restart() {
            this.elapsedTime = 0;
            this.isCompleted = false;
            this.isPaused = false;
            return this.start();
        }
    }

    //=============================================================================
    // AnimationManager类 - 管理所有动画
    //=============================================================================

    const AnimationManager = {
        animations: [],

        /**
         * 创建并开始一个新动画
         * @param {Object|Array} targets - 动画目标对象或对象数组
         * @param {Object} properties - 要动画的属性
         * @param {number} duration - 动画持续时间(毫秒)
         * @param {string|Function} easing - 缓动函数名称或函数
         * @param {Object} options - 其他选项(回调等)
         * @returns {Animation} 动画实例
         */
        animate: function (targets, properties, duration = 1000, easing = 'linear', options = {}) {
            const animation = new Animation(targets, properties, duration, easing, options);
            this.animations.push(animation);
            animation.start();
            return animation;
        },

        /**
         * 创建并开始一个动画序列
         * @param {Object|Array} targets - 动画目标对象或对象数组
         * @param {Array} propertiesArray - 属性数组
         * @param {number} duration - 每个动画的持续时间
         * @param {string|Function} easing - 缓动函数名称或函数
         * @param {Object} options - 其他选项(回调等)
         * @returns {Array} 动画实例数组
         */
        sequence: function (targets, propertiesArray, duration = 1000, easing = 'linear', options = {}) {
            const animations = [];
            let delay = 0;

            propertiesArray.forEach((props, index) => {
                const sequenceOptions = { ...options };

                // 设置延迟
                sequenceOptions.delay = delay;

                // 创建动画
                const animation = this.animate(targets, props, duration, easing, sequenceOptions);
                animations.push(animation);

                // 增加延迟
                delay += duration;
            });

            return animations;
        },

        /**
         * 更新所有动画
         */
        update: function () {
            for (let i = this.animations.length - 1; i >= 0; i--) {
                const animation = this.animations[i];
                animation.update();

                // 移除已完成的动画
                if (animation.isCompleted) {
                    this.animations.splice(i, 1);
                }
            }
        },

        /**
         * 停止所有动画
         */
        stopAll: function () {
            this.animations.forEach(animation => animation.stop());
            this.animations = [];
        },

        /**
         * 停止特定目标的所有动画
         * @param {Object} target - 目标对象
         */
        stopByTarget: function (target) {
            for (let i = this.animations.length - 1; i >= 0; i--) {
                const animation = this.animations[i];
                if (animation.targets.includes(target)) {
                    animation.stop();
                    this.animations.splice(i, 1);
                }
            }
        }
    };

    //=============================================================================
    // 扩展对象原型，添加动画方法
    //=============================================================================

    /**
     * 为对象添加唯一ID
     */
    const addUniqueId = function (obj) {
        if (!obj.id) {
            obj.id = 'obj_' + Date.now().toString() + Math.floor(Math.random() * 1000);
        }
    };

    /**
     * 扩展对象，添加动画方法
     * @param {Object} obj - 要扩展的对象
     */
    const extendWithAnimationMethods = function (obj) {
        if (!obj) return;

        // 添加唯一ID
        addUniqueId(obj);

        // 添加动画方法
        if (!obj.animate) {
            obj.animate = function (properties, duration, easing, options) {
                return AnimationManager.animate(this, properties, duration, easing, options);
            };
        }

        // 添加序列动画方法
        if (!obj.animateSequence) {
            obj.animateSequence = function (propertiesArray, duration, easing, options) {
                return AnimationManager.sequence(this, propertiesArray, duration, easing, options);
            };
        }

        // 添加停止动画方法
        if (!obj.stopAnimations) {
            obj.stopAnimations = function () {
                AnimationManager.stopByTarget(this);
            };
        }
    };

    /**
     * 扩展容器，添加子元素动画方法
     * @param {Object} container - 要扩展的容器
     */
    const extendContainerWithAnimationMethods = function (container) {
        if (!container || !container.children) return;

        // 添加子元素动画方法
        if (!container.animateChildren) {
            container.animateChildren = function (properties, duration, easing, options) {
                const visibleChildren = this.children.filter(child => {
                    return child && !child._hidden && child.visible !== false;
                });

                return AnimationManager.animate(visibleChildren, properties, duration, easing, options);
            };
        }

        // 添加停止子元素动画方法
        if (!container.stopChildrenAnimations) {
            container.stopChildrenAnimations = function () {
                this.children.forEach(child => {
                    if (child) {
                        AnimationManager.stopByTarget(child);
                    }
                });
            };
        }
    };

    //=============================================================================
    // 将AnimationManager挂载到SceneManager的update循环中
    //=============================================================================

    // 保存原始的SceneManager.updateScene方法
    const _SceneManager_updateScene = SceneManager.updateScene;

    // 重写SceneManager.updateScene方法，添加动画更新
    SceneManager.updateScene = function () {
        // 调用原始方法
        _SceneManager_updateScene.call(this);

        // 更新动画
        AnimationManager.update();
    };

    //=============================================================================
    // 导出到全局作用域
    //=============================================================================

    // 将AnimationManager导出到全局作用域
    window.AnimationManager = AnimationManager;

    // 导出辅助方法
    window.extendWithAnimationMethods = extendWithAnimationMethods;
    window.extendContainerWithAnimationMethods = extendContainerWithAnimationMethods;

    //=============================================================================
    // 初始化
    //=============================================================================

    // 在游戏加载完成后初始化
    const _Scene_Boot_start = Scene_Boot.prototype.start;
    Scene_Boot.prototype.start = function () {
        _Scene_Boot_start.call(this);

        // 输出初始化信息
        console.log('CustomAnimation插件初始化完成');
    };
})();