Scene_Title 的继承链：
Scene_Title 继承自 Scene_Base
Scene_Base 继承自 Stage
Stage 继承自 PIXI.Container


根据提供的代码内容，我可以看到角色数据的获取流程如下：

1. 数据文件加载：
- 游戏启动时，通过 DataManager.loadDatabase() 加载所有数据文件
- 角色数据存储在 "data/Actors.json" 中，加载后保存在全局变量 $dataActors 中
2. 数据加载流程：
3. 游戏对象创建：
- 通过 DataManager.createGameObjects() 创建游戏对象
- 角色相关的对象有:
  - $gameActors = new Game_Actors() - 管理所有角色
  - $gameParty = new Game_Party() - 管理队伍成员
4. 数据访问方式：
- 静态数据: $dataActors[actorId] - 获取角色的基础数据
- 动态数据: $gameActors.actor(actorId) - 获取角色的游戏实例
所以要获取角色数据，你可以：

1. 获取角色基础数据：
javascript

Open Folder

1

const actorData = $dataActors

[ actorId ] ; // 获取角色的静态数据

2. 获取角色游戏实例：
javascript

Open Folder

1

const actor = $gameActors . actor

( actorId ) ; // 获取角色的动态数据

3. 获取当前队伍成员：
javascript

Open Folder

1

const partyMembers = $gameParty . members

( ) ; // 获取所有队伍成员

主要数据文件位置：

- 角色基础数据： `Actors.json`
- 职业数据： `Classes.json`
- 技能数据： `Skills.json`



TouchInput类：
在TouchInput._setupEventHandlers方法中，事件监听器是添加到document上的，而不是canvas上
关键事件处理方法包括_onTouchStart、_onTouchMove、_onTouchEnd等
使用Graphics.pageToCanvasX/Y方法将页面坐标转换为canvas坐标
Input类：
同样，事件监听器是添加到document上的
处理键盘和游戏手柄输入
维护按键状态和方向输入
Graphics类：
提供了pageToCanvasX/Y方法，用于坐标转换
提供了isInsideCanvas方法，用于检查点是否在canvas内



从逻辑对象找到对应的精灵：使用Spriteset_Map的findTargetSprite方法
从精灵找到对应的逻辑对象：直接访问精灵的_character属性


// 加载图片
const newBitmap = ImageManager.loadBitmapFromUrl(fullPath);
newBitmap.addLoadListener(function (bitmap) {
    // 设置bitmap（这会自动触发必要的更新）
    targetObject_sprite_Scene_Title_0_56135.bitmap = bitmap;
});


注意了 接下来非常重要1.类型树中默认选中的是对象那么修改后发生给后端保存的时候修改的就是具体的对象，
后端生成代码时还是原来的样子先在场景中找到具体的对象在修改该对象，2.如果类型树中选择的是类型，
当前显示面板还是修改当前对象的属性，但是后端处理时就是修改都是基于当前类型修改，不是具体的对象了，
后端生成面板代码的时候就不能在场景中查找具体的对象了而是，重写当前修改的类型