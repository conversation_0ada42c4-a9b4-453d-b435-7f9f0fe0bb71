import { Box, Typography, Chip, Tooltip } from '@mui/material';
import FolderIcon from '@mui/icons-material/Folder';
import useProjectStore from '../store/Store';

const ProjectInfo = () => {
  const { projectName, projectPath, isProjectLoaded } = useProjectStore();

  if (!isProjectLoaded) {
    return (
      <Box sx={{ p: 1, display: 'flex', alignItems: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          未加载项目
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 1, display: 'flex', alignItems: 'center', overflow: 'hidden' }}>
      <FolderIcon sx={{ mr: 1, color: 'primary.main', fontSize: 20 }} />
      <Typography 
        variant="body2" 
        sx={{ 
          fontWeight: 'bold',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          mr: 1
        }}
      >
        {projectName}
      </Typography>
      <Tooltip title={projectPath} arrow>
        <Chip 
          label={projectPath.split('/').pop() || projectPath} 
          size="small" 
          variant="outlined"
          sx={{ 
            maxWidth: 150,
            '& .MuiChip-label': {
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }
          }}
        />
      </Tooltip>
    </Box>
  );
};

export default ProjectInfo;
