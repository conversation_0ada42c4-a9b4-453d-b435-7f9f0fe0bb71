/**
 * 基础代码生成器抽象类
 * 提供所有生成器的通用功能和接口
 */

import {
  OperationInfo,
  CodeSnippet,
  GeneratorConfig,
  GenerationContext,
  CodeGenerationError
} from './types';

/**
 * 基础生成器抽象类
 */
export abstract class BaseGenerator {
  protected config: GeneratorConfig;
  protected context: GenerationContext;

  constructor(config: Partial<GeneratorConfig> = {}) {
    this.config = {
      debug: false,
      minify: false,
      addComments: true,
      variablePrefix: 'rpgEditor_',
      indentSize: 2,
      ...config
    };

    this.context = {
      config: this.config,
      variableCounter: 0,
      generatedVariables: new Map()
    };
  }

  /**
   * 生成代码的主要方法，子类必须实现
   */
  abstract generate(operation: OperationInfo): CodeSnippet;

  /**
   * 检查是否支持该操作
   */
  abstract canHandle(operation: OperationInfo): boolean;

  /**
   * 生成唯一变量名
   */
  protected generateVariableName(prefix: string = 'var'): string {
    const counter = ++this.context.variableCounter;
    const timestamp = Date.now() % 10000;
    return `${this.config.variablePrefix}${prefix}_${timestamp}_${counter}`;
  }

  /**
   * 获取对象的变量名（如果已存在则复用）
   */
  protected getObjectVariableName(objectPath: string[], objectType: string): string {
    const pathKey = objectPath.join('_');
    
    if (this.context.generatedVariables.has(pathKey)) {
      return this.context.generatedVariables.get(pathKey)!;
    }

    const variableName = this.generateVariableName(objectType.toLowerCase());
    this.context.generatedVariables.set(pathKey, variableName);
    return variableName;
  }

  /**
   * 序列化值为JavaScript代码
   */
  protected serializeValue(value: any): string {
    if (value === null) return 'null';
    if (value === undefined) return 'undefined';
    if (typeof value === 'string') return `"${value.replace(/"/g, '\\"')}"`;
    if (typeof value === 'number') return value.toString();
    if (typeof value === 'boolean') return value.toString();
    if (Array.isArray(value)) {
      const items = value.map(item => this.serializeValue(item));
      return `[${items.join(', ')}]`;
    }
    if (typeof value === 'object') {
      const pairs = Object.entries(value).map(([key, val]) => 
        `${key}: ${this.serializeValue(val)}`
      );
      return `{${pairs.join(', ')}}`;
    }
    return JSON.stringify(value);
  }

  /**
   * 添加缩进
   */
  protected indent(code: string, level: number = 1): string {
    const indentStr = ' '.repeat(this.config.indentSize * level);
    return code.split('\n').map(line => 
      line.trim() ? indentStr + line : line
    ).join('\n');
  }

  /**
   * 添加注释
   */
  protected addComment(comment: string): string {
    if (!this.config.addComments) return '';
    return `// ${comment}`;
  }

  /**
   * 添加调试日志
   */
  protected addDebugLog(message: string, ...args: any[]): string {
    if (!this.config.debug) return '';
    const argsStr = args.length > 0 ? `, ${args.map(arg => this.serializeValue(arg)).join(', ')}` : '';
    return `if (DEBUG) console.log("${message}"${argsStr});`;
  }

  /**
   * 生成对象查找代码
   */
  protected generateObjectLookup(objectPath: string[], variableName: string): string {
    const pathStr = objectPath.map(p => `"${p}"`).join(', ');
    return [
      `const ${variableName}Path = [${pathStr}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`,
      this.addDebugLog(`找到对象: \${${variableName} ? ${variableName}.constructor.name : 'null'}`, `${variableName}Path`)
    ].filter(Boolean).join('\n');
  }

  /**
   * 生成条件检查代码
   */
  protected generateObjectCheck(variableName: string, code: string): string {
    return [
      `if (${variableName}) {`,
      this.indent(code),
      '}'
    ].join('\n');
  }

  /**
   * 验证操作信息
   */
  protected validateOperation(operation: OperationInfo): void {
    if (!operation.targetObject) {
      throw new CodeGenerationError('目标对象不能为空', operation, this.context);
    }
    if (!operation.objectPath || operation.objectPath.length === 0) {
      throw new CodeGenerationError('对象路径不能为空', operation, this.context);
    }
    if (!operation.className) {
      throw new CodeGenerationError('类名不能为空', operation, this.context);
    }
  }

  /**
   * 获取对象类型
   */
  protected getObjectType(obj: any): string {
    if (!obj) return 'Unknown';
    return obj.constructor?.name || 'Unknown';
  }

  /**
   * 检查是否为特殊属性
   */
  protected isSpecialProperty(propertyName: string, objectType: string): boolean {
    // 子类可以重写此方法来定义特殊属性
    return false;
  }

  /**
   * 生成错误处理代码
   */
  protected generateErrorHandling(variableName: string, operation: string): string {
    if (!this.config.debug) return '';
    
    return [
      'try {',
      this.indent(operation),
      '} catch (error) {',
      this.indent(`console.error("操作失败:", error, ${variableName});`),
      '}'
    ].join('\n');
  }

  /**
   * 重置生成器状态
   */
  public reset(): void {
    this.context.variableCounter = 0;
    this.context.generatedVariables.clear();
    this.context.currentScene = undefined;
    this.context.currentClass = undefined;
  }

  /**
   * 获取生成器统计信息
   */
  public getStats() {
    return {
      variableCount: this.context.variableCounter,
      generatedVariables: Array.from(this.context.generatedVariables.keys()),
      currentScene: this.context.currentScene,
      currentClass: this.context.currentClass
    };
  }
}
