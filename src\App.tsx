import { Box, Typography } from "@mui/material";
import { Resizable } from "re-resizable";
import { useEffect, useState } from "react";
import {
  DndContext,
  DragEndEvent,
  DragStartEvent,
  DragOverEvent,
  PointerSensor,
  useSensor,
  useSensors
} from '@dnd-kit/core';
import useGlobalEvents from "./hooks/useGlobalEvents";

import "./App.css";
import MenuBar from "./components/MenuPanel/MenuBar";
import LeftPanel from "./components/leftPanel/LeftPanel";
import CenterPanel from "./components/centerPanel/CenterPanel";
import BottomPanel from "./components/BottomPanel";
import RightPanel from "./components/rightPanel/RightPanel";

import ProjectSelector from "./components/ProjectSelector";

import useProjectStore from "./store/Store";
import initSelectionVisualizer from "./utils/selection/SelectionVisualizer";
import { scan } from "react-scan";
// import initSelectionVisualizer from "./utils/SelectionVisualizer";

// // // 启用React-scan性能监控
// scan({
//   enabled: true,
//   // log: true,
//   showFPS: true,
//   showToolbar: true,
// });

// 项目信息在 Store.ts 中定义

function App() {
  console.log("app...")
  // 使用全局状态管理
  const projectSelected = useProjectStore(state => state.projectSelected);

  // 配置拖拽传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      // 配置拖拽激活约束，防止误触发
      activationConstraint: {
        distance: 5, // 至少移动 5px 才激活拖拽
      },
    })
  );

  // 拖拽开始事件处理
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    console.log('App: 拖拽开始', active.id, active.data.current);
  };

  // 拖拽悬停事件处理
  const handleDragOver = (event: DragOverEvent) => {
    const { active, over } = event;
    if (over) {
      console.log('App: 拖拽悬停', active.id, '悬停在', over.id, over.data.current);
    }
  };

  // 拖拽结束事件处理
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over) {
      console.log('App: 拖拽结束', active.id, '放置到', over.id);
      console.log('App: 拖拽项数据', active.data.current);
      console.log('App: 放置目标数据', over.data.current);

      // 分发自定义事件，通知组件拖拽结束
      const customEvent = new CustomEvent('dnd-kit-drag-end', {
        detail: {
          active,
          over
        }
      });
      window.dispatchEvent(customEvent);
    }
  };

  // 使用全局事件处理钩子
  // 只有在选择了项目后才启用全局事件处理
  useGlobalEvents(projectSelected);

  // // 初始化选中对象可视化工具
  // useEffect(() => {
  //   if (projectSelected) {
  //     // 初始化选中对象可视化
  //     initSelectionVisualizer();
  //     console.log('App: 选中对象可视化工具已初始化');
  //   }
  // }, [projectSelected]);

  // 添加全局快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 只有在选择了项目后才处理快捷键
      if (!projectSelected) return;

      // Ctrl+Z: 撤销
      if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        try {
          // 查找 HistoryPanel 组件
          const historyPanel = document.querySelector('.history-panel');
          if (historyPanel) {
            // 触发撤销按钮点击
            const undoButton = historyPanel.querySelector('.undo-button');
            if (undoButton) {
              (undoButton as HTMLElement).click();
              console.log('App: 触发撤销操作');
            }
          }
        } catch (error) {
          console.error('App: 撤销操作失败', error);
        }
      }
      // Ctrl+Shift+Z 或 Ctrl+Y: 重做
      else if ((e.ctrlKey && e.shiftKey && e.key === 'z') || (e.ctrlKey && e.key === 'y')) {
        e.preventDefault();
        try {
          // 查找 HistoryPanel 组件
          const historyPanel = document.querySelector('.history-panel');
          if (historyPanel) {
            // 触发重做按钮点击
            const redoButton = historyPanel.querySelector('.redo-button');
            if (redoButton) {
              (redoButton as HTMLElement).click();
              console.log('App: 触发重做操作');
            }
          }
        } catch (error) {
          console.error('App: 重做操作失败', error);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [projectSelected]);


  console.log("app...")


  // 如果已选择项目，显示编辑器界面
  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      {!projectSelected && <ProjectSelector />}
      {projectSelected && <Box sx={{ height: "100vh", display: "flex", flexDirection: "column" }}>

        <MenuBar />

        <Box sx={{ flex: 1, display: "flex", overflow: "hidden" }}>
          {/* Left Panel */}
          <Resizable
            defaultSize={{ width: "20%", height: "100%" }}
            minWidth="15%"
            maxWidth="40%"
            enable={{ right: true }}
            style={{ display: "flex" }}
          >
            <Box sx={{ width: "100%", height: "100%", overflow: "hidden" }}>
              <LeftPanel />
            </Box>
          </Resizable>

          {/* Center Panel */}
          <Box sx={{ flex: 1, height: "100%", overflow: "hidden" }}>
            <CenterPanel />
          </Box>

          {/* Right Panel */}
          <RightPanel />
        </Box>

        {/* Bottom Panel */}
        <BottomPanel />
      </Box>}
    </DndContext>
  );

}

export default App;
