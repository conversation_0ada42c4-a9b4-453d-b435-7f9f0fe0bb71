# ObjectManager 插件使用说明

## 插件介绍

ObjectManager 插件是一个强大的对象管理工具，允许您通过唯一ID管理游戏中的各种对象。它提供了创建、查找、修改、添加子对象和删除对象的功能，使游戏开发更加灵活和高效。

## 安装方法

1. 将 `ObjectManager.js` 文件放入您的项目的 `js/plugins` 目录中
2. 在 RPG Maker MZ 编辑器中打开插件管理器
3. 启用 ObjectManager 插件

## 核心功能

### 1. 创建对象

```javascript
ObjectManager.create(id, properties);
```

- `id`: 对象的唯一标识符（字符串）
- `properties`: 对象的属性（对象）

示例：
```javascript
ObjectManager.create('player', {
    name: '勇者',
    level: 1,
    hp: 100,
    mp: 50,
    position: { x: 0, y: 0 }
});
```

### 2. 查找对象

```javascript
const obj = ObjectManager.find(id);
```

- `id`: 要查找的对象的唯一标识符
- 返回: 找到的对象，如果不存在则返回null

示例：
```javascript
const player = ObjectManager.find('player');
if (player) {
    console.log(player.name); // 输出: 勇者
}
```

### 3. 修改对象属性

```javascript
ObjectManager.modify(id, properties);
```

- `id`: 要修改的对象的唯一标识符
- `properties`: 要修改的属性（对象）
- 返回: 修改后的对象，如果对象不存在则返回null

示例：
```javascript
ObjectManager.modify('player', {
    level: 2,
    hp: 120,
    position: { y: 10 } // 只修改y坐标，x坐标保持不变
});
```

### 4. 添加子对象

```javascript
ObjectManager.addChild(parentId, childId, properties);
```

- `parentId`: 父对象的唯一标识符
- `childId`: 子对象的唯一标识符
- `properties`: 子对象的属性（对象）
- 返回: 创建的子对象，如果父对象不存在则返回null

示例：
```javascript
ObjectManager.addChild('player', 'sword', {
    name: '铁剑',
    attack: 10
});
```

### 5. 获取子对象

```javascript
const children = ObjectManager.getChildren(id);
```

- `id`: 父对象的唯一标识符
- 返回: 子对象数组

示例：
```javascript
const equipment = ObjectManager.getChildren('player');
for (const item of equipment) {
    console.log(item.name);
}
```

### 6. 删除对象

```javascript
ObjectManager.remove(id);
```

- `id`: 要删除的对象的唯一标识符
- 返回: 是否成功删除（布尔值）

示例：
```javascript
const result = ObjectManager.remove('sword');
if (result) {
    console.log('删除成功');
}
```

### 7. 获取所有对象

```javascript
const allObjects = ObjectManager.getAllObjects();
```

- 返回: 包含所有对象的对象映射

示例：
```javascript
const allObjects = ObjectManager.getAllObjects();
for (const id in allObjects) {
    console.log(`${id}: ${allObjects[id].name}`);
}
```

### 8. 清除所有对象

```javascript
ObjectManager.clear();
```

示例：
```javascript
ObjectManager.clear(); // 删除所有对象
```

### 9. 保存和加载

```javascript
// 保存到游戏变量
ObjectManager.saveToVariable(variableId);

// 从游戏变量加载
ObjectManager.loadFromVariable(variableId);
```

- `variableId`: 游戏变量的ID

示例：
```javascript
// 保存对象数据到变量1
ObjectManager.saveToVariable(1);

// 从变量1加载对象数据
ObjectManager.loadFromVariable(1);
```

## 高级用法

### 嵌套属性修改

ObjectManager 支持深度修改嵌套属性，而不会覆盖其他属性：

```javascript
// 创建带有嵌套属性的对象
ObjectManager.create('player', {
    name: '勇者',
    stats: {
        strength: 10,
        agility: 8,
        intelligence: 6
    }
});

// 只修改部分嵌套属性
ObjectManager.modify('player', {
    stats: {
        strength: 12,
        // agility和intelligence保持不变
    }
});
```

### 对象关系管理

您可以使用子对象功能创建复杂的对象关系：

```javascript
// 创建一个队伍
ObjectManager.create('team', {
    name: '冒险者小队',
    level: 1
});

// 添加队员
ObjectManager.addChild('team', 'player1', {
    name: '战士',
    role: 'tank'
});

ObjectManager.addChild('team', 'player2', {
    name: '法师',
    role: 'dps'
});

ObjectManager.addChild('team', 'player3', {
    name: '牧师',
    role: 'healer'
});

// 获取所有队员
const members = ObjectManager.getChildren('team');
```

### 游戏存档集成

将对象数据保存到游戏变量，以便与游戏存档系统集成：

```javascript
// 在游戏保存前调用
Scene_Save.prototype.onSavefileOk = function() {
    // 保存对象数据到变量1
    ObjectManager.saveToVariable(1);
    
    // 原有的保存逻辑
    Scene_File.prototype.onSavefileOk.call(this);
};

// 在游戏加载后调用
const _Scene_Load_onLoadSuccess = Scene_Load.prototype.onLoadSuccess;
Scene_Load.prototype.onLoadSuccess = function() {
    _Scene_Load_onLoadSuccess.call(this);
    
    // 从变量1加载对象数据
    ObjectManager.loadFromVariable(1);
};
```

## 实际应用示例

### 1. 物品系统

```javascript
// 创建物品类别
ObjectManager.create('items', {
    name: '物品类别',
    type: 'category'
});

// 添加物品
ObjectManager.addChild('items', 'potion', {
    name: '恢复药',
    type: 'consumable',
    effect: {
        hp: 50
    }
});

// 使用物品
function useItem(itemId, targetId) {
    const item = ObjectManager.find(itemId);
    const target = ObjectManager.find(targetId);
    
    if (item && target && item.effect) {
        if (item.effect.hp) {
            target.hp = Math.min(target.maxHp, target.hp + item.effect.hp);
        }
    }
}
```

### 2. 任务系统

```javascript
// 创建任务
ObjectManager.create('quest1', {
    name: '拯救村民',
    description: '击败10只史莱姆',
    status: 'active',
    progress: 0,
    target: 10,
    reward: {
        gold: 100,
        exp: 50
    }
});

// 更新任务进度
function updateQuest(questId, progress) {
    const quest = ObjectManager.find(questId);
    if (quest) {
        quest.progress += progress;
        if (quest.progress >= quest.target) {
            quest.status = 'completed';
        }
    }
}
```

### 3. 技能树系统

```javascript
// 创建技能树
ObjectManager.create('skillTree', {
    name: '战士技能树'
});

// 添加技能
ObjectManager.addChild('skillTree', 'skill1', {
    name: '基础打击',
    level: 1,
    damage: 10,
    unlocked: true
});

ObjectManager.addChild('skillTree', 'skill2', {
    name: '强力打击',
    level: 1,
    damage: 20,
    requires: ['skill1'],
    unlocked: false
});

// 解锁技能
function unlockSkill(skillId) {
    const skill = ObjectManager.find(skillId);
    if (skill && !skill.unlocked) {
        // 检查前置技能
        if (skill.requires) {
            for (const requiredSkillId of skill.requires) {
                const requiredSkill = ObjectManager.find(requiredSkillId);
                if (!requiredSkill || !requiredSkill.unlocked) {
                    return false; // 前置技能未解锁
                }
            }
        }
        
        skill.unlocked = true;
        return true;
    }
    return false;
}
```

## 注意事项

1. 对象ID必须是唯一的字符串，重复使用相同的ID会覆盖现有对象
2. 删除父对象会自动删除所有子对象
3. 修改对象属性时，只会修改指定的属性，其他属性保持不变
4. 对象数据不会自动保存，需要使用 `saveToVariable` 和 `loadFromVariable` 方法与游戏存档系统集成
5. 对象属性可以是任何JavaScript值，包括函数，但保存到变量时函数会丢失

## 测试插件

项目中还包含一个测试插件 `ObjectManagerTest.js`，它提供了一个简单的界面来测试 ObjectManager 的功能。在游戏中按下 O 键打开测试界面，您可以通过这个界面创建、查找、修改和删除对象。

## 兼容性

- 本插件兼容 RPG Maker MZ
- 本插件应该与大多数其他插件兼容

## 许可证

本插件采用 MIT 许可证。您可以自由使用、修改和分发本插件，包括用于商业项目。
