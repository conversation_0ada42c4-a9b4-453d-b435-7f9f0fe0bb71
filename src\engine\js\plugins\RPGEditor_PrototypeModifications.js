/*:
 * @target MZ
 * @plugindesc RPG Editor - 自动生成的修改插件
 * <AUTHOR> Editor
 * @version 1.0.0
 * @url 
 * @help 
 * 
 * 由RPG Editor自动生成的对象和原型修改插件
 * 
 * 此插件由RPG Editor自动生成，包含以下修改：
 * - 对象属性修改
 * - 对象创建和删除
 * - 类型原型修改
 * 
 * 注意：请不要手动编辑此文件，所有修改都会在下次生成时丢失。
 * 
 * @param debug
 * @text 调试模式
 * @desc 是否启用调试输出
 * @type boolean
 * @default false
 * 
 */
(function() {
    "use strict";

    // 获取插件参数
    const parameters = PluginManager.parameters("RPGEditor_PrototypeModifications");
    const DEBUG = parameters["debug"] === "true";

    // 调试日志函数
    function log(message, ...args) {
        if (DEBUG) {
            console.log("[RPGEditor]", message, ...args);
        }
    }

    // 工具函数：根据场景路径查找对象
    function findObjectByScenePath(scenePath) {
        if (!scenePath || scenePath.length === 0) return null;

        let current = SceneManager._scene;
        if (!current) return null;

        // 验证场景名称
        const expectedSceneName = scenePath[0];
        const actualSceneName = current.constructor.name;
        if (actualSceneName !== expectedSceneName) {
            if (DEBUG) log(`场景不匹配: 期望 ${expectedSceneName}, 实际 ${actualSceneName}`);
            return null;
        }

        // 遍历路径
        for (let i = 1; i < scenePath.length; i++) {
            const index = parseInt(scenePath[i]);
            if (isNaN(index) || !current.children || !current.children[index]) {
                if (DEBUG) log(`路径中断: 索引 ${index} 在 ${current.constructor.name} 中不存在`);
                return null;
            }
            current = current.children[index];
        }

        return current;
    }

    // ==================== 场景修改 ====================

    // 修改 Scene_Title 场景
    const originalScene_TitleStart = Scene_Title.prototype.start;
    Scene_Title.prototype.start = function() {
        // 调用原始方法
        originalScene_TitleStart.apply(this, arguments);

        if (DEBUG) log('Scene_Title.start 被调用');

        // 设置 Sprite 的 x 属性为 -114
        // 查找 Sprite 对象
        const target_sprite_Scene_Title_2Path = ["Scene_Title", "2"];
        const target_sprite_Scene_Title_2 = findObjectByScenePath(target_sprite_Scene_Title_2Path);
        if (target_sprite_Scene_Title_2) {
            target_sprite_Scene_Title_2.x = -114;
            if (DEBUG) console.log('设置属性:', 'x', -114, target);
        }

        // 设置 WindowLayer 的 x 属性为 44
        // 查找 WindowLayer 对象
        const target_windowlayer_Scene_Title_3Path = ["Scene_Title", "3"];
        const target_windowlayer_Scene_Title_3 = findObjectByScenePath(target_windowlayer_Scene_Title_3Path);
        if (target_windowlayer_Scene_Title_3) {
            target_windowlayer_Scene_Title_3.x = 44;
            if (DEBUG) console.log('设置属性:', 'x', 44, target);
        }

    };
})();