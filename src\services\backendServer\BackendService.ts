import { getCodeGenerationService } from '../codeGeneration/CodeGenerationService';

/**
 * 记录属性修改到后端
 * 使用新的前端代码生成服务
 */
export const recordPropertyModification = async (
  object: any,
  propertyName: string,
  value: any
): Promise<string> => {
  try {
    const className = object.constructor.name;
    console.log(`🎯 [前端] 开始记录属性修改: ${className}.${propertyName} = `, value);

    // 处理特殊操作类型
    if (propertyName.startsWith('__CREATE_CHILD__')) {
      return await handleObjectCreation(object, propertyName, value);
    } else if (propertyName.startsWith('__DELETE_CHILD__')) {
      return await handleObjectDeletion(object, propertyName, value);
    }

    // 使用新的代码生成服务记录属性修改
    const codeService = getCodeGenerationService({
      debugMode: true,
      autoSave: false, // 关闭自动保存
      saveToTempOnly: true // 只保存到临时目录
    });

    const result = await codeService.recordPropertyModification(
      object,
      propertyName,
      object[propertyName], // 旧值
      value // 新值
    );

    console.log('✅ [前端] 记录修改结果:', result);
    return result;
  } catch (error) {
    console.error('记录属性修改失败:', error);
    throw error;
  }
};

/**
 * 处理对象创建操作
 */
async function handleObjectCreation(parentObject: any, propertyName: string, value: any): Promise<string> {
  try {
    console.log('🏗️ [前端] 处理对象创建操作:', propertyName, value);

    const codeService = getCodeGenerationService({
      debugMode: true,
      autoSave: false,
      saveToTempOnly: true
    });

    // 解析创建参数
    const objectType = value.objectType || 'Sprite';
    const initialProperties = value.properties || {};
    const objectName = value.name;

    const result = await codeService.recordObjectCreation(
      parentObject,
      objectType,
      initialProperties,
      objectName
    );

    console.log('✅ [前端] 对象创建记录完成:', result);
    return result;
  } catch (error) {
    console.error('处理对象创建失败:', error);
    throw error;
  }
}

/**
 * 处理对象删除操作
 */
async function handleObjectDeletion(object: any, propertyName: string, value: any): Promise<string> {
  try {
    console.log('🗑️ [前端] 处理对象删除操作:', propertyName, value);

    const codeService = getCodeGenerationService({
      debugMode: true,
      autoSave: false,
      saveToTempOnly: true
    });

    const result = await codeService.recordObjectDeletion(object);

    console.log('✅ [前端] 对象删除记录完成:', result);
    return result;
  } catch (error) {
    console.error('处理对象删除失败:', error);
    throw error;
  }
}

/**
 * 保存所有修改到临时目录（Ctrl+S 触发）
 */
export const saveAllModifications = async (): Promise<string> => {
  try {
    console.log('💾 [前端] 保存到临时目录');

    const codeService = getCodeGenerationService({
      debugMode: true,
      autoSave: false,
      saveToTempOnly: true
    });
    const result = await codeService.saveToTemp();

    console.log('✅ [前端] 临时保存完成:', result);
    return result;
  } catch (error) {
    console.error('临时保存失败:', error);
    throw error;
  }
};

/**
 * 刷新预览 - 从临时目录复制到引擎和项目目录
 */
export const refreshPreview = async (): Promise<string> => {
  try {
    console.log('🔄 [前端] 刷新预览');

    const codeService = getCodeGenerationService({
      debugMode: true,
      autoSave: false,
      saveToTempOnly: true
    });
    const result = await codeService.refreshPreview();

    console.log('✅ [前端] 预览刷新完成:', result);
    return result;
  } catch (error) {
    console.error('刷新预览失败:', error);
    throw error;
  }
};

/**
 * 清除所有修改记录
 */
export const clearModifications = async (): Promise<string> => {
  try {
    console.log('🗑️ [前端] 清除所有修改记录');

    const codeService = getCodeGenerationService({
      debugMode: true,
      autoSave: false,
      saveToTempOnly: true
    });
    codeService.clearQueue();

    console.log('✅ [前端] 修改记录已清除');
    return '修改记录已清除';
  } catch (error) {
    console.error('清除修改记录失败:', error);
    throw error;
  }
};

/**
 * 获取代码预览
 */
export const getCodePreview = (): string => {
  try {
    const codeService = getCodeGenerationService({
      debugMode: true,
      autoSave: false,
      saveToTempOnly: true
    });
    return codeService.generatePreview();
  } catch (error) {
    console.error('获取代码预览失败:', error);
    return '预览生成失败';
  }
};

/**
 * 获取操作队列状态
 */
export const getQueueStatus = () => {
  try {
    const codeService = getCodeGenerationService({
      debugMode: true,
      autoSave: false,
      saveToTempOnly: true
    });
    return codeService.getQueueStatus();
  } catch (error) {
    console.error('获取队列状态失败:', error);
    return { operationCount: 0, operations: [] };
  }
};

/**
 * 保存操作记录到项目目录
 */
export const saveOperationsRecord = async (): Promise<string> => {
  try {
    console.log('💾 [前端] 保存操作记录到项目目录');

    const codeService = getCodeGenerationService({
      debugMode: true,
      autoSave: false,
      saveToTempOnly: true
    });
    const result = await codeService.saveOperationsRecord();

    console.log('✅ [前端] 操作记录保存完成:', result);
    return result;
  } catch (error) {
    console.error('保存操作记录失败:', error);
    throw error;
  }
};

/**
 * 从项目目录加载操作记录
 */
export const loadOperationsRecord = async (): Promise<boolean> => {
  try {
    console.log('📂 [前端] 从项目目录加载操作记录');

    const codeService = getCodeGenerationService({
      debugMode: true,
      autoSave: false,
      saveToTempOnly: true
    });
    const loaded = await codeService.loadOperationsRecord();

    if (loaded) {
      console.log('✅ [前端] 操作记录加载完成');
    } else {
      console.log('ℹ️ [前端] 没有找到操作记录文件');
    }

    return loaded;
  } catch (error) {
    console.error('加载操作记录失败:', error);
    return false;
  }
};



