import { invoke } from '@tauri-apps/api/core';
import useProjectStore from '../../store/Store';
import { getObjectPath } from '../../utils/object/objectPro';
/**
 * 记录属性修改到后端（支持对象修改和类型修改两种模式）
 * @param object 要修改的对象
 * @param propertyName 属性名称
 * @param value 新值
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export const recordPropertyModification = async (
  object: any,
  propertyName: string,
  value: any
): Promise<string> => {
  try {
    // 获取对象的类名
    const className = object.constructor.name;

    // 获取当前选中对象状态
    const selectedObjects = useProjectStore.getState().selectedObjects;

    console.log(`记录属性修改: ${className}.${propertyName} = `, value);

    // 统一确定操作模式和路径
    const operationContext = determineOperationContext(object, selectedObjects);

    console.log('操作上下文:', operationContext);

    // 构建统一的操作信息
    const operationInfo = {
      operationMode: operationContext.operationMode,
      targetPath: operationContext.targetPath,
      className: className,
      propertyName: propertyName,
      value: value,
      // 添加完整的上下文信息
      _operationContext: {
        isTypeOperation: operationContext.operationMode === 'TYPE',
        parentClassName: operationContext.parentClassName,
        fullPath: operationContext.targetPath
      }
    };

    // 统一发送到后端
    const result = await invoke('record_unified_modification', operationInfo);

    console.log('记录修改结果:', result);
    return result as string;
  } catch (error) {
    console.error('记录属性修改失败:', error);
    throw error;
  }
};

/**
 * 统一确定操作上下文（类型操作 vs 对象操作）
 */
function determineOperationContext(object: any, selectedObjects: any) {
  // 检查对象是否有类型创建标记
  if (object._rpgEditorTypeCreated && object._rpgEditorPath) {
    console.log('检测到类型创建的对象，使用类型操作模式');
    return {
      operationMode: 'TYPE' as const,
      targetPath: object._rpgEditorPath,
      parentClassName: object._rpgEditorParentType || object._rpgEditorPath[0]
    };
  }

  // 检查 selectedObjects 中是否有类型创建的对象
  if (selectedObjects.objects && selectedObjects.objects.length > 0) {
    const firstObject = selectedObjects.objects[0];
    if (firstObject._rpgEditorTypeCreated && firstObject._rpgEditorPath) {
      console.log('选中对象中检测到类型创建的对象，使用类型操作模式');
      return {
        operationMode: 'TYPE' as const,
        targetPath: firstObject._rpgEditorPath,
        parentClassName: firstObject._rpgEditorParentType || firstObject._rpgEditorPath[0]
      };
    }
  }

  // 默认为对象操作模式
  console.log('使用对象操作模式');
  return {
    operationMode: 'OBJECT' as const,
    targetPath: getObjectPath(object),
    parentClassName: null
  };
}

/**
 * 保存所有修改到插件文件
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export const saveAllModifications = async (): Promise<string> => {
  try {
    const result = await invoke('save_all_modifications');
    console.log('保存所有修改结果:', result);
    return result as string;
  } catch (error) {
    console.error('保存所有修改失败:', error);
    throw error;
  }
};


export const refreshPreview = async (): Promise<string> => {
  try {
    // 调用后端 API 将临时插件复制到项目目录
    const result = await invoke<string>('refresh_preview');
    console.log('刷新结果:', result);
    return result;
  } catch (error) {
    console.error('刷新结果失败:', error);
    throw error;
  }
};


/**
 * 清除所有修改记录
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export const clearModifications = async (): Promise<string> => {
  try {
    const result = await invoke('clear_modifications');
    console.log('清除所有修改记录结果:', result);
    return result as string;
  } catch (error) {
    console.error('清除所有修改记录失败:', error);
    throw error;
  }
};



