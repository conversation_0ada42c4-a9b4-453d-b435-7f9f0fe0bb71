import { invoke } from '@tauri-apps/api/core';
import useProjectStore from '../../store/Store';
import { getObjectPath } from '../../utils/object/objectPro';
/**
 * 记录属性修改到后端（支持对象修改和类型修改两种模式）
 * @param object 要修改的对象
 * @param propertyName 属性名称
 * @param value 新值
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export const recordPropertyModification = async (
  object: any,
  propertyName: string,
  value: any
): Promise<string> => {
  try {
    // 获取对象的类名
    const className = object.constructor.name;

    // 获取当前选中对象状态，判断修改模式
    const selectedObjects = useProjectStore.getState().selectedObjects;

    // 对象删除操作始终使用对象修改模式，但对象创建操作根据当前模式决定
    if (propertyName.startsWith('__DELETE_CHILD__')) {
      console.log(`对象删除操作: ${propertyName}，强制使用对象修改模式`);

      // 获取当前场景
      const scene = useProjectStore.getState().scene;

      // 如果没有场景，则不记录修改
      if (!scene) {
        console.warn('无法获取当前场景，不记录对象删除操作', scene);
        return '无法获取当前场景，不记录对象删除操作';
      }

      // 强制使用对象修改模式记录对象删除操作
      const result = await invoke('record_property_modification', {
        objectPath: getObjectPath(object),
        className: className,
        fieldName: propertyName,
        value: value
      });

      console.log('记录对象删除操作结果:', result);
      return result as string;
    }
    // 判断是类型修改模式还是对象修改模式
    else if (selectedObjects.className) {
      // 类型修改模式：修改类型原型
      console.log(`类型修改模式: 修改 ${selectedObjects.className} 类型的 ${propertyName} 属性`);

      const result = await invoke('record_class_prototype_modification', {
        className: selectedObjects.className,
        fieldName: propertyName,
        value: value
      });

      console.log('记录类型原型修改结果:', result);
      return result as string;
    } else {
      // 对象修改模式：修改具体对象实例
      console.log(`对象修改模式: 修改具体对象 ${className} 的 ${propertyName} 属性`);

      // 获取当前场景
      const scene = useProjectStore.getState().scene;

      // 如果没有场景，则不记录修改
      if (!scene) {
        console.warn('无法获取当前场景，不记录属性修改', scene);
        return '无法获取当前场景，不记录属性修改';
      }

      // 调用后端 API 记录对象属性修改
      const result = await invoke('record_property_modification', {
        objectPath: getObjectPath(object),
        className: className,
        fieldName: propertyName,
        value: value
      });

      console.log('记录对象属性修改结果:', result);
      return result as string;
    }
  } catch (error) {
    console.error('记录属性修改失败:', error);
    throw error;
  }
};

/**
 * 保存所有修改到插件文件
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export const saveAllModifications = async (): Promise<string> => {
  try {
    const result = await invoke('save_all_modifications');
    console.log('保存所有修改结果:', result);
    return result as string;
  } catch (error) {
    console.error('保存所有修改失败:', error);
    throw error;
  }
};


export const refreshPreview = async (): Promise<string> => {
  try {
    // 调用后端 API 将临时插件复制到项目目录
    const result = await invoke<string>('refresh_preview');
    console.log('刷新结果:', result);
    return result;
  } catch (error) {
    console.error('刷新结果失败:', error);
    throw error;
  }
};


/**
 * 清除所有修改记录
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export const clearModifications = async (): Promise<string> => {
  try {
    const result = await invoke('clear_modifications');
    console.log('清除所有修改记录结果:', result);
    return result as string;
  } catch (error) {
    console.error('清除所有修改记录失败:', error);
    throw error;
  }
};



