import React from 'react';
import { useDraggable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import { Box } from '@mui/material';

interface DraggableBaseProps {
  id: string;
  data?: any;
  children: React.ReactNode;
  style?: React.CSSProperties;
  className?: string;
  disabled?: boolean;
}

/**
 * 基础可拖动组件
 * 提供拖动功能的基础实现
 * 在拖动过程中，原始组件保持不变，创建副本跟随鼠标移动
 */
const DraggableBase: React.FC<DraggableBaseProps> = ({
  id,
  data,
  children,
  style,
  className,
  disabled = false,
}) => {
  // 使用 dnd-kit 的 useDraggable hook
  const {
    attributes,
    listeners,
    setNodeRef,
    isDragging
  } = useDraggable({
    id,
    data: {
      ...data,
      // 添加一个标记，表示这是复制拖拽
      copyDrag: true,
    },
    disabled
  });

  // 合并样式 - 不再应用 transform，因为我们使用 DragOverlay 来显示拖拽中的元素
  const combinedStyle: React.CSSProperties = {
    ...style,
    // 不再改变原始元素的透明度，保持原样
    cursor: disabled ? 'default' : 'grab',
    touchAction: 'none', // 防止在移动设备上滚动
  };

  // 合并类名，添加asset-item类用于全局点击事件识别
  const combinedClassName = `asset-item ${className || ''}`.trim();

  return (
    <Box
      ref={setNodeRef}
      style={combinedStyle}
      className={combinedClassName}
      {...listeners}
      {...attributes}
    >
      {children}
    </Box>
  );
};

export default DraggableBase;
