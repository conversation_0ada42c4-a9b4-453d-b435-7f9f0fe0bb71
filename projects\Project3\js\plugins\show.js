/*:
 * @target MZ
 * @plugindesc 显示对象的包围盒和锚点
 * <AUTHOR> Name
 */

(() => {
  Sprite_Clickable.prototype.initialize = function () {
    Sprite.prototype.initialize.call(this);
    this._pressed = false;
    this._hovered = false;
    this._boundingBoxContainer = null;
    this._anchorPoint = null;
    // ... existing code ...
  };

  Sprite_Clickable.prototype.showBoundingBox = function () {
    if (!this._boundingBoxContainer) {
      this._boundingBoxContainer = new PIXI.Graphics();
      this.addChild(this._boundingBoxContainer);

      // 创建锚点精灵
      this._anchorPoint = new Sprite();
      this._anchorPoint.bitmap = new Bitmap(6, 6);
      this._anchorPoint.bitmap.fillAll('red');
      this._anchorPoint.anchor.x = 0.5;
      this._anchorPoint.anchor.y = 0.5;
      this.addChild(this._anchorPoint);
    }

    // 更新包围盒
    this._boundingBoxContainer.clear();
    this._boundingBoxContainer.lineStyle(1, 0x00ff00);

    const width = this.width;
    const height = this.height;
    const anchorX = this.anchor.x;
    const anchorY = this.anchor.y;

    this._boundingBoxContainer.drawRect(
      -width * anchorX,
      -height * anchorY,
      width,
      height
    );

    // 更新锚点位置
    this._anchorPoint.x = 0;
    this._anchorPoint.y = 0;

    this._boundingBoxContainer.visible = true;
    this._anchorPoint.visible = true;
  };

  Sprite_Clickable.prototype.hideBoundingBox = function () {
    if (this._boundingBoxContainer) {
      this._boundingBoxContainer.visible = false;
    }
    if (this._anchorPoint) {
      this._anchorPoint.visible = false;
    }
  };

  Sprite_Clickable.prototype.processTouch = function () {
    if (this.isClickEnabled()) {
      if (this.isBeingTouched()) {
        if (!this._hovered && TouchInput.isHovered()) {
          this._hovered = true;
          this.onMouseEnter();
          this.showBoundingBox();
        }
        if (TouchInput.isTriggered()) {
          this._pressed = true;
          this.onPress();
        }
      } else {
        if (this._hovered) {
          this.onMouseExit();
          this.hideBoundingBox();
        }
        this._pressed = false;
        this._hovered = false;
      }
      if (this._pressed && TouchInput.isReleased()) {
        this._pressed = false;
        this.onClick();
      }
    } else {
      this._pressed = false;
      this._hovered = false;
      this.hideBoundingBox();
    }
  };
})();