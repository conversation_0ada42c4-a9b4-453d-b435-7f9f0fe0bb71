import React, { useState } from 'react';
import { Box, Tabs, Tab } from '@mui/material';
import { Resizable } from 're-resizable';
import PropertyPanel from './PropertyPanel';
import EventPanel from './EventPanel/EventPanel';
import LayoutPanel from './LayoutPanel/LayoutPanel';
import AnimationPanel from './AnimationPanel/AnimationPanel';

interface RightPanelProps {
  // 可以添加其他属性
}

const RightPanel: React.FC<RightPanelProps> = () => {
  const [tabIndex, setTabIndex] = useState(0);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabIndex(newValue);
  };

  return (
    <Resizable
      defaultSize={{ width: '20%', height: '100%' }}
      minWidth="15%"
      maxWidth="40%"
      enable={{ left: true }}
      style={{ display: 'flex' }}
      className="right-panel-container" // 添加类名，便于全局事件处理器识别
    >
      <Box
        sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}
        className="right-panel-content" // 添加类名，便于全局事件处理器识别
      >
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabIndex} onChange={handleTabChange}>
            <Tab label="属性" />
            <Tab label="事件" />
            <Tab label="布局" />
            <Tab label="动画" />
          </Tabs>
        </Box>

        <Box sx={{ flex: 1, overflow: 'auto', display: tabIndex === 0 ? 'block' : 'none' }}>
          <PropertyPanel />
        </Box>

        <Box sx={{ flex: 1, overflow: 'auto', display: tabIndex === 1 ? 'block' : 'none' }}>
          <EventPanel />
        </Box>

        <Box sx={{ flex: 1, overflow: 'auto', display: tabIndex === 2 ? 'block' : 'none' }}>
          <LayoutPanel />
        </Box>

        <Box sx={{ flex: 1, overflow: 'auto', display: tabIndex === 3 ? 'block' : 'none' }}>
          <AnimationPanel />
        </Box>
      </Box>
    </Resizable>
  );
};

export default RightPanel;
