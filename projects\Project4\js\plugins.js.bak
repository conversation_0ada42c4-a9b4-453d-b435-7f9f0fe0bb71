// Generated by RPG Maker.
// Do not edit this file directly.
var $plugins =
[
{"description":"通过ID管理游戏对象的插件","name":"ObjectManager","parameters":{},"status":true},
{"description":"ObjectManager插件的测试插件","name":"ObjectManagerTest","parameters":{},"status":true},
{"name":"RPGEditor_PrototypeModifications","status":true,"description":"RPG Editor - 自动生成的原型链修改插件","parameters":{}},
{"name":"RPGEditor_BitmapTracker","status":true,"description":"跟踪并记录Bitmap上的文本和图像绘制操作","parameters":{}},
{"name":"showClasses","status":true,"description":"显示鼠标悬停对象的继承关系","parameters":{"textSize":"16","backgroundColor":"rgba(0, 0, 0, 0.7)","textColor":"rgba(255, 255, 255, 1.0)","displayDuration":"60"}}
];
