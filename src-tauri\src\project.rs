use chrono::prelude::*;
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::{Path, PathBuf};

use tauri::AppHandle;
use tauri::Manager;

// 项目信息结构体
#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct ProjectInfo {
    pub name: String,
    pub path: String,
    pub last_opened: Option<String>,
}

// 获取配置文件路径
fn get_config_path(app_handle: &AppHandle) -> Result<PathBuf, String> {
    // 在 Tauri 2.0 中，我们使用 app_handle.app_handle 来获取应用数据目录
    let app_dir = app_handle
        .app_handle()
        .path()
        .app_data_dir()
        .map_err(|e| format!("无法获取应用数据目录: {}", e))?;

    if !app_dir.exists() {
        fs::create_dir_all(&app_dir).map_err(|e| format!("创建应用数据目录失败: {}", e))?;
    }

    Ok(app_dir.join("projects.json"))
}

// 获取公共目录路径
fn get_public_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {
    // 在 Tauri 2.0 中，我们使用 app_handle.app_handle 来获取应用目录
    let resource_dir = app_handle
        .app_handle()
        .path()
        .resource_dir()
        .map_err(|e| format!("无法获取应用目录: {}", e))?;

    // 打印资源目录路径，用于调试
    println!("资源目录路径: {}", resource_dir.display());

    // 获取当前工作目录
    let current_dir =
        std::env::current_dir().map_err(|e| format!("无法获取当前工作目录: {}", e))?;
    println!("当前工作目录: {}", current_dir.display());

    // 尝试找到项目根目录
    // 如果当前目录包含 "src-tauri"，则向上查找到项目根目录
    let mut root_dir = current_dir.clone();
    while root_dir
        .file_name()
        .map_or(false, |name| name != "src-tauri")
    {
        if !root_dir.pop() {
            break;
        }
    }

    // 如果找到了 src-tauri 目录，再向上一级获取项目根目录
    if root_dir
        .file_name()
        .map_or(false, |name| name == "src-tauri")
    {
        root_dir.pop();
    } else {
        // 如果没有找到 src-tauri 目录，使用当前目录的父目录
        root_dir = current_dir
            .parent()
            .ok_or_else(|| "无法获取当前目录的父目录".to_string())?
            .to_path_buf();
    }

    println!("项目根目录: {}", root_dir.display());

    // 使用 projects 目录而不是 public 目录
    let projects_dir = root_dir.join("projects");

    if !projects_dir.exists() {
        fs::create_dir_all(&projects_dir).map_err(|e| format!("创建 projects 目录失败: {}", e))?;
    }

    println!("项目目录路径: {}", projects_dir.display());
    Ok(projects_dir)
}

// 递归复制目录
fn copy_dir_all(src: &Path, dst: &Path) -> std::io::Result<()> {
    if !dst.exists() {
        fs::create_dir_all(dst)?;
    }

    for entry in fs::read_dir(src)? {
        let entry = entry?;
        let ty = entry.file_type()?;
        let target = dst.join(entry.file_name());

        if ty.is_dir() {
            copy_dir_all(&entry.path(), &target)?;
        } else {
            fs::copy(entry.path(), target)?;
        }
    }

    Ok(())
}

// 获取项目列表
#[tauri::command]
pub fn get_project_list(app_handle: AppHandle) -> Result<Vec<ProjectInfo>, String> {
    let config_path = get_config_path(&app_handle)?;
    let projects_dir = get_public_dir(&app_handle)?;

    if !config_path.exists() {
        return Ok(Vec::new());
    }

    let content =
        fs::read_to_string(&config_path).map_err(|e| format!("读取配置文件失败: {}", e))?;

    if content.trim().is_empty() {
        return Ok(Vec::new());
    }

    let all_projects: Vec<ProjectInfo> =
        serde_json::from_str(&content).map_err(|e| format!("解析配置文件失败: {}", e))?;

    // 过滤出实际存在的项目
    let mut existing_projects = Vec::new();
    let mut missing_projects = Vec::new();

    for project in all_projects {
        let project_path = projects_dir.join(&project.name);
        if project_path.exists() {
            existing_projects.push(project);
        } else {
            missing_projects.push(project.name.clone());
        }
    }

    // 如果有项目不存在，更新配置文件
    if !missing_projects.is_empty() {
        println!("以下项目不存在，将从列表中移除: {:?}", missing_projects);

        // 保存更新后的项目列表
        let content = serde_json::to_string_pretty(&existing_projects)
            .map_err(|e| format!("序列化配置失败: {}", e))?;

        fs::write(&config_path, content).map_err(|e| format!("写入配置文件失败: {}", e))?;
    }

    Ok(existing_projects)
}

// 导入项目
#[tauri::command]
pub fn import_project(
    app_handle: AppHandle,
    source_path: String,
    project_name: Option<String>,
) -> Result<String, String> {
    println!("导入项目: {}", source_path);

    // 获取源目录
    let source_path_obj = Path::new(&source_path);
    if !source_path_obj.exists() {
        return Err(format!("源目录不存在: {}", source_path));
    }

    // 检查是否是有效的 RPG Maker 项目
    let index_html_path = source_path_obj.join("index.html");
    if !index_html_path.exists() {
        return Err("所选目录不是有效的 RPG Maker 项目 (缺少 index.html)".to_string());
    }

    // 如果没有提供项目名称，使用源目录的名称
    let project_name = match project_name {
        Some(name) => name,
        None => source_path_obj
            .file_name()
            .ok_or_else(|| "无法获取源目录名称".to_string())?
            .to_string_lossy()
            .to_string(),
    };

    // 获取公共目录
    let public_dir = get_public_dir(&app_handle)?;

    // 创建目标目录
    let target_dir = public_dir.clone().join(&project_name);

    // 如果目标目录已存在，返回错误
    if target_dir.exists() {
        return Err(format!("项目 '{}' 已存在", project_name));
    }

    // 复制项目文件
    copy_dir_all(source_path_obj, &target_dir).map_err(|e| format!("复制项目失败: {}", e))?;

    // 更新项目列表
    update_project_list(
        &app_handle,
        &project_name,
        &target_dir.to_string_lossy().to_string(),
    )?;

    println!(
        "项目 '{}' 已成功导入 公共目录 {:?}",
        project_name, public_dir
    );

    Ok(project_name)
}

// 更新项目列表
fn update_project_list(
    app_handle: &AppHandle,
    project_name: &str,
    project_path: &str,
) -> Result<(), String> {
    let config_path = get_config_path(app_handle)?;

    let mut projects = if config_path.exists() {
        let content =
            fs::read_to_string(&config_path).map_err(|e| format!("读取配置文件失败: {}", e))?;

        if content.trim().is_empty() {
            Vec::new()
        } else {
            serde_json::from_str::<Vec<ProjectInfo>>(&content)
                .map_err(|e| format!("解析配置文件失败: {}", e))?
        }
    } else {
        Vec::new()
    };

    // 检查项目是否已存在
    if let Some(project) = projects.iter_mut().find(|p| p.name == project_name) {
        // 更新最后打开时间
        project.last_opened = Some(Utc::now().to_rfc3339());
        project.path = project_path.to_string();
    } else {
        // 添加新项目
        projects.push(ProjectInfo {
            name: project_name.to_string(),
            path: project_path.to_string(),
            last_opened: Some(Utc::now().to_rfc3339()),
        });
    }

    // 保存配置
    let content =
        serde_json::to_string_pretty(&projects).map_err(|e| format!("序列化配置失败: {}", e))?;

    fs::write(&config_path, content).map_err(|e| format!("写入配置文件失败: {}", e))?;

    Ok(())
}

// 更新项目最后打开时间
#[tauri::command]
pub fn update_project_last_opened(
    app_handle: AppHandle,
    project_name: String,
) -> Result<(), String> {
    let config_path = get_config_path(&app_handle)?;

    if !config_path.exists() {
        return Err("配置文件不存在".to_string());
    }

    let content =
        fs::read_to_string(&config_path).map_err(|e| format!("读取配置文件失败: {}", e))?;

    let mut projects: Vec<ProjectInfo> =
        serde_json::from_str(&content).map_err(|e| format!("解析配置文件失败: {}", e))?;

    // 查找并更新项目
    let project = projects
        .iter_mut()
        .find(|p| p.name == project_name)
        .ok_or_else(|| format!("项目 '{}' 不存在", project_name))?;

    // 更新最后打开时间
    project.last_opened = Some(Utc::now().to_rfc3339());

    // 保存配置
    let content =
        serde_json::to_string_pretty(&projects).map_err(|e| format!("序列化配置失败: {}", e))?;

    fs::write(&config_path, content).map_err(|e| format!("写入配置文件失败: {}", e))?;

    Ok(())
}

// 获取项目路径
#[tauri::command]
pub fn get_project_path(app_handle: AppHandle, project_name: String) -> Result<String, String> {
    let public_dir = get_public_dir(&app_handle)?;
    let project_path = public_dir.join(project_name);

    if !project_path.exists() {
        return Err("项目不存在".to_string());
    }

    Ok(project_path.to_string_lossy().to_string())
}

// 检查项目是否存在
#[tauri::command]
pub fn check_project_exists(app_handle: AppHandle, project_name: String) -> Result<bool, String> {
    let public_dir = get_public_dir(&app_handle)?;
    let project_path = public_dir.join(project_name);

    Ok(project_path.exists())
}

// 更新自定义资源路径插件
#[tauri::command]
pub fn update_custom_resource_path(
    app_handle: AppHandle,
    project_name: String,
) -> Result<(), String> {
    println!("更新自定义资源路径插件，项目名称: {}", project_name);

    // 获取当前工作目录
    let current_dir =
        std::env::current_dir().map_err(|e| format!("无法获取当前工作目录: {}", e))?;

    // 尝试找到项目根目录
    let mut root_dir = current_dir.clone();
    while root_dir
        .file_name()
        .map_or(false, |name| name != "src-tauri")
    {
        if !root_dir.pop() {
            break;
        }
    }

    // 如果找到了 src-tauri 目录，再向上一级获取项目根目录
    if root_dir
        .file_name()
        .map_or(false, |name| name == "src-tauri")
    {
        root_dir.pop();
    } else {
        // 如果没有找到 src-tauri 目录，使用当前目录的父目录
        root_dir = current_dir
            .parent()
            .ok_or_else(|| "无法获取当前目录的父目录".to_string())?
            .to_path_buf();
    }

    // 构建插件文件路径
    let plugin_path = root_dir
        .join("src")
        .join("engine")
        .join("js")
        .join("plugins")
        .join("CustomResourcePath.js");

    println!("插件文件路径: {}", plugin_path.display());

    // 检查文件是否存在
    if !plugin_path.exists() {
        return Err(format!("插件文件不存在: {}", plugin_path.display()));
    }

    // 读取文件内容
    let content =
        fs::read_to_string(&plugin_path).map_err(|e| format!("读取插件文件失败: {}", e))?;

    // 使用正则表达式替换 basePath 变量
    let re = regex::Regex::new(
        r#"const basePath = parameters\["BasePath"\] \|\| "../projects/[^/]*/";"#,
    )
    .map_err(|e| format!("创建正则表达式失败: {}", e))?;

    let new_content = re.replace(
        &content,
        &format!(
            r#"const basePath = parameters["BasePath"] || "../projects/{}/";"#,
            project_name
        ),
    );

    // 写入文件
    fs::write(&plugin_path, new_content.as_bytes())
        .map_err(|e| format!("写入插件文件失败: {}", e))?;

    println!("已成功更新自定义资源路径插件，项目名称: {}", project_name);

    Ok(())
}

// 删除项目
#[tauri::command]
pub fn delete_project(app_handle: AppHandle, project_name: String) -> Result<(), String> {
    // 获取项目路径
    let public_dir = get_public_dir(&app_handle)?;
    let project_path = public_dir.join(&project_name);

    // 更新项目列表（无论项目目录是否存在）
    let config_path = get_config_path(&app_handle)?;

    if config_path.exists() {
        let content =
            fs::read_to_string(&config_path).map_err(|e| format!("读取配置文件失败: {}", e))?;

        let mut projects: Vec<ProjectInfo> =
            serde_json::from_str(&content).map_err(|e| format!("解析配置文件失败: {}", e))?;

        // 移除项目
        projects.retain(|p| p.name != project_name);

        // 保存配置
        let content = serde_json::to_string_pretty(&projects)
            .map_err(|e| format!("序列化配置失败: {}", e))?;

        fs::write(&config_path, content).map_err(|e| format!("写入配置文件失败: {}", e))?;
    }

    // 检查项目目录是否存在
    if project_path.exists() {
        // 删除项目目录
        fs::remove_dir_all(&project_path).map_err(|e| format!("删除项目目录失败: {}", e))?;
        println!("项目 '{}' 的目录已成功删除", project_name);
    } else {
        println!("项目 '{}' 的目录不存在，仅从配置中移除", project_name);
    }

    println!("项目 '{}' 已成功从列表中移除", project_name);

    Ok(())
}
