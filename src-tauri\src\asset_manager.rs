use serde::{Deserialize, Serialize};
use std::fs;
use std::path::{Path, PathBuf};
use tauri::AppHandle;

// 资源类型枚举
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub enum AssetType {
    Image,
    Audio,
    Data,
    Map,
    Font,
    Video,
    Other,
    Directory,
}

// 资源信息结构体
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AssetInfo {
    pub name: String,                // 资源名称
    pub asset_type: AssetType,       // 资源类型
    pub game_path: String,           // 游戏项目中的相对路径
    pub absolute_path: String,       // 电脑中的绝对路径
    pub display_path: String,        // Tauri 中显示的路径
    pub is_directory: bool,          // 是否是目录
    pub children: Option<Vec<AssetInfo>>, // 子资源（如果是目录）
    pub parent_path: Option<String>, // 父目录路径
    pub size: Option<u64>,           // 文件大小（字节）
    pub extension: Option<String>,   // 文件扩展名
}

// 获取资源类型
fn get_asset_type(path: &Path) -> AssetType {
    if path.is_dir() {
        return AssetType::Directory;
    }

    match path.extension().and_then(|ext| ext.to_str()) {
        Some(ext) => match ext.to_lowercase().as_str() {
            "png" | "jpg" | "jpeg" | "gif" | "webp" => AssetType::Image,
            "ogg" | "wav" | "mp3" | "m4a" => AssetType::Audio,
            "json" => AssetType::Data,
            "ttf" | "otf" | "woff" | "woff2" => AssetType::Font,
            "mp4" | "webm" | "ogv" => AssetType::Video,
            _ => AssetType::Other,
        },
        None => AssetType::Other,
    }
}

// 获取项目根目录
fn get_project_root_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {
    // 获取当前工作目录
    let current_dir =
        std::env::current_dir().map_err(|e| format!("无法获取当前工作目录: {}", e))?;
    
    // 尝试找到项目根目录
    let mut root_dir = current_dir.clone();
    while root_dir
        .file_name()
        .map_or(false, |name| name != "src-tauri")
    {
        if !root_dir.pop() {
            break;
        }
    }
    
    // 如果找到了 src-tauri 目录，再向上一级获取项目根目录
    if root_dir
        .file_name()
        .map_or(false, |name| name == "src-tauri")
    {
        root_dir.pop();
    } else {
        // 如果没有找到 src-tauri 目录，使用当前目录的父目录
        root_dir = current_dir
            .parent()
            .ok_or_else(|| "无法获取当前目录的父目录".to_string())?
            .to_path_buf();
    }
    
    Ok(root_dir)
}

// 获取项目目录
fn get_project_dir(app_handle: &AppHandle, project_name: &str) -> Result<PathBuf, String> {
    let root_dir = get_project_root_dir(app_handle)?;
    let projects_dir = root_dir.join("projects");
    let project_dir = projects_dir.join(project_name);
    
    if !project_dir.exists() {
        return Err(format!("项目 '{}' 不存在", project_name));
    }
    
    Ok(project_dir)
}

// 扫描目录并获取资源信息
fn scan_directory(
    dir_path: &Path,
    project_dir: &Path,
    parent_path: Option<&str>,
) -> Result<Vec<AssetInfo>, String> {
    let mut assets = Vec::new();
    
    let entries = fs::read_dir(dir_path)
        .map_err(|e| format!("无法读取目录 {}: {}", dir_path.display(), e))?;
    
    for entry in entries {
        let entry = entry.map_err(|e| format!("无法读取目录项: {}", e))?;
        let path = entry.path();
        let file_name = path
            .file_name()
            .ok_or_else(|| "无法获取文件名".to_string())?
            .to_string_lossy()
            .to_string();
        
        // 跳过隐藏文件和目录
        if file_name.starts_with(".") {
            continue;
        }
        
        let is_directory = path.is_dir();
        let asset_type = get_asset_type(&path);
        
        // 计算相对于项目目录的路径
        let relative_path = path
            .strip_prefix(project_dir)
            .map_err(|e| format!("无法计算相对路径: {}", e))?;
        
        let game_path = relative_path
            .to_str()
            .ok_or_else(|| "无法转换路径为字符串".to_string())?
            .replace("\\", "/");
        
        let absolute_path = path
            .to_str()
            .ok_or_else(|| "无法转换路径为字符串".to_string())?
            .to_string();
        
        // 构建显示路径（用于 Tauri 中显示）
        let display_path = format!("../projects/{}/{}", 
            project_dir.file_name().unwrap().to_string_lossy(),
            game_path);
        
        // 获取文件大小
        let size = if !is_directory {
            match fs::metadata(&path) {
                Ok(metadata) => Some(metadata.len()),
                Err(_) => None,
            }
        } else {
            None
        };
        
        // 获取文件扩展名
        let extension = path
            .extension()
            .map(|ext| ext.to_string_lossy().to_string());
        
        // 递归扫描子目录
        let children = if is_directory {
            match scan_directory(&path, project_dir, Some(&game_path)) {
                Ok(child_assets) => Some(child_assets),
                Err(_) => None,
            }
        } else {
            None
        };
        
        let asset_info = AssetInfo {
            name: file_name,
            asset_type,
            game_path,
            absolute_path,
            display_path,
            is_directory,
            children,
            parent_path: parent_path.map(|p| p.to_string()),
            size,
            extension,
        };
        
        assets.push(asset_info);
    }
    
    // 按照目录在前，文件在后的顺序排序
    assets.sort_by(|a, b| {
        if a.is_directory && !b.is_directory {
            std::cmp::Ordering::Less
        } else if !a.is_directory && b.is_directory {
            std::cmp::Ordering::Greater
        } else {
            a.name.to_lowercase().cmp(&b.name.to_lowercase())
        }
    });
    
    Ok(assets)
}

// 获取项目资源
#[tauri::command]
pub fn get_project_assets(app_handle: AppHandle, project_name: String) -> Result<Vec<AssetInfo>, String> {
    let project_dir = get_project_dir(&app_handle, &project_name)?;
    
    scan_directory(&project_dir, &project_dir, None)
}

// 获取指定目录的资源
#[tauri::command]
pub fn get_directory_assets(
    app_handle: AppHandle,
    project_name: String,
    directory_path: String,
) -> Result<Vec<AssetInfo>, String> {
    let project_dir = get_project_dir(&app_handle, &project_name)?;
    let dir_path = project_dir.join(directory_path.replace("/", std::path::MAIN_SEPARATOR_STR));
    
    if !dir_path.exists() || !dir_path.is_dir() {
        return Err(format!("目录 '{}' 不存在", directory_path));
    }
    
    scan_directory(&dir_path, &project_dir, Some(&directory_path))
}

// 获取资源信息
#[tauri::command]
pub fn get_asset_info(
    app_handle: AppHandle,
    project_name: String,
    asset_path: String,
) -> Result<AssetInfo, String> {
    let project_dir = get_project_dir(&app_handle, &project_name)?;
    let path = project_dir.join(asset_path.replace("/", std::path::MAIN_SEPARATOR_STR));
    
    if !path.exists() {
        return Err(format!("资源 '{}' 不存在", asset_path));
    }
    
    let file_name = path
        .file_name()
        .ok_or_else(|| "无法获取文件名".to_string())?
        .to_string_lossy()
        .to_string();
    
    let is_directory = path.is_dir();
    let asset_type = get_asset_type(&path);
    
    // 计算相对于项目目录的路径
    let relative_path = path
        .strip_prefix(&project_dir)
        .map_err(|e| format!("无法计算相对路径: {}", e))?;
    
    let game_path = relative_path
        .to_str()
        .ok_or_else(|| "无法转换路径为字符串".to_string())?
        .replace("\\", "/");
    
    let absolute_path = path
        .to_str()
        .ok_or_else(|| "无法转换路径为字符串".to_string())?
        .to_string();
    
    // 构建显示路径（用于 Tauri 中显示）
    let display_path = format!("../projects/{}/{}", 
        project_dir.file_name().unwrap().to_string_lossy(),
        game_path);
    
    // 获取父目录路径
    let parent_path = if let Some(parent) = relative_path.parent() {
        if parent.as_os_str().is_empty() {
            None
        } else {
            Some(parent.to_string_lossy().replace("\\", "/"))
        }
    } else {
        None
    };
    
    // 获取文件大小
    let size = if !is_directory {
        match fs::metadata(&path) {
            Ok(metadata) => Some(metadata.len()),
            Err(_) => None,
        }
    } else {
        None
    };
    
    // 获取文件扩展名
    let extension = path
        .extension()
        .map(|ext| ext.to_string_lossy().to_string());
    
    // 递归扫描子目录
    let children = if is_directory {
        match scan_directory(&path, &project_dir, Some(&game_path)) {
            Ok(child_assets) => Some(child_assets),
            Err(_) => None,
        }
    } else {
        None
    };
    
    let asset_info = AssetInfo {
        name: file_name,
        asset_type,
        game_path,
        absolute_path,
        display_path,
        is_directory,
        children,
        parent_path,
        size,
        extension,
    };
    
    Ok(asset_info)
}
