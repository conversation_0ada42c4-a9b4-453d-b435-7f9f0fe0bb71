import React, { useState, useEffect } from 'react';
import { Box } from '@mui/material';
import { useDroppable } from '@dnd-kit/core';

interface DroppableBaseProps {
  id: string;
  accepts?: string[];
  children?: React.ReactNode;
  onDragOver?: (data: any) => void;
  onDrop?: (data: any) => void;
  style?: React.CSSProperties;
  className?: string;
}

/**
 * 基础可放置组件
 * 提供拖放目标功能的基础实现
 * 支持拖拽悬停和放置回调
 */
const DroppableBase: React.FC<DroppableBaseProps> = ({ 
  id, 
  accepts, 
  children, 
  onDragOver, 
  onDrop,
  style,
  className 
}) => {
  const { isOver, setNodeRef, active } = useDroppable({
    id: id,
    data: {
      accepts: accepts // 定义接受的拖拽类型
    }
  });

  // 跟踪上一次的isOver状态
  const [wasOver, setWasOver] = useState(false);

  // 使用 useEffect 监听拖拽悬停状态
  useEffect(() => {
    if (isOver && active && onDragOver) {
      // 当有元素拖拽到区域上时,调用 onDragOver 回调
      onDragOver(active.data.current);
    }

    // 更新wasOver状态
    setWasOver(isOver);
  }, [isOver, active, onDragOver, wasOver]);

  // 监听全局拖拽结束事件
  useEffect(() => {
    if (!onDrop) return;

    const handleDragEnd = (e: Event) => {
      const customEvent = e as CustomEvent;
      const { active, over } = customEvent.detail;

      // 检查是否拖放到当前区域
      if (over && over.id === id && active) {
        console.log('DroppableBase: 拖拽结束，触发onDrop回调', active.id, '放置到', id);
        onDrop(active.data.current);
      }
    };

    // 添加全局事件监听
    window.addEventListener('dnd-kit-drag-end', handleDragEnd);

    // 清理函数
    return () => {
      window.removeEventListener('dnd-kit-drag-end', handleDragEnd);
    };
  }, [id, onDrop]);

  // 合并样式
  const combinedStyle: React.CSSProperties = {
    padding: 16,
    minHeight: 100,
    border: '2px dashed',
    borderColor: isOver ? '#1976d2' : '#e0e0e0',
    backgroundColor: isOver ? 'rgba(25, 118, 210, 0.04)' : 'transparent',
    transition: 'all 0.2s',
    borderRadius: 4,
    ...style
  };

  return (
    <Box
      ref={setNodeRef}
      sx={combinedStyle}
      className={className}
    >
      {children}
    </Box>
  );
};

export default DroppableBase;
