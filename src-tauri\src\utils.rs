use std::path::PathBuf;
use std::sync::Mute<PERSON>;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};

/// 错误处理相关的工具函数和宏
pub mod error {
    use std::fmt;

    /// 统一的错误类型
    pub type Result<T> = std::result::Result<T, AppError>;

    /// 应用错误类型
    #[derive(Debug, Clone)]
    pub enum AppError {
        ProjectNotFound(String),
        FileNotFound(String),
        InvalidInput(String),
        IoError(String),
        ValidationError(String),
        StateError(String),
        Other(String),
    }

    impl fmt::Display for AppError {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            match self {
                AppError::ProjectNotFound(msg) => write!(f, "项目未找到: {}", msg),
                AppError::FileNotFound(msg) => write!(f, "文件未找到: {}", msg),
                AppError::InvalidInput(msg) => write!(f, "输入无效: {}", msg),
                AppError::IoError(msg) => write!(f, "IO错误: {}", msg),
                AppError::ValidationError(msg) => write!(f, "验证错误: {}", msg),
                AppError::StateError(msg) => write!(f, "状态错误: {}", msg),
                AppError::Other(msg) => write!(f, "其他错误: {}", msg),
            }
        }
    }

    impl From<AppError> for String {
        fn from(err: AppError) -> String {
            err.to_string()
        }
    }

    /// 格式化错误信息的宏
    macro_rules! format_error {
        ($msg:expr, $err:expr) => {
            format!("{}: {}", $msg, $err)
        };
    }

    pub(crate) use format_error;
}

/// 路径处理相关的工具函数
pub mod path {
    use super::*;

    /// 获取项目根目录
    pub fn get_project_root() -> Result<PathBuf, String> {
        let current_dir = std::env::current_dir()
            .map_err(|e| format!("Failed to get current directory: {}", e))?;

        let project_root = current_dir
            .parent()
            .ok_or_else(|| "Failed to get project root directory".to_string())?;

        Ok(project_root.to_path_buf())
    }

    /// 获取项目目录路径
    pub fn get_project_dir(project_name: &str) -> Result<PathBuf, String> {
        let project_root = get_project_root()?;
        Ok(project_root.join("projects").join(project_name))
    }

    /// 获取项目插件目录路径
    pub fn get_project_plugins_dir(project_name: &str) -> Result<PathBuf, String> {
        let project_dir = get_project_dir(project_name)?;
        Ok(project_dir.join("js").join("plugins"))
    }

    /// 获取项目数据目录路径
    pub fn get_project_data_dir(project_name: &str) -> Result<PathBuf, String> {
        let project_dir = get_project_dir(project_name)?;
        Ok(project_dir.join("data"))
    }

    /// 确保目录存在
    pub fn ensure_dir_exists(path: &PathBuf) -> Result<(), String> {
        if !path.exists() {
            std::fs::create_dir_all(path)
                .map_err(|e| format!("Failed to create directory {:?}: {}", path, e))?;
        }
        Ok(())
    }
}

/// 项目状态管理相关的工具函数
pub mod project_state {
    use super::*;

    /// 获取当前项目名称
    pub fn get_current_project_name(app_handle: &AppHandle) -> Result<String, String> {
        let state_manager = app_handle.state::<Mutex<String>>();
        let project_name = state_manager
            .lock()
            .map_err(|e| format!("Failed to lock project name: {}", e))?
            .clone();

        if project_name.is_empty() {
            return Err("项目名称为空，请先选择项目".to_string());
        }

        Ok(project_name)
    }

    /// 设置当前项目名称
    pub fn set_current_project_name(
        app_handle: &AppHandle,
        project_name: String,
    ) -> Result<(), String> {
        let state_manager = app_handle.state::<Mutex<String>>();
        let mut state = state_manager
            .lock()
            .map_err(|e| format!("Failed to lock project name: {}", e))?;

        *state = project_name;
        Ok(())
    }
}

/// 文件操作相关的工具函数
pub mod file_ops {
    use super::*;
    use std::fs;

    /// 安全读取文件内容
    pub fn read_file_safe(file_path: &PathBuf) -> Result<String, String> {
        if !file_path.exists() {
            return Err(format!("File does not exist: {:?}", file_path));
        }

        fs::read_to_string(file_path)
            .map_err(|e| format!("Failed to read file {:?}: {}", file_path, e))
    }

    /// 安全写入文件内容
    pub fn write_file_safe(file_path: &PathBuf, content: &str) -> Result<(), String> {
        // 确保父目录存在
        if let Some(parent) = file_path.parent() {
            super::path::ensure_dir_exists(&parent.to_path_buf())?;
        }

        fs::write(file_path, content)
            .map_err(|e| format!("Failed to write file {:?}: {}", file_path, e))
    }

    /// 检查文件是否存在
    pub fn file_exists(file_path: &PathBuf) -> bool {
        file_path.exists() && file_path.is_file()
    }
}

/// 日志记录相关的工具函数
pub mod logging {
    /// 统一的日志输出格式
    pub fn log_info(module: &str, message: &str) {
        println!("[{}] INFO: {}", module, message);
    }

    pub fn log_error(module: &str, message: &str) {
        eprintln!("[{}] ERROR: {}", module, message);
    }

    pub fn log_debug(module: &str, message: &str) {
        if cfg!(debug_assertions) {
            println!("[{}] DEBUG: {}", module, message);
        }
    }

    /// 记录函数调用的宏
    macro_rules! log_function_call {
        ($module:expr, $function:expr, $($arg:expr),*) => {
            if cfg!(debug_assertions) {
                println!("[{}] 调用函数: {} 参数: {:?}", $module, $function, ($($arg,)*));
            }
        };
    }

    pub(crate) use log_function_call;
}

/// 验证相关的工具函数
pub mod validation {
    /// 验证场景路径是否有效
    pub fn validate_scene_path(scene_path: &[String]) -> Result<(), String> {
        if scene_path.is_empty() {
            return Err("对象路径为空".to_string());
        }

        let scene_name = &scene_path[0];
        if !scene_name.starts_with("Scene_") {
            return Err(format!("无效的场景名称: {}", scene_name));
        }

        Ok(())
    }

    /// 验证项目名称是否有效
    pub fn validate_project_name(project_name: &str) -> Result<(), String> {
        if project_name.is_empty() {
            return Err("项目名称不能为空".to_string());
        }

        // 检查项目名称是否包含非法字符
        if project_name.contains(['/', '\\', ':', '*', '?', '"', '<', '>', '|']) {
            return Err("项目名称包含非法字符".to_string());
        }

        Ok(())
    }
}

/// 缓存管理相关的工具函数
pub mod cache {
    use super::logging;
    use crate::save::types::{Modification, MODIFICATIONS_CACHE};

    /// 获取缓存的修改列表
    pub fn get_cached_modifications() -> Option<Vec<Modification>> {
        match MODIFICATIONS_CACHE.lock() {
            Ok(cache) => {
                if let Some(ref modifications) = *cache {
                    logging::log_debug(
                        "CacheManager",
                        &format!("从缓存获取 {} 个修改", modifications.len()),
                    );
                    Some(modifications.clone())
                } else {
                    logging::log_debug("CacheManager", "缓存为空");
                    None
                }
            }
            Err(e) => {
                logging::log_error("CacheManager", &format!("获取缓存失败: {}", e));
                None
            }
        }
    }

    /// 更新缓存的修改列表
    pub fn update_cached_modifications(modifications: Vec<Modification>) -> Result<(), String> {
        match MODIFICATIONS_CACHE.lock() {
            Ok(mut cache) => {
                logging::log_info(
                    "CacheManager",
                    &format!("更新缓存，共 {} 个修改", modifications.len()),
                );

                // 添加详细的调试信息
                for (i, modification) in modifications.iter().enumerate() {
                    logging::log_debug(
                        "CacheManager",
                        &format!(
                            "  修改 {}: 路径={:?}, 类名={}, 属性数量={}",
                            i,
                            modification.scene_path,
                            modification.class_name,
                            modification.properties.len()
                        ),
                    );
                    for (key, value) in &modification.properties {
                        logging::log_debug(
                            "CacheManager",
                            &format!("    属性: {} = {}", key, value),
                        );
                    }
                }

                *cache = Some(modifications);
                Ok(())
            }
            Err(e) => {
                let error_msg = format!("更新缓存失败: {}", e);
                logging::log_error("CacheManager", &error_msg);
                Err(error_msg)
            }
        }
    }

    /// 清空缓存
    pub fn clear_cache() -> Result<(), String> {
        match MODIFICATIONS_CACHE.lock() {
            Ok(mut cache) => {
                logging::log_info("CacheManager", "清空缓存");
                *cache = None;
                Ok(())
            }
            Err(e) => {
                let error_msg = format!("清空缓存失败: {}", e);
                logging::log_error("CacheManager", &error_msg);
                Err(error_msg)
            }
        }
    }

    /// 检查缓存是否为空
    pub fn is_cache_empty() -> bool {
        match MODIFICATIONS_CACHE.lock() {
            Ok(cache) => cache.is_none(),
            Err(_) => true,
        }
    }
}
