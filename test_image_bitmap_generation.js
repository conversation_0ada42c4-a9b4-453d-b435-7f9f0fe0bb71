// 测试图片bitmap生成的代码逻辑
console.log("测试图片bitmap生成逻辑");

// 模拟包含图片元素的elements数组
const elementsWithImage = [
  {
    type: 'image',
    bounds: { x: 5, y: 3, width: 144, height: 129 },
    dh: 129,
    dw: 144,
    dx: 5,
    dy: 3,
    sh: 129,
    source: {
      _url: '../projects/Project4/img/faces/Actor1.png',
      width: 576,
      height: 288
    },
    sw: 144,
    sx: 0,
    sy: 7
  },
  {
    type: 'text',
    text: '里德',
    x: 184,
    y: 13,
    maxWidth: 168,
    lineHeight: 36,
    align: 'left',
    bounds: { x: 184, y: 13, width: 168, height: 36 }
  }
];

// 模拟只有文本元素的elements数组
const elementsWithTextOnly = [
  {
    type: 'text',
    text: '纯文本',
    x: 100,
    y: 50,
    maxWidth: 200,
    lineHeight: 24,
    align: 'center',
    bounds: { x: 100, y: 50, width: 200, height: 24 }
  }
];

// 模拟检测图片元素的逻辑
const detectImageElements = (elements) => {
  const imageUrls = [];
  let hasImageElements = false;

  for (const element of elements) {
    if (element.type === 'image') {
      hasImageElements = true;
      if (element.source && element.source._url) {
        imageUrls.push(element.source._url);
      }
    }
  }

  return { hasImageElements, imageUrls };
};

// 模拟生成的代码结构
const generateBitmapCode = (elements, varName = 'sprite_Scene_Title_0_56135') => {
  const { hasImageElements, imageUrls } = detectImageElements(elements);
  let code = '';

  code += `// 合并处理所有_bitmap相关属性\n`;
  code += `if (targetObject_${varName}._bitmap) {\n`;

  if (hasImageElements && imageUrls.length > 0) {
    // 生成图片加载代码
    imageUrls.forEach((imageUrl, index) => {
      code += `    // 加载图片 ${index + 1}\n`;
      code += `    const fullPath_${index} = window.mzl ? '../projects/' + window.currentProjectName + '${imageUrl.replace('../projects/Project4', '')}' : '${imageUrl.replace('../projects/Project4', '')}';\n`;
      code += `    const newBitmap_${index} = ImageManager.loadBitmapFromUrl(fullPath_${index});\n`;
      code += `    newBitmap_${index}.addLoadListener(function (bitmap) {\n`;
      code += `        // 设置bitmap（这会自动触发必要的更新）\n`;
      code += `        targetObject_${varName}.bitmap = bitmap;\n`;
      code += `        if (DEBUG) log('图片加载完成，已设置bitmap:', fullPath_${index});\n`;
      code += `    });\n`;
    });
    code += `    // 图片元素将在加载完成后自动设置bitmap\n`;
  } else {
    // 常规文字重绘处理
    code += `    // 一次性重新绘制文字\n`;
    code += `    // 先清除位图\n`;
    code += `    targetObject_${varName}._bitmap.clear();\n`;
    code += `    // 检查是否有 elements 数组\n`;
    code += `    if (targetObject_${varName}._bitmap.elements && targetObject_${varName}._bitmap.elements.length > 0) {\n`;
    code += `        // 调用统一的重绘方法\n`;
    code += `        targetObject_${varName}._bitmap.redrawing();\n`;
    code += `    }\n`;
  }

  code += `}\n`;
  return code;
};

// 测试包含图片的情况
console.log('\n=== 测试包含图片的elements ===');
const { hasImageElements: hasImage1, imageUrls: urls1 } = detectImageElements(elementsWithImage);
console.log('包含图片元素:', hasImage1);
console.log('图片URLs:', urls1);

const codeWithImage = generateBitmapCode(elementsWithImage);
console.log('\n生成的代码（包含图片）:');
console.log(codeWithImage);

// 测试只有文本的情况
console.log('\n=== 测试只有文本的elements ===');
const { hasImageElements: hasImage2, imageUrls: urls2 } = detectImageElements(elementsWithTextOnly);
console.log('包含图片元素:', hasImage2);
console.log('图片URLs:', urls2);

const codeWithTextOnly = generateBitmapCode(elementsWithTextOnly);
console.log('\n生成的代码（只有文本）:');
console.log(codeWithTextOnly);

// 测试路径处理
console.log('\n=== 测试路径处理 ===');
const testUrl = '../projects/Project4/img/faces/Actor1.png';
const processedUrl = testUrl.replace('../projects/Project4', '');
console.log('原始URL:', testUrl);
console.log('处理后URL:', processedUrl);
console.log('完整路径模板:', `window.mzl ? '../projects/' + window.currentProjectName + '${processedUrl}' : '${processedUrl}'`);

console.log('\n=== 测试完成 ===');
