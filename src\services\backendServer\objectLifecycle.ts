import { recordPropertyModification } from './BackendService';
import { getObjectPath as getPath } from '../../utils/object/objectPro';

// ==================== 对象生命周期管理相关方法 ====================

/**
 * 对象创建信息接口
 */
export interface ObjectCreationInfo {
  parentPath: string[];      // 父对象路径
  objectType: string;        // 对象类型（如 'Sprite', 'Container', 'Label' 等）
  objectName: string;        // 对象名称
  properties?: Record<string, any>; // 初始属性
  position?: { x: number; y: number }; // 初始位置
}

/**
 * 对象删除信息接口
 */
export interface ObjectDeletionInfo {
  objectPath: string[];      // 要删除的对象路径
  objectType: string;        // 对象类型
  objectName: string;        // 对象名称
  parentPath: string[];      // 父对象路径
}

/**
 * 记录对象创建操作到后端
 * @param parentObject 父对象
 * @param createdObject 创建的对象
 * @param objectType 对象类型
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export async function recordObjectCreation(
  parentObject: any,
  createdObject: any,
  objectType: string
): Promise<string> {
  try {
    const creationInfo: ObjectCreationInfo = {
      parentPath: getObjectPath(parentObject),
      objectType: objectType,
      objectName: getObjectName(createdObject),
      properties: extractObjectProperties(createdObject),
      position: getObjectPosition(createdObject)
    };

    console.log(`记录对象创建: ${objectType} "${creationInfo.objectName}" 添加到父对象`);

    // 使用统一的后端记录方法，使用特殊的属性名来标识这是对象创建操作
    return await recordPropertyModification(
      parentObject,
      `__CREATE_CHILD__${objectType}`,
      creationInfo
    );
  } catch (error) {
    console.error('记录对象创建失败:', error);
    throw error;
  }
}

/**
 * 记录对象删除操作到后端
 * @param deletedObject 被删除的对象
 * @param parentObject 父对象
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export async function recordObjectDeletion(
  deletedObject: any,
  parentObject: any
): Promise<string> {
  try {
    const deletionInfo: ObjectDeletionInfo = {
      objectPath: getObjectPath(deletedObject),
      objectType: getObjectType(deletedObject),
      objectName: getObjectName(deletedObject),
      parentPath: getObjectPath(parentObject)
    };

    console.log(`记录对象删除: ${deletionInfo.objectType} "${deletionInfo.objectName}" 从父对象移除`);

    // 使用统一的后端记录方法，使用特殊的属性名来标识这是对象删除操作
    return await recordPropertyModification(
      parentObject,
      `__DELETE_CHILD__${deletionInfo.objectType}`,
      deletionInfo
    );
  } catch (error) {
    console.error('记录对象删除失败:', error);
    throw error;
  }
}

/**
 * 获取对象路径（复用ProtoChange中的方法）
 */
function getObjectPath(object: any): string[] {
  return getPath(object);
}

/**
 * 获取对象类型
 */
function getObjectType(object: any): string {
  if (!object) return 'Unknown';
  return object.constructor?.name || 'Unknown';
}

/**
 * 获取对象名称
 */
function getObjectName(object: any): string {
  if (!object) return 'Unknown';

  // 尝试从多个可能的属性中获取名称
  if (object.name) return object.name;
  if (object._name) return object._name;
  if (object.label) return object.label;
  if (object.title) return object.title;

  // 如果是文本对象，尝试获取文本内容作为名称
  if (object._bitmap?.text) {
    return `文本: ${object._bitmap.text.substring(0, 20)}`;
  }

  // 如果是图片对象，尝试获取图片路径作为名称
  if (object._bitmap?._url) {
    const url = object._bitmap._url;
    const fileName = url.split('/').pop() || url;
    return `图片: ${fileName}`;
  }

  // 默认使用类型名称
  return getObjectType(object);
}

/**
 * 获取对象位置
 */
function getObjectPosition(object: any): { x: number; y: number } | undefined {
  if (!object) return undefined;

  if (typeof object.x === 'number' && typeof object.y === 'number') {
    return { x: object.x, y: object.y };
  }

  if (object.position && typeof object.position.x === 'number' && typeof object.position.y === 'number') {
    return { x: object.position.x, y: object.position.y };
  }

  return undefined;
}

/**
 * 提取对象的关键属性
 */
function extractObjectProperties(object: any): Record<string, any> {
  if (!object) return {};

  const properties: Record<string, any> = {};

  // 基础属性
  if (typeof object.x === 'number') properties.x = object.x;
  if (typeof object.y === 'number') properties.y = object.y;
  if (typeof object.width === 'number') properties.width = object.width;
  if (typeof object.height === 'number') properties.height = object.height;
  if (typeof object.visible === 'boolean') properties.visible = object.visible;
  if (typeof object.alpha === 'number') properties.alpha = object.alpha;
  if (typeof object.rotation === 'number') properties.rotation = object.rotation;

  // 缩放属性
  if (object.scale) {
    if (typeof object.scale.x === 'number') properties.scaleX = object.scale.x;
    if (typeof object.scale.y === 'number') properties.scaleY = object.scale.y;
  }

  // 锚点属性
  if (object.anchor) {
    if (typeof object.anchor.x === 'number') properties.anchorX = object.anchor.x;
    if (typeof object.anchor.y === 'number') properties.anchorY = object.anchor.y;
  }

  // 文本属性
  if (object._bitmap?.text) {
    properties.text = object._bitmap.text;
  }

  // 图片属性
  if (object._bitmap?._url) {
    properties.imageUrl = object._bitmap._url;
  }

  // 颜色属性
  if (typeof object.tint === 'number') properties.tint = object.tint;

  // 对象名称属性
  if (object.name && typeof object.name === 'string') properties.name = object.name;
  if (object._name && typeof object._name === 'string') properties.name = object._name;

  return properties;
}

/**
 * 验证对象创建参数
 */
export function validateObjectCreation(
  parentObject: any,
  objectType: string
): { isValid: boolean; errorMessage?: string } {
  if (!parentObject) {
    return { isValid: false, errorMessage: '父对象不存在' };
  }

  if (!objectType || typeof objectType !== 'string') {
    return { isValid: false, errorMessage: '对象类型无效' };
  }

  // 检查父对象是否支持添加子对象
  if (typeof parentObject.addChild !== 'function') {
    return { isValid: false, errorMessage: '父对象不支持添加子对象' };
  }

  return { isValid: true };
}

/**
 * 验证对象删除参数
 */
export function validateObjectDeletion(
  object: any,
  parentObject?: any
): { isValid: boolean; errorMessage?: string } {
  if (!object) {
    return { isValid: false, errorMessage: '要删除的对象不存在' };
  }

  // 如果提供了父对象，检查父对象是否支持移除子对象
  if (parentObject && typeof parentObject.removeChild !== 'function') {
    return { isValid: false, errorMessage: '父对象不支持移除子对象' };
  }

  return { isValid: true };
}

/**
 * 获取对象创建的默认属性
 */
export function getDefaultPropertiesForType(objectType: string): Record<string, any> {
  const defaults: Record<string, any> = {
    x: 0,
    y: 0,
    visible: true,
    alpha: 1
  };

  switch (objectType.toLowerCase()) {
    case 'sprite':
      return {
        ...defaults,
        name: '新Sprite',
        width: 50,
        height: 50,
        anchor: { x: 0, y: 0 }
      };

    case 'container':
      return {
        ...defaults,
        name: '新Container'
      };

    case 'label':
    case 'text':
      return {
        ...defaults,
        name: '新Label',
        text: '新文本',
        fontSize: 16,
        fontFamily: 'Arial'
      };

    case 'button':
      return {
        ...defaults,
        name: '新Button',
        width: 100,
        height: 30,
        text: '按钮'
      };

    case 'window':
      return {
        ...defaults,
        name: '新Window',
        width: 200,
        height: 150
      };

    default:
      return {
        ...defaults,
        name: `新${objectType}`
      };
  }
}
