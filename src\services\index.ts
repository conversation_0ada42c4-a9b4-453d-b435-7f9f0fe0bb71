/**
 * 服务层统一导出
 * 提供分类的属性处理服务
 */

// 统一属性管理器
export { PropertyManager, PropertyType } from './PropertyManager';
export type { PropertyUpdateResult } from './PropertyManager';

// 基础属性服务
export {
  isBasicProperty,
  getBasicPropertyType,
  updateBasicProperty,
  BASIC_PROPERTY_MAP
} from './backendServer/basicProperty';
export type { BasicPropertyType } from './backendServer/basicProperty';

// Bitmap属性服务
export {
  isBitmapProperty,
  getBitmapPropertyType,
  updateBitmapProperty,
  BITMAP_PROPERTY_MAP,
  FONT_PROPERTIES,
  TEXT_ITEM_PROPERTIES
} from './backendServer/bitmap';
export type { BitmapPropertyType } from './backendServer/bitmap';

// Sprite颜色属性服务
export {
  isSpriteColorProperty,
  getSpriteColorPropertyType,
  supportsSpriteColorProperties,
  updateSpriteColorProperty,
  getSpriteColorPropertyValue,
  validateSpriteColorPropertyValue,
  SPRITE_COLOR_PROPERTY_MAP
} from './backendServer/spriteColor';
export type { SpriteColorPropertyType } from './backendServer/spriteColor';

// 滤镜服务
export {
  recordFilterParamModification,
  recordAddFilter,
  recordRemoveFilter,
  recordReorderFilters
} from './backendServer/filter';

// 对象方法服务
export {
  setObjectMethod,
  executeMethod,
  getObjectMethodCode,
  hasObjectMethod,
  removeObjectMethod
} from './backendServer/objectMethod';

// 对象生命周期管理服务
export {
  recordObjectCreation,
  recordObjectDeletion,
  validateObjectCreation,
  validateObjectDeletion,
  getDefaultPropertiesForType
} from './backendServer/objectLifecycle';
export type { ObjectCreationInfo, ObjectDeletionInfo } from './backendServer/objectLifecycle';

// 通用后端服务（向后兼容）
export {
  recordPropertyModification,
  saveAllModifications,
  clearModifications
} from './backendServer/BackendService';

// PropertyPanel服务
export { PropertyPanelService } from './PropertyPanelService';
