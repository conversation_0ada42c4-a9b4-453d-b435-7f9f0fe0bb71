use tauri::AppHandle;
use crate::utils::logging;
use super::handlers::{type_handlers, object_handlers};

/// 操作请求结构体
#[derive(Debug, Clone)]
pub struct OperationRequest {
    pub operation_mode: String,     // "TYPE" | "OBJECT"
    pub operation_type: String,     // "CREATE" | "DELETE" | "MODIFY"
    pub target_path: Vec<String>,   // 路径
    pub field_name: String,         // 字段名字
    pub field_value: serde_json::Value, // 字段值
    pub class_name: String,         // 类名
    pub top_level_type: Option<String>, // 顶级类型（仅类型操作需要）
}

/// 操作路由器
/// 架构：
/// 1. 第一层分类：根据操作模式分类（TYPE vs OBJECT）
/// 2. 第二层分类：根据操作类型分类（CREATE vs DELETE vs MODIFY）
/// 3. 第三层处理：根据路径和字段操作具体的值
pub fn route_operation(
    app_handle: AppHandle,
    request: OperationRequest,
) -> Result<String, String> {
    logging::log_info(
        "OperationRouter",
        &format!(
            "路由操作请求 - 模式: {}, 类型: {}, 路径: {:?}",
            request.operation_mode, request.operation_type, request.target_path
        ),
    );

    // 第一层分类：根据操作模式分类
    match request.operation_mode.as_str() {
        "TYPE" => {
            logging::log_info("OperationRouter", "路由到类型操作处理器");
            route_type_operation(app_handle, request)
        }
        "OBJECT" => {
            logging::log_info("OperationRouter", "路由到对象操作处理器");
            route_object_operation(app_handle, request)
        }
        _ => {
            let error_msg = format!("未知的操作模式: {}", request.operation_mode);
            logging::log_error("OperationRouter", &error_msg);
            Err(error_msg)
        }
    }
}

/// 路由类型操作
/// 第二层分类：根据操作类型分类
fn route_type_operation(
    app_handle: AppHandle,
    request: OperationRequest,
) -> Result<String, String> {
    let top_level_type = request
        .top_level_type
        .as_ref()
        .ok_or("类型操作缺少顶级类型")?;

    logging::log_info(
        "OperationRouter",
        &format!(
            "处理类型操作 - 顶级类型: {}, 操作类型: {}",
            top_level_type, request.operation_type
        ),
    );

    // 第二层分类：根据操作类型分类
    match request.operation_type.as_str() {
        "CREATE" => {
            logging::log_info("OperationRouter", "路由到类型创建操作");
            type_handlers::handle_type_create(app_handle, request)
        }
        "DELETE" => {
            logging::log_info("OperationRouter", "路由到类型删除操作");
            type_handlers::handle_type_delete(app_handle, request)
        }
        "MODIFY" => {
            logging::log_info("OperationRouter", "路由到类型修改操作");
            type_handlers::handle_type_modify(app_handle, request)
        }
        _ => {
            let error_msg = format!("未知的类型操作类型: {}", request.operation_type);
            logging::log_error("OperationRouter", &error_msg);
            Err(error_msg)
        }
    }
}

/// 路由对象操作
/// 第二层分类：根据操作类型分类
fn route_object_operation(
    app_handle: AppHandle,
    request: OperationRequest,
) -> Result<String, String> {
    logging::log_info(
        "OperationRouter",
        &format!(
            "处理对象操作 - 场景路径: {:?}, 操作类型: {}",
            request.target_path, request.operation_type
        ),
    );

    // 第二层分类：根据操作类型分类
    match request.operation_type.as_str() {
        "CREATE" => {
            logging::log_info("OperationRouter", "路由到对象创建操作");
            object_handlers::handle_object_create(app_handle, request)
        }
        "DELETE" => {
            logging::log_info("OperationRouter", "路由到对象删除操作");
            object_handlers::handle_object_delete(app_handle, request)
        }
        "MODIFY" => {
            logging::log_info("OperationRouter", "路由到对象修改操作");
            object_handlers::handle_object_modify(app_handle, request)
        }
        _ => {
            let error_msg = format!("未知的对象操作类型: {}", request.operation_type);
            logging::log_error("OperationRouter", &error_msg);
            Err(error_msg)
        }
    }
}
