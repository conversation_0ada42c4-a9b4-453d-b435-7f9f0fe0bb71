import React, { useState, useCallback, useRef } from "react";
import { Box, Typography, CircularProgress, Fade } from "@mui/material";
import ImageIcon from '@mui/icons-material/Image';
import AddPhotoAlternateIcon from '@mui/icons-material/AddPhotoAlternate';
import FileDownloadDoneIcon from '@mui/icons-material/FileDownloadDone';
import CloudDownloadIcon from '@mui/icons-material/CloudDownload';
import useProjectStore, { AssetType } from "../../store/Store";
import DroppableBase from '../drag/DroppableBase';

interface ImagePreviewProps {
  src: string;
  width?: number;
  height?: number;
  onImageChange?: (newSrc: string) => void;
  label?: string;
  acceptTypes?: string[];
  disabled?: boolean;
}

/**
 * 图片预览组件，支持拖拽放置图片
 * 可以接受拖拽过来的图片组件，并在鼠标松开后重新设置图片
 */
const ImagePreview: React.FC<ImagePreviewProps> = ({
  src,
  width = 100,
  height = 100,
  onImageChange,
  label = "图片预览",
  acceptTypes = [AssetType.Image],
  disabled = false,
}) => {
  const projectName = useProjectStore(state => state.projectName);

  // 图片加载错误状态
  const [imageError, setImageError] = useState(false);
  // 图片加载中状态
  const [isLoading, setIsLoading] = useState(false);
  // 放置成功状态（用于显示成功动画）
  const [dropSuccess, setDropSuccess] = useState(false);
  // 上一次的图片源
  const prevSrcRef = useRef<string>(src);

  // 拖拽悬停状态
  const [isOver, setIsOver] = useState(false);

  // 处理图片路径
  const getImagePath = useCallback((imageSrc: string) => {
    if (!imageSrc) return '';

    // 如果是相对路径，添加项目路径前缀
    if (!imageSrc.startsWith('http') && !imageSrc.startsWith('data:')) {
      return `../${projectName}/${imageSrc}`;
    }

    return imageSrc;
  }, [projectName]);

  // 处理传统文件拖拽悬停
  const handleFileDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  // 处理组件拖拽悬停
  const handleDragOver = useCallback((data: any) => {
    console.log('ImagePreview: 拖拽悬停', data);
    setIsOver(true);
  }, []);

  // 处理拖拽放置
  const handleDrop = useCallback((data: any) => {
    console.log('ImagePreview: 拖拽放置', data);
    setIsOver(false);

    if (!onImageChange || disabled) return;

    // 检查拖拽项类型是否被接受
    if (data && data.type && acceptTypes.includes(data.type)) {
      console.log('ImagePreview: 接收到拖拽的图片资源:', data);

      // 确定图片路径 - 支持多种数据结构
      let imagePath = '';
      if (data.asset && data.asset.display_path) {
        // 新的数据结构：data.asset.display_path
        imagePath = data.asset.display_path;
      } else if (data.asset && data.asset.game_path) {
        // 备选：使用game_path
        imagePath = data.asset.game_path;
      } else if (data.path) {
        // 备选：直接使用path
        imagePath = data.path;
      } else {
        console.warn('ImagePreview: 无法确定图片路径', data);
        return;
      }

      console.log('ImagePreview: 使用图片路径:', imagePath);

      // 更新图片路径
      onImageChange(imagePath);

      // 显示成功状态
      setDropSuccess(true);

      // 2秒后重置成功状态
      setTimeout(() => {
        setDropSuccess(false);
      }, 2000);
    } else {
      console.log('ImagePreview: 不接受的拖拽类型:', data?.type, '接受的类型:', acceptTypes);
    }
  }, [onImageChange, acceptTypes, disabled]);

  // 处理拖拽离开
  const handleDragLeave = useCallback(() => {
    setIsOver(false);
  }, []);

  // 处理传统文件拖拽（从文件系统拖拽文件）
  const handleFileDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    // 如果组件被禁用，不处理拖放
    if (disabled) return;

    // 处理文件拖拽
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];

      // 检查是否是图片文件
      if (file.type.startsWith('image/') && acceptTypes.includes(AssetType.Image)) {
        setIsLoading(true);

        const reader = new FileReader();
        reader.onload = (event) => {
          if (event.target && event.target.result && onImageChange) {
            // 调用回调函数，传递新的图片路径
            onImageChange(file.name);

            // 结束加载状态
            setIsLoading(false);

            // 显示成功状态
            setDropSuccess(true);

            // 2秒后重置成功状态
            setTimeout(() => {
              setDropSuccess(false);
            }, 2000);
          }
        };
        reader.readAsDataURL(file);
      }
    }
  }, [onImageChange, acceptTypes, disabled]);

  // 图片加载错误处理
  const handleImageError = useCallback(() => {
    console.error('图片加载失败:', src);
    setImageError(true);
  }, [src]);

  // 图片加载成功处理
  const handleImageLoad = useCallback((e: React.SyntheticEvent<HTMLImageElement>) => {
    setImageError(false);
    console.log('图片加载成功:', src);
    console.log('图片尺寸:', (e.target as HTMLImageElement).naturalWidth, 'x', (e.target as HTMLImageElement).naturalHeight);
  }, [src]);

  return (
    <Box sx={{ mb: 2 }}>
      <Typography variant="subtitle2" sx={{ mb: 1 }}>
        {label}
      </Typography>
      <DroppableBase
        id={`image-preview-${label}`}
        accepts={acceptTypes}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        style={{
          width,
          height,
          border: "2px dashed",
          borderColor: disabled ? "#bdbdbd" :
            dropSuccess ? "#4caf50" :
              isOver ? "#ff9800" : "#2196f3",
          borderRadius: 4,
          overflow: "hidden",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: disabled ? "rgba(0, 0, 0, 0.05)" :
            dropSuccess ? "rgba(76, 175, 80, 0.1)" :
              isOver ? "rgba(255, 152, 0, 0.1)" : "rgba(33, 150, 243, 0.1)",
          transition: "all 0.2s",
          position: "relative",
          cursor: disabled ? "not-allowed" : "pointer",
          opacity: disabled ? 0.7 : 1,
          padding: 0, // 覆盖DroppableBase的默认padding
          minHeight: height, // 确保最小高度
        }}
      >
        {/* 传统文件拖拽处理层 */}
        <Box
          onDragOver={handleFileDragOver}
          onDrop={handleFileDrop}
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative',
          }}
        >
          {/* 加载中状态 */}
          {isLoading && (
            <Fade in={isLoading}>
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: 'rgba(0, 0, 0, 0.3)',
                  zIndex: 3,
                }}
              >
                <CircularProgress color="primary" />
              </Box>
            </Fade>
          )}

          {/* 成功放置动画 */}
          {dropSuccess && (
            <Fade in={dropSuccess}>
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: 'rgba(76, 175, 80, 0.2)',
                  zIndex: 2,
                }}
              >
                <FileDownloadDoneIcon
                  sx={{
                    fontSize: 60,
                    color: '#4caf50',
                    animation: 'scaleUp 0.5s forwards',
                    '@keyframes scaleUp': {
                      '0%': { transform: 'scale(0.5)', opacity: 0 },
                      '100%': { transform: 'scale(1)', opacity: 1 },
                    },
                  }}
                />
              </Box>
            </Fade>
          )}

          {/* 图片预览 */}
          {src && !imageError ? (
            <img
              src={getImagePath(src)}
              alt="Preview"
              style={{
                maxWidth: "100%",
                maxHeight: "100%",
                objectFit: "contain",
                transition: "all 0.3s",
              }}
              onLoad={handleImageLoad}
              onError={handleImageError}
            />
          ) : (
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                color: disabled ? "#bdbdbd" : "#9e9e9e",
                padding: 2,
                textAlign: "center",
              }}
            >
              <AddPhotoAlternateIcon sx={{
                fontSize: 40,
                mb: 1,
                animation: isOver ? 'pulse 1.5s infinite' : 'none',
                '@keyframes pulse': {
                  '0%': { transform: 'scale(1)' },
                  '50%': { transform: 'scale(1.1)' },
                  '100%': { transform: 'scale(1)' },
                },
              }} />
              <Typography variant="caption" align="center">
                {disabled ? "图片预览已禁用" :
                  imageError ? "图片加载失败" :
                    isOver ? "释放鼠标放置图片" : "拖拽图片到此处"}
              </Typography>
              {acceptTypes.length > 0 && acceptTypes[0] !== AssetType.Image && (
                <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, fontSize: '0.7rem' }}>
                  接受类型: {acceptTypes.join(', ')}
                </Typography>
              )}
            </Box>
          )}

          {/* 拖拽提示覆盖层 */}
          {isOver && !disabled && !isLoading && (
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(255, 152, 0, 0.1)',
                border: '2px dashed #ff9800',
                borderRadius: 1,
                zIndex: 1,
                pointerEvents: 'none',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <CloudDownloadIcon
                sx={{
                  fontSize: 50,
                  color: '#ff9800',
                  opacity: 0.7,
                }}
              />
            </Box>
          )}
        </Box>
      </DroppableBase>
    </Box>
  );
};

export default ImagePreview;
