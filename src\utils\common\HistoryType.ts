// 操作类型
export enum OperationType {
  MODIFY_PROPERTY = 'ModifyProperty',
  ADD_OBJECT = 'AddObject',
  DELETE_OBJECT = 'DeleteObject',
}

// 操作详情接口
export interface OperationDetails {
  propertyName?: string;
  oldValue?: any;
  newValue?: any;
  // 可以根据需要添加更多字段
}

// 操作记录接口
export interface Operation {
  id: string;
  operation_type: OperationType;
  timestamp: number;
  object_path: string[];
  details: OperationDetails;
}

// // 历史记录状态接口
// export interface HistoryState {
//   operations: Operation[];
//   current_index: number;
//   max_size: number;
// }
