/**
 * 获取对象的路径，其中场景是名字，后面都是索引
 * @param object 要获取路径的对象
 * @returns 对象路径数组，第一个元素是场景名称，后面的元素是索引
 */
export function getObjectPath(object: any): string[] {
  if (!object) {
    console.error('ProtoChange: 对象为空，无法获取路径');
    return [];
  }

  try {
    const path: string[] = [];
    let current = object;

    console.log(`ProtoChange: 获取对象路径，对象类型: ${object.constructor.name}`);

    // 如果对象有父对象，获取在父对象中的索引
    if (current && current.parent) {
      // 获取对象在父对象中的索引
      const index = current.parent.children.indexOf(current);
      if (index >= 0) {
        path.push(index.toString());
        console.log(`ProtoChange: 对象在父对象中的索引: ${index}`);
      }

      // 向上遍历到场景
      let parent = current.parent;
      while (parent && parent.parent) {
        const parentIndex = parent.parent.children.indexOf(parent);
        if (parentIndex >= 0) {
          path.unshift(parentIndex.toString());
        }
        parent = parent.parent;
      }

      // 添加场景名称
      if (parent) {
        path.unshift(parent.constructor.name);
        console.log(`ProtoChange: 找到场景: ${parent.constructor.name}`);
      }
    } else {
      // 如果没有parent属性，检查是否是场景对象
      const className = object.constructor.name;
      if (className.startsWith('Scene_')) {
        path.push(className);
        console.log(`ProtoChange: 使用场景名称作为路径: ${className}`);
      } else {
        console.error(`ProtoChange: 无法确定对象的路径，对象类型: ${className}`);
        return [];
      }
    }

    console.log(`ProtoChange: 获取的对象路径: ${path.join('/')}`);
    return path;
  } catch (error) {
    console.error('ProtoChange: 获取对象路径失败:', error);
    return [];
  }
}
