use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::fs;
use tokio::fs::File;
use tokio::io::AsyncWriteExt;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ClassInfo {
    pub name: String,
    pub parent: Option<String>,
    pub children: HashMap<String, ClassInfo>,
}

impl ClassInfo {
    pub fn new(name: String, parent: Option<String>) -> Self {
        ClassInfo {
            name,
            parent,
            children: HashMap::new(),
        }
    }
}

/// Reads and parses JavaScript files to extract class information
pub async fn read_js_files() -> Result<(), Box<dyn std::error::Error>> {
    let files = vec![
        "rmmz_core",
        "rmmz_managers",
        "rmmz_objects",
        "rmmz_scenes",
        "rmmz_sprites",
        "rmmz_windows",
    ];

    // Get the current directory
    let current_dir = std::env::current_dir()?;
    println!("Current directory: {:?}", current_dir);

    // Find the coreRPGtype directory
    let mut core_dir = current_dir.clone();
    if !core_dir.ends_with("src-tauri") {
        core_dir = core_dir.join("src-tauri");
    }
    core_dir = core_dir.join("src").join("coreRPGtype");

    println!("Core directory: {:?}", core_dir);

    // Ensure the directory exists
    if !core_dir.exists() {
        return Err(format!("Directory not found: {:?}", core_dir).into());
    }

    // First pass: Collect all classes from all files
    let mut all_classes: HashMap<String, ClassInfo> = HashMap::new();
    let mut file_classes: HashMap<String, Vec<String>> = HashMap::new();

    for file_name in &files {
        let file_path = core_dir.join(format!("{}.js", file_name));
        println!("Reading file: {:?}", file_path);

        if !file_path.exists() {
            return Err(format!("File not found: {:?}", file_path).into());
        }

        let content = fs::read_to_string(&file_path)?;

        // Parse the file content to extract class information
        let file_class_info = parse_js_file(&content)?;

        // Store which classes belong to which file
        let class_names: Vec<String> = file_class_info.keys().cloned().collect();
        file_classes.insert((*file_name).to_string(), class_names);

        // Add classes to the global collection
        for (class_name, class_info) in file_class_info {
            all_classes.insert(class_name, class_info);
        }
    }

    println!("Collected {} classes from all files", all_classes.len());

    // We no longer need to build the complete inheritance tree
    // let _inheritance_tree = build_inheritance_tree(&all_classes);

    // Third pass: Save each file's classes as JSON, but with complete inheritance info
    for file_name in files {
        let mut file_class_info: HashMap<String, ClassInfo> = HashMap::new();
        let file_name_str = file_name.to_string();

        if let Some(class_names) = file_classes.get(&file_name_str) {
            // Create a map of parent-child relationships
            let mut parent_child_map: HashMap<String, Vec<String>> = HashMap::new();

            // First, collect all parent-child relationships
            for class_name in class_names {
                if let Some(class_info) = all_classes.get(class_name) {
                    if let Some(parent_name) = &class_info.parent {
                        parent_child_map
                            .entry(parent_name.clone())
                            .or_insert_with(Vec::new)
                            .push(class_name.clone());
                    }
                }
            }

            // Find root classes (classes with no parent or parent not in this file)
            let mut root_classes: Vec<String> = Vec::new();
            for class_name in class_names {
                if let Some(class_info) = all_classes.get(class_name) {
                    if class_info.parent.is_none()
                        || !class_names.contains(&class_info.parent.as_ref().unwrap())
                    {
                        root_classes.push(class_name.clone());
                    }
                }
            }

            // Build the class hierarchy for each root class
            for root_class_name in &root_classes {
                if let Some(class_info) = all_classes.get(root_class_name) {
                    let mut root_class =
                        ClassInfo::new(class_info.name.clone(), class_info.parent.clone());

                    // Add children recursively
                    build_class_hierarchy(&mut root_class, &parent_child_map, &all_classes);

                    file_class_info.insert(root_class_name.clone(), root_class);
                }
            }
        }

        // Save the class information as JSON
        let json_path = core_dir.join(format!("{}.json", file_name));
        save_json(&json_path.to_string_lossy(), &file_class_info).await?;
    }

    Ok(())
}

/// Parses JavaScript file content to extract class information
fn parse_js_file(content: &str) -> Result<HashMap<String, ClassInfo>, Box<dyn std::error::Error>> {
    let mut top_level_classes: HashMap<String, ClassInfo> = HashMap::new();
    let mut all_classes: HashMap<String, ClassInfo> = HashMap::new();

    // Regular expressions for extracting class information
    let class_def_regex = Regex::new(r"function\s+([A-Za-z_][A-Za-z0-9_]*)\s*\(")?;
    let inheritance_regex = Regex::new(
        r"([A-Za-z_][A-Za-z0-9_]*).prototype\s*=\s*Object\.create\(([A-Za-z_][A-Za-z0-9_]*)\.prototype\)",
    )?;
    let _constructor_regex = Regex::new(
        r"([A-Za-z_][A-Za-z0-9_]*).prototype\.constructor\s*=\s*([A-Za-z_][A-Za-z0-9_]*)",
    )?;

    println!("Parsing file with {} lines", content.lines().count());

    // First pass: Find all class definitions
    for line in content.lines() {
        if let Some(captures) = class_def_regex.captures(line) {
            let class_name = captures[1].to_string();
            // Skip if the class name contains invalid characters or is a reserved word
            if class_name.contains("$") || class_name == "function" {
                continue;
            }
            let class_info = ClassInfo::new(class_name.clone(), None);
            all_classes.insert(class_name.clone(), class_info);
        }
    }

    println!("Found {} classes", all_classes.len());

    // Second pass: Find inheritance relationships
    for line in content.lines() {
        if let Some(captures) = inheritance_regex.captures(line) {
            let child_name = captures[1].to_string();
            let parent_name = captures[2].to_string();

            // Make sure the child class exists in our collection
            if !all_classes.contains_key(&child_name) {
                let class_info = ClassInfo::new(child_name.clone(), None);
                all_classes.insert(child_name.clone(), class_info);
            }

            if let Some(child_class) = all_classes.get_mut(&child_name) {
                child_class.parent = Some(parent_name.clone());
            }
        }
    }

    // Also check constructor assignments for inheritance relationships
    for line in content.lines() {
        if let Some(captures) = _constructor_regex.captures(line) {
            let class_name = captures[1].to_string();
            let _constructor_name = captures[2].to_string();

            // Make sure the class exists in our collection
            if !all_classes.contains_key(&class_name) {
                let class_info = ClassInfo::new(class_name.clone(), None);
                all_classes.insert(class_name.clone(), class_info);
            }
        }
    }

    // We no longer need to extract methods and properties
    println!(
        "After processing class definitions: {} classes",
        all_classes.len()
    );

    // Build the class hierarchy
    for (class_name, class_info) in all_classes.iter() {
        if class_info.parent.is_none() {
            // This is a top-level class
            top_level_classes.insert(class_name.clone(), class_info.clone());
        }
    }

    // Add child classes to their parents
    let mut child_parent_map: HashMap<String, String> = HashMap::new();
    for (class_name, class_info) in all_classes.iter() {
        if let Some(parent_name) = &class_info.parent {
            child_parent_map.insert(class_name.clone(), parent_name.clone());
        }
    }

    // Build the final hierarchy
    let mut result: HashMap<String, ClassInfo> = HashMap::new();

    // Start with top-level classes
    for (class_name, class_info) in top_level_classes.iter() {
        result.insert(class_name.clone(), class_info.clone());
    }

    // Add child classes to their parents
    for (child_name, parent_name) in child_parent_map.iter() {
        let child_info = all_classes.get(child_name).unwrap().clone();

        // Find the parent class in the hierarchy
        add_child_to_parent(&mut result, parent_name, child_name.clone(), child_info);
    }

    // If the result is empty, just return all classes
    // This happens when all classes in the file inherit from classes in other files
    if result.is_empty() && !all_classes.is_empty() {
        println!(
            "No top-level classes found, returning all {} classes",
            all_classes.len()
        );
        return Ok(all_classes);
    }

    // Check if we lost any classes in the hierarchy building process
    if result.len() < all_classes.len() {
        println!(
            "Warning: Lost {} classes in hierarchy building ({} -> {})",
            all_classes.len() - result.len(),
            all_classes.len(),
            result.len()
        );

        // Add any missing classes to the result
        for (class_name, class_info) in all_classes.iter() {
            if !result.contains_key(class_name) {
                result.insert(class_name.clone(), class_info.clone());
            }
        }

        println!("After adding missing classes: {} classes", result.len());
    }

    Ok(result)
}

/// Recursively adds a child class to its parent in the hierarchy
fn add_child_to_parent(
    classes: &mut HashMap<String, ClassInfo>,
    parent_name: &str,
    child_name: String,
    child_info: ClassInfo,
) {
    // Check if the parent is a top-level class
    if classes.contains_key(parent_name) {
        if let Some(parent_class) = classes.get_mut(parent_name) {
            parent_class.children.insert(child_name, child_info);
        }
    } else {
        // Parent might be a child of another class
        for (_, class_info) in classes.iter_mut() {
            if class_info.children.contains_key(parent_name) {
                if let Some(parent_class) = class_info.children.get_mut(parent_name) {
                    parent_class.children.insert(child_name, child_info);
                    return;
                }
            }

            // Recursively check deeper in the hierarchy
            add_child_to_parent_recursive(
                class_info,
                parent_name,
                child_name.clone(),
                child_info.clone(),
            );
        }
    }
}

/// Helper function for recursive child addition
fn add_child_to_parent_recursive(
    class_info: &mut ClassInfo,
    parent_name: &str,
    child_name: String,
    child_info: ClassInfo,
) {
    for (name, child) in class_info.children.iter_mut() {
        if name == parent_name {
            child.children.insert(child_name, child_info);
            return;
        }

        add_child_to_parent_recursive(child, parent_name, child_name.clone(), child_info.clone());
    }
}

/// Recursively builds the class hierarchy
fn build_class_hierarchy(
    class_info: &mut ClassInfo,
    parent_child_map: &HashMap<String, Vec<String>>,
    all_classes: &HashMap<String, ClassInfo>,
) {
    // Check if this class has any children
    if let Some(children) = parent_child_map.get(&class_info.name) {
        for child_name in children {
            if let Some(child_info) = all_classes.get(child_name) {
                let mut new_child =
                    ClassInfo::new(child_info.name.clone(), Some(class_info.name.clone()));

                // Recursively add grandchildren
                build_class_hierarchy(&mut new_child, parent_child_map, all_classes);

                class_info.children.insert(child_name.clone(), new_child);
            }
        }
    }
}

/// Builds a complete inheritance tree from all classes
fn build_inheritance_tree(all_classes: &HashMap<String, ClassInfo>) -> HashMap<String, ClassInfo> {
    let mut result: HashMap<String, ClassInfo> = HashMap::new();

    // First, identify all root classes (classes with no parent)
    let mut root_classes: HashMap<String, ClassInfo> = HashMap::new();
    for (class_name, class_info) in all_classes {
        if class_info.parent.is_none() {
            root_classes.insert(class_name.clone(), class_info.clone());
        }
    }

    println!("Found {} root classes", root_classes.len());

    // Create a set to track which classes have been processed
    let mut processed_classes: HashSet<String> = HashSet::new();

    // Start building the tree from root classes
    for (class_name, class_info) in root_classes {
        let mut tree_class = class_info.clone();
        build_class_tree(&mut tree_class, all_classes, &mut processed_classes);
        result.insert(class_name, tree_class);
    }

    // Add any classes that weren't added to the tree yet
    // (This can happen if there are circular dependencies or missing parent classes)
    for (class_name, class_info) in all_classes {
        if !processed_classes.contains(class_name) {
            let mut tree_class = class_info.clone();
            build_class_tree(&mut tree_class, all_classes, &mut processed_classes);
            result.insert(class_name.clone(), tree_class);
            processed_classes.insert(class_name.clone());
        }
    }

    println!("Built inheritance tree with {} classes", result.len());

    result
}

/// Recursively builds the class tree by adding children to a class
fn build_class_tree(
    class_info: &mut ClassInfo,
    all_classes: &HashMap<String, ClassInfo>,
    processed_classes: &mut HashSet<String>,
) {
    // Mark this class as processed
    processed_classes.insert(class_info.name.clone());

    // Find all classes that have this class as their parent
    for (child_name, child_info) in all_classes {
        if let Some(parent_name) = &child_info.parent {
            if parent_name == &class_info.name && !processed_classes.contains(child_name) {
                let mut tree_child = child_info.clone();
                build_class_tree(&mut tree_child, all_classes, processed_classes);
                class_info.children.insert(child_name.clone(), tree_child);
                processed_classes.insert(child_name.clone());
            }
        }
    }
}

/// Saves class information as JSON
async fn save_json(
    path: &str,
    class_info: &HashMap<String, ClassInfo>,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("Saving JSON to {}", path);
    println!("Class info contains {} classes", class_info.len());

    // Don't save empty class info
    if class_info.is_empty() {
        println!("Warning: No classes found, saving empty JSON");
        let json_content = "{}".to_string();
        let mut file = File::create(path).await?;
        file.write_all(json_content.as_bytes()).await?;
        return Ok(());
    }

    let json_content = serde_json::to_string_pretty(class_info)?;
    let mut file = File::create(path).await?;
    file.write_all(json_content.as_bytes()).await?;
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_read_js_files() {
        let result = read_js_files().await;

        // This test will only pass if the JS files exist in the correct location
        // If they don't exist, we'll just print the error and skip the test
        if let Err(e) = &result {
            println!("Skipping test: {}", e);
            return;
        }

        assert!(
            result.is_ok(),
            "Failed to read JS files: {:?}",
            result.err()
        );

        // Get the current directory
        let current_dir = std::env::current_dir().unwrap();
        let mut core_dir = current_dir.clone();
        if !core_dir.ends_with("src-tauri") {
            core_dir = core_dir.join("src-tauri");
        }
        core_dir = core_dir.join("src").join("coreRPGtype");

        // Check if JSON files were created
        let files = vec![
            "rmmz_core",
            "rmmz_managers",
            "rmmz_objects",
            "rmmz_scenes",
            "rmmz_sprites",
            "rmmz_windows",
        ];

        for file_name in files {
            let json_path = core_dir.join(format!("{}.json", file_name));

            if !json_path.exists() {
                println!("JSON file not found: {:?}", json_path);
                continue;
            }

            // Read the JSON file to verify it contains valid data
            let json_content = fs::read_to_string(&json_path).unwrap();
            let parsed: Result<HashMap<String, ClassInfo>, _> = serde_json::from_str(&json_content);
            assert!(
                parsed.is_ok(),
                "Invalid JSON content in {:?}: {:?}",
                json_path,
                parsed.err()
            );

            let class_info = parsed.unwrap();
            // We don't assert that class_info is not empty, because we now allow empty JSON files
            println!(
                "JSON file {} contains {} classes",
                json_path.display(),
                class_info.len()
            );
        }
    }
}
