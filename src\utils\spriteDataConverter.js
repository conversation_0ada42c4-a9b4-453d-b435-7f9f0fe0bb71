/**
 * Sprite数据转换工具
 * 用于在elements数组数据和sprite对象之间进行转换
 */

/**
 * 将elements数组数据转换为sprite对象
 * @param {Array} elementsData - 包含文本和图片元素的数组
 * @param {Object} options - 转换选项
 * @param {number} options.canvasWidth - 画布宽度，默认816
 * @param {number} options.canvasHeight - 画布高度，默认624
 * @param {number} options.spriteX - sprite的X坐标，默认0
 * @param {number} options.spriteY - sprite的Y坐标，默认0
 * @param {number} options.anchorX - sprite的X锚点，默认0
 * @param {number} options.anchorY - sprite的Y锚点，默认0
 * @param {number} options.scaleX - sprite的X缩放，默认1
 * @param {number} options.scaleY - sprite的Y缩放，默认1
 * @param {number} options.rotation - sprite的旋转角度（弧度），默认0
 * @param {boolean} options.fontBold - 字体是否加粗，默认false
 * @param {string} options.fontFace - 字体名称，默认"sans-serif"
 * @param {boolean} options.fontItalic - 字体是否斜体，默认false
 * @param {number} options.fontSize - 字体大小，默认16
 * @param {string} options.outlineColor - 描边颜色，默认"rgba(0, 0, 0, 0.5)"
 * @param {number} options.outlineWidth - 描边宽度，默认3
 * @param {string} options.textColor - 文字颜色，默认"#ffffff"
 * @returns {Promise<Sprite>} 返回完整的sprite对象
 */
/**
 * 使用ArrayBuffer的方式将elements数据转换为sprite
 * @param {Array} elementsData - 元素数据数组
 * @param {Object} options - 选项参数
 * @returns {Promise<Object>} 转换后的sprite对象
 */
export async function elementsToSpriteByBuf(elementsData, options = {}) {
  const {
    canvasWidth = 816,
    canvasHeight = 624,
    spriteX = 0,
    spriteY = 0,
    anchorX = 0,
    anchorY = 0,
    scaleX = 1,
    scaleY = 1,
    rotation = 0
  } = options;

  console.log('elementsToSpriteByBuf开始转换，元素数量:', elementsData.length);

  if (!elementsData || !Array.isArray(elementsData)) {
    throw new Error('elementsData必须是一个数组');
  }

  // 检查RPG Maker API是否可用
  if (typeof window.Bitmap === 'undefined' || typeof window.Sprite === 'undefined' || typeof window.ImageManager === 'undefined') {
    throw new Error('RPG Maker API尚未加载');
  }

  // 检查是否有外部的convertFilePathToArrayBuffer方法
  if (!window.convertFilePathToArrayBuffer || typeof window.convertFilePathToArrayBuffer !== 'function') {
    console.error('未找到外部的convertFilePathToArrayBuffer方法');
    throw new Error('需要外部提供convertFilePathToArrayBuffer方法');
  }

  try {
    // 收集所有需要转换的图片路径
    const imageUrls = new Set();
    elementsData.forEach(element => {
      if (element.type === 'image' && element.source && element.source._url) {
        imageUrls.add(element.source._url);
      }
    });

    console.log('需要转换的图片路径:', Array.from(imageUrls));

    // 创建bitmap映射表
    const bitmapMap = new Map();

    // 使用外部方法将路径转换为ArrayBuffer并创建bitmap
    const loadPromises = Array.from(imageUrls).map(filePath => {
      return new Promise((resolve, reject) => {
        console.log('开始转换路径到ArrayBuffer:', filePath);

        // 使用外部方法转换路径为ArrayBuffer
        window.convertFilePathToArrayBuffer(filePath, (arrayBuffer) => {
          if (!arrayBuffer) {
            reject(new Error(`无法转换路径: ${filePath}`));
            return;
          }

          try {
            console.log('ArrayBuffer转换成功，大小:', arrayBuffer.byteLength);

            // 创建Blob URL
            const blob = new Blob([arrayBuffer]);
            const blobUrl = URL.createObjectURL(blob);

            // 使用RPG Maker的ImageManager加载bitmap
            const loadedBitmap = window.ImageManager.loadBitmapFromUrl(blobUrl);

            // 设置原始路径
            loadedBitmap._url = filePath;

            loadedBitmap.addLoadListener(function (bitmap) {
              console.log('ArrayBuffer bitmap加载完成:', filePath);
              bitmapMap.set(filePath, bitmap);
              resolve(bitmap);
            });

            // 添加错误处理
            loadedBitmap.addErrorListener && loadedBitmap.addErrorListener(function () {
              console.error('ArrayBuffer bitmap加载失败:', filePath);
              reject(new Error(`Failed to load ArrayBuffer bitmap: ${filePath}`));
            });

            // 如果没有错误监听器，使用超时作为备用
            if (!loadedBitmap.addErrorListener) {
              setTimeout(() => {
                if (!loadedBitmap.isReady()) {
                  console.error('ArrayBuffer bitmap加载超时:', filePath);
                  reject(new Error(`ArrayBuffer bitmap load timeout: ${filePath}`));
                }
              }, 10000); // 10秒超时
            }

          } catch (error) {
            console.error('创建ArrayBuffer bitmap失败:', filePath, error);
            reject(error);
          }
        });
      });
    });

    // 等待所有图片加载完成
    try {
      await Promise.all(loadPromises);
      console.log('所有ArrayBuffer图片加载完成');
    } catch (error) {
      console.warn('部分ArrayBuffer图片加载失败，继续处理:', error);
      // 继续处理，即使部分图片加载失败
    }

    // 计算所需的canvas尺寸
    let maxWidth = canvasWidth;
    let maxHeight = canvasHeight;

    elementsData.forEach(element => {
      if (element.type === 'text') {
        maxWidth = Math.max(maxWidth, element.x + (element.maxWidth || 200));
        maxHeight = Math.max(maxHeight, element.y + (element.lineHeight || 36));
      } else if (element.type === 'image') {
        maxWidth = Math.max(maxWidth, element.dx + element.dw);
        maxHeight = Math.max(maxHeight, element.dy + element.dh);
      }
    });

    console.log('计算出的canvas尺寸:', { width: maxWidth, height: maxHeight });

    // 创建新的bitmap，保存完整的elements信息
    const newBitmap = new window.Bitmap(maxWidth, maxHeight);

    // 设置bitmap的字体属性（从options或默认值）
    newBitmap.fontBold = options.fontBold !== undefined ? options.fontBold : false;
    newBitmap.fontFace = options.fontFace || "sans-serif";
    newBitmap.fontItalic = options.fontItalic !== undefined ? options.fontItalic : false;
    newBitmap.fontSize = options.fontSize || 16;
    newBitmap.outlineColor = options.outlineColor || "rgba(0, 0, 0, 0.5)";
    newBitmap.outlineWidth = options.outlineWidth || 3;
    newBitmap.textColor = options.textColor || "#ffffff";

    console.log('设置bitmap字体属性:', {
      fontBold: newBitmap.fontBold,
      fontFace: newBitmap.fontFace,
      fontItalic: newBitmap.fontItalic,
      fontSize: newBitmap.fontSize,
      outlineColor: newBitmap.outlineColor,
      outlineWidth: newBitmap.outlineWidth,
      textColor: newBitmap.textColor
    });

    // 深拷贝elements数组并重新创建source引用
    newBitmap.elements = elementsData.map(element => {
      const newElement = { ...element };

      if (element.type === 'image' && element.source && element.source._url) {
        const loadedBitmap = bitmapMap.get(element.source._url);
        if (loadedBitmap) {
          // 使用加载完成的bitmap作为source
          newElement.source = loadedBitmap;
        } else {
          // 如果图片加载失败，保留原始source信息
          newElement.source = { ...element.source };
        }
      }

      return newElement;
    });

    // 创建新的sprite
    const newSprite = new window.Sprite(newBitmap);

    // 初始化sprite的状态
    newSprite.x = spriteX;
    newSprite.y = spriteY;
    newSprite.anchor.x = anchorX;
    newSprite.anchor.y = anchorY;
    newSprite.scale.x = scaleX;
    newSprite.scale.y = scaleY;
    newSprite.rotation = rotation;

    // 保存初始化参数到sprite上，方便后续使用
    newSprite._initOptions = {
      spriteX,
      spriteY,
      anchorX,
      anchorY,
      scaleX,
      scaleY,
      rotation,
      canvasWidth: maxWidth,
      canvasHeight: maxHeight
    };

    console.log('elementsToSpriteByBuf转换完成，包含', newBitmap.elements.length, '个元素');
    console.log('sprite初始化参数:', newSprite._initOptions);

    return newSprite;

  } catch (error) {
    console.error('elementsToSpriteByBuf转换失败:', error);
    throw error;
  }
}

export async function elementsToSprite(elementsData, options = {}) {
  const {
    canvasWidth = 816,
    canvasHeight = 624,
    spriteX = 0,
    spriteY = 0,
    anchorX = 0,
    anchorY = 0,
    scaleX = 1,
    scaleY = 1,
    rotation = 0
  } = options;

  console.log('开始转换elements数据到sprite:', elementsData);

  if (!elementsData || !Array.isArray(elementsData)) {
    throw new Error('elementsData必须是一个数组');
  }

  // 检查RPG Maker API是否可用
  if (typeof window.Bitmap === 'undefined' || typeof window.Sprite === 'undefined' || typeof window.ImageManager === 'undefined') {
    throw new Error('RPG Maker API尚未加载');
  }

  try {
    // 收集所有不同的图片URL
    const imageUrls = new Set();
    elementsData.forEach(element => {
      if (element.type === 'image' && element.source && element.source._url) {
        imageUrls.add(element.source._url);
      }
    });

    console.log('需要加载的图片URLs:', Array.from(imageUrls));

    // 创建bitmap映射表
    const bitmapMap = new Map();

    // 使用ImageManager加载所有图片
    const loadPromises = Array.from(imageUrls).map(url => {
      return new Promise((resolve, reject) => {
        console.log('开始加载图片:', url);

        try {
          const bitmap = window.ImageManager.loadBitmapFromUrl(url);

          bitmap.addLoadListener(function (loadedBitmap) {
            console.log('图片加载完成:', url);
            bitmapMap.set(url, loadedBitmap);
            resolve(loadedBitmap);
          });

          // 添加错误处理
          bitmap.addErrorListener && bitmap.addErrorListener(function () {
            console.error('图片加载失败:', url);
            reject(new Error(`Failed to load image: ${url}`));
          });

          // 如果没有错误监听器，使用超时作为备用
          if (!bitmap.addErrorListener) {
            setTimeout(() => {
              if (!bitmap.isReady()) {
                console.error('图片加载超时:', url);
                reject(new Error(`Image load timeout: ${url}`));
              }
            }, 10000); // 10秒超时
          }

        } catch (error) {
          console.error('创建bitmap失败:', url, error);
          reject(error);
        }
      });
    });

    // 等待所有图片加载完成
    try {
      await Promise.all(loadPromises);
      console.log('所有图片加载完成');
    } catch (error) {
      console.warn('部分图片加载失败，继续处理:', error);
      // 继续处理，即使部分图片加载失败
    }

    // 计算所需的canvas尺寸
    let maxWidth = canvasWidth;
    let maxHeight = canvasHeight;

    elementsData.forEach(element => {
      if (element.type === 'text') {
        maxWidth = Math.max(maxWidth, element.x + (element.maxWidth || 200));
        maxHeight = Math.max(maxHeight, element.y + (element.lineHeight || 36));
      } else if (element.type === 'image') {
        maxWidth = Math.max(maxWidth, element.dx + element.dw);
        maxHeight = Math.max(maxHeight, element.dy + element.dh);
      }
    });

    // 不再添加边距，使用计算出的精确尺寸

    console.log('计算出的canvas尺寸:', { width: maxWidth, height: maxHeight });

    // 创建新的bitmap，保存完整的elements信息
    const newBitmap = new window.Bitmap(maxWidth, maxHeight);

    // 设置bitmap的字体属性（从options或默认值）
    newBitmap.fontBold = options.fontBold !== undefined ? options.fontBold : false;
    newBitmap.fontFace = options.fontFace || "sans-serif";
    newBitmap.fontItalic = options.fontItalic !== undefined ? options.fontItalic : false;
    newBitmap.fontSize = options.fontSize || 16;
    newBitmap.outlineColor = options.outlineColor || "rgba(0, 0, 0, 0.5)";
    newBitmap.outlineWidth = options.outlineWidth || 3;
    newBitmap.textColor = options.textColor || "#ffffff";

    console.log('设置bitmap字体属性:', {
      fontBold: newBitmap.fontBold,
      fontFace: newBitmap.fontFace,
      fontItalic: newBitmap.fontItalic,
      fontSize: newBitmap.fontSize,
      outlineColor: newBitmap.outlineColor,
      outlineWidth: newBitmap.outlineWidth,
      textColor: newBitmap.textColor
    });

    // 深拷贝elements数组并重新创建source引用
    newBitmap.elements = elementsData.map(element => {
      const newElement = { ...element };

      if (element.type === 'image' && element.source && element.source._url) {
        const loadedBitmap = bitmapMap.get(element.source._url);
        if (loadedBitmap) {
          // 使用加载完成的bitmap作为source
          newElement.source = loadedBitmap;
        } else {
          // 如果图片加载失败，保留原始source信息
          newElement.source = { ...element.source };
        }
      }

      return newElement;
    });

    // 创建新的sprite
    const newSprite = new window.Sprite(newBitmap);

    // 初始化sprite的状态
    newSprite.x = spriteX;
    newSprite.y = spriteY;
    newSprite.anchor.x = anchorX;
    newSprite.anchor.y = anchorY;
    newSprite.scale.x = scaleX;
    newSprite.scale.y = scaleY;
    newSprite.rotation = rotation;

    // 保存初始化参数到sprite上，方便后续使用
    newSprite._initOptions = {
      spriteX,
      spriteY,
      anchorX,
      anchorY,
      scaleX,
      scaleY,
      rotation,
      canvasWidth: maxWidth,
      canvasHeight: maxHeight
    };

    console.log('sprite创建完成，包含', newBitmap.elements.length, '个元素');
    console.log('sprite初始化参数:', newSprite._initOptions);

    return newSprite;

  } catch (error) {
    console.error('elementsToSprite转换失败:', error);
    throw error;
  }
}



/**
 * 将sprite对象转换为elements数组数据
 * @param {Object} sprite - sprite对象，包含所有需要的属性
 * @returns {Object} 返回包含elements数组和元数据的对象
 */
export function spriteToElements(sprite) {
  console.log('开始转换sprite到elements数据:', sprite);

  if (!sprite || !sprite.bitmap || !sprite.bitmap.elements) {
    throw new Error('无效的sprite对象或缺少elements数组');
  }

  try {
    // 获取bitmap的原始尺寸
    const bitmapWidth = sprite.bitmap.width;
    const bitmapHeight = sprite.bitmap.height;

    console.log('bitmap原始尺寸:', { bitmapWidth, bitmapHeight });

    // 从sprite对象直接获取所有状态信息
    const initOptions = sprite._initOptions || {};

    // 获取sprite的状态信息
    const actualSpriteX = sprite.x !== undefined ? sprite.x : initOptions.spriteX || 0;
    const actualSpriteY = sprite.y !== undefined ? sprite.y : initOptions.spriteY || 0;
    const actualAnchorX = sprite.anchor && sprite.anchor.x !== undefined ? sprite.anchor.x : initOptions.anchorX || 0;
    const actualAnchorY = sprite.anchor && sprite.anchor.y !== undefined ? sprite.anchor.y : initOptions.anchorY || 0;
    const actualScaleX = sprite.scale && sprite.scale.x !== undefined ? sprite.scale.x : initOptions.scaleX || 1;
    const actualScaleY = sprite.scale && sprite.scale.y !== undefined ? sprite.scale.y : initOptions.scaleY || 1;
    const actualRotation = sprite.rotation !== undefined ? sprite.rotation : initOptions.rotation || 0;

    console.log('从sprite获取的状态信息:', {
      actualSpriteX, actualSpriteY, actualAnchorX, actualAnchorY,
      actualScaleX, actualScaleY, actualRotation
    });

    // 深拷贝elements数组，准备导出
    const elementsToExport = sprite.bitmap.elements.map(element => {
      const exportElement = { ...element };

      // 对于图片元素，处理source对象
      if (element.type === 'image' && element.source) {
        // 优先使用原始路径，避免blob URL
        let sourceUrl = element.source._url || element.source.url;

        // 如果是blob URL，尝试获取原始路径
        if (sourceUrl && sourceUrl.startsWith('blob:')) {
          // 检查是否有保存的原始路径
          if (element.source.originalPath) {
            sourceUrl = element.source.originalPath;
          } else if (element.source._originalUrl) {
            sourceUrl = element.source._originalUrl;
          } else {
            console.warn('检测到blob URL但没有原始路径信息:', sourceUrl);
          }
        }

        // 只保存URL和基本信息，移除bitmap对象
        exportElement.source = {
          _url: sourceUrl,
          width: element.source.width,
          height: element.source.height
        };

        // 如果有原始路径信息，也保存下来
        if (element.source.originalPath) {
          exportElement.source.originalPath = element.source.originalPath;
        }
      }

      return exportElement;
    });

    // 获取bitmap的字体属性
    const bitmapFontProps = {
      fontBold: sprite.bitmap.fontBold !== undefined ? sprite.bitmap.fontBold : false,
      fontFace: sprite.bitmap.fontFace || "sans-serif",
      fontItalic: sprite.bitmap.fontItalic !== undefined ? sprite.bitmap.fontItalic : false,
      fontSize: sprite.bitmap.fontSize || 16,
      outlineColor: sprite.bitmap.outlineColor || "rgba(0, 0, 0, 0.5)",
      outlineWidth: sprite.bitmap.outlineWidth || 3,
      textColor: sprite.bitmap.textColor || "#ffffff"
    };

    console.log('从bitmap读取的字体属性:', bitmapFontProps);

    // 为elements数组添加sprite信息，用于往返转换
    const elementsWithSpriteInfo = elementsToExport.slice(); // 复制数组
    elementsWithSpriteInfo.spriteInfo = {
      x: actualSpriteX,
      y: actualSpriteY,
      anchorX: actualAnchorX,
      anchorY: actualAnchorY,
      scaleX: actualScaleX,
      scaleY: actualScaleY,
      rotation: actualRotation,
      canvasWidth: bitmapWidth,
      canvasHeight: bitmapHeight,
      // 添加字体属性
      ...bitmapFontProps
    };

    const result = {
      elements: elementsWithSpriteInfo,
      elementCount: elementsToExport.length,
      textCount: elementsToExport.filter(e => e.type === 'text').length,
      imageCount: elementsToExport.filter(e => e.type === 'image').length,
      canvasSize: {
        width: bitmapWidth,
        height: bitmapHeight
      },
      originalBitmapSize: {
        width: bitmapWidth,
        height: bitmapHeight
      },
      spriteState: {
        x: actualSpriteX,
        y: actualSpriteY,
        anchorX: actualAnchorX,
        anchorY: actualAnchorY,
        scaleX: actualScaleX,
        scaleY: actualScaleY,
        rotation: actualRotation
      },
      scaleInfo: {
        scaleX: actualScaleX,
        scaleY: actualScaleY
      }
    };

    console.log('sprite转换完成:', result);

    return result;

  } catch (error) {
    console.error('spriteToElements转换失败:', error);
    throw error;
  }
}

/**
 * 验证elements数组数据的有效性
 * @param {Array} elementsData - 要验证的elements数组
 * @returns {Object} 验证结果
 */
export function validateElementsData(elementsData) {
  const result = {
    isValid: true,
    errors: [],
    warnings: [],
    statistics: {
      totalElements: 0,
      textElements: 0,
      imageElements: 0,
      invalidElements: 0
    }
  };

  if (!Array.isArray(elementsData)) {
    result.isValid = false;
    result.errors.push('elementsData必须是一个数组');
    return result;
  }

  result.statistics.totalElements = elementsData.length;

  elementsData.forEach((element, index) => {
    if (!element || typeof element !== 'object') {
      result.errors.push(`元素${index}: 必须是一个对象`);
      result.statistics.invalidElements++;
      return;
    }

    if (!element.type) {
      result.errors.push(`元素${index}: 缺少type属性`);
      result.statistics.invalidElements++;
      return;
    }

    if (element.type === 'text') {
      result.statistics.textElements++;

      // 验证文本元素必需属性
      if (typeof element.text !== 'string') {
        result.errors.push(`文本元素${index}: text属性必须是字符串`);
      }
      if (typeof element.x !== 'number') {
        result.errors.push(`文本元素${index}: x属性必须是数字`);
      }
      if (typeof element.y !== 'number') {
        result.errors.push(`文本元素${index}: y属性必须是数字`);
      }

      // 可选属性警告
      if (!element.maxWidth) {
        result.warnings.push(`文本元素${index}: 建议设置maxWidth属性`);
      }
      if (!element.lineHeight) {
        result.warnings.push(`文本元素${index}: 建议设置lineHeight属性`);
      }

    } else if (element.type === 'image') {
      result.statistics.imageElements++;

      // 验证图片元素必需属性
      const requiredProps = ['source', 'sx', 'sy', 'sw', 'sh', 'dx', 'dy', 'dw', 'dh'];
      requiredProps.forEach(prop => {
        if (element[prop] === undefined) {
          result.errors.push(`图片元素${index}: 缺少${prop}属性`);
        }
      });

      // 验证source对象
      if (element.source && !element.source._url) {
        result.errors.push(`图片元素${index}: source对象缺少_url属性`);
      }

    } else {
      result.warnings.push(`元素${index}: 未知的type类型: ${element.type}`);
    }
  });

  if (result.errors.length > 0) {
    result.isValid = false;
  }

  return result;
}

/**
 * 创建默认的文本元素
 * @param {Object} options - 文本元素选项
 * @returns {Object} 文本元素对象
 */
export function createTextElement(options = {}) {
  return {
    type: 'text',
    text: options.text || '新文本',
    x: options.x || 0,
    y: options.y || 0,
    maxWidth: options.maxWidth || 200,
    lineHeight: options.lineHeight || 36,
    align: options.align || 'left',
    ...options
  };
}

/**
 * 创建默认的图片元素
 * @param {Object} options - 图片元素选项
 * @returns {Object} 图片元素对象
 */
export function createImageElement(options = {}) {
  return {
    type: 'image',
    source: options.source || { _url: '', width: 0, height: 0 },
    sx: options.sx || 0,
    sy: options.sy || 0,
    sw: options.sw || 32,
    sh: options.sh || 32,
    dx: options.dx || 0,
    dy: options.dy || 0,
    dw: options.dw || 32,
    dh: options.dh || 32,
    bounds: options.bounds || { x: 0, y: 0, width: 32, height: 32 },
    ...options
  };
}

