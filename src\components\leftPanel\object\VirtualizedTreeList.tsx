import React, { useRef, useState, useEffect, useCallback } from 'react';
import { Box } from '@mui/material';
import { TreeNode } from '../../../utils/tree/TreeNodeTypes';
import TreeNodeComponent from './TreeNodeComponent';

interface VirtualizedTreeListProps {
  nodes: Array<TreeNode & { level: number; isVisible: boolean }>;
  expandedNodes: Set<string>;
  onToggleExpand: (nodeId: string) => void;
  updateTree?: () => void;
  itemHeight?: number;
  overscan?: number;
}

const VirtualizedTreeList: React.FC<VirtualizedTreeListProps> = ({
  nodes,
  expandedNodes,
  onToggleExpand,
  updateTree,
  itemHeight = 36, // 每个节点的高度
  overscan = 5, // 额外渲染的项数（上下各overscan个）
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(0);

  // 过滤出可见节点
  const visibleNodes = nodes.filter(node => node.isVisible);

  // 计算总内容高度
  const totalHeight = visibleNodes.length * itemHeight;

  // 计算可见范围内的起始和结束索引
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    visibleNodes.length,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  // 获取可见范围内的节点
  const visibleItems = visibleNodes.slice(startIndex, endIndex);

  // 处理滚动事件
  const handleScroll = useCallback(() => {
    if (containerRef.current) {
      setScrollTop(containerRef.current.scrollTop);
    }
  }, []);

  // 监听容器大小变化
  useEffect(() => {
    if (containerRef.current) {
      setContainerHeight(containerRef.current.clientHeight);

      // 创建 ResizeObserver 监听容器大小变化
      const resizeObserver = new ResizeObserver(entries => {
        for (const entry of entries) {
          if (entry.target === containerRef.current) {
            setContainerHeight(entry.contentRect.height);
          }
        }
      });

      resizeObserver.observe(containerRef.current);

      return () => {
        if (containerRef.current) {
          resizeObserver.unobserve(containerRef.current);
        }
      };
    }
  }, []);

  return (
    <Box
      ref={containerRef}
      sx={{
        height: '100%',
        overflow: 'auto',
        position: 'relative',
      }}
      onScroll={handleScroll}
    >
      {/* 创建一个占位容器，高度等于所有项的总高度 */}
      <Box sx={{ height: totalHeight, position: 'relative' }}>
        {/* 只渲染可见范围内的项 */}
        <Box
          sx={{
            position: 'absolute',
            top: startIndex * itemHeight,
            left: 0,
            right: 0,
          }}
        >
          {visibleItems.map(node => (
            <TreeNodeComponent
              key={node.id}
              node={node}
              level={node.level}
              expandedNodes={expandedNodes}
              onToggleExpand={onToggleExpand}
              updateTree={updateTree}
            />
          ))}
        </Box>
      </Box>
    </Box>
  );
};

export default React.memo(VirtualizedTreeList);
