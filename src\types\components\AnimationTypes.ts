// 动画属性类型
export interface AnimationProperty {
  property: string;
  value: number;
  relative: boolean;
}

// 动画播放模式
export type AnimationMode = 'once' | 'loop' | 'yoyo';

// 动画栈播放模式
export type AnimationPlayMode = 'once' | 'loop' | 'yoyo' | 'remove';

// 动画栈项类型
export interface AnimationStackItem {
  id: string;
  name: string;
  properties: AnimationProperty[];
  duration: number;
  easing: string;
  targetType: 'self' | 'children';
  playMode: AnimationPlayMode;
  playCount: number; // -1 表示无限循环
  childrenDelay: number; // 子元素动画开始的时间间隔（毫秒）
  active: boolean;
  animation?: any;
  animationStack?: any;
}

// 动画栈配置类型
export interface AnimationStackConfig {
  playMode: AnimationPlayMode; // 播放模式: 单次播放(once), 循环播放(loop), 来回播放(yoyo), 播放后删除(remove)
  playCount: number; // 播放次数，-1表示无限循环
}
