/**
 * 代码生成器核心类型定义
 */

// ==================== 操作相关类型 ====================

/**
 * 操作类型枚举
 */
export enum OperationType {
  CREATE = 'CREATE',
  DELETE = 'DELETE',
  MODIFY = 'MODIFY'
}

/**
 * 操作模式枚举
 */
export enum OperationMode {
  OBJECT = 'OBJECT',    // 对象实例操作
  TYPE = 'TYPE'         // 类型原型操作
}

/**
 * 基础操作信息接口
 */
export interface BaseOperationInfo {
  id: string;                    // 操作唯一标识
  operationType: OperationType;  // 操作类型
  operationMode: OperationMode;  // 操作模式
  timestamp: number;             // 操作时间戳
  targetObject: any;             // 目标对象
  objectPath: string[];          // 对象路径
  className: string;             // 类名
}

/**
 * 属性修改操作信息
 */
export interface PropertyModificationInfo extends BaseOperationInfo {
  operationType: OperationType.MODIFY;
  propertyName: string;          // 属性名
  oldValue: any;                 // 旧值
  newValue: any;                 // 新值
  propertyType?: string;         // 属性类型（basic, sprite, container等）
}

/**
 * 对象创建操作信息
 */
export interface ObjectCreationInfo extends BaseOperationInfo {
  operationType: OperationType.CREATE;
  objectType: string;            // 要创建的对象类型
  parentPath: string[];          // 父对象路径
  initialProperties: Record<string, any>; // 初始属性
  objectName?: string;           // 对象名称
}

/**
 * 对象删除操作信息
 */
export interface ObjectDeletionInfo extends BaseOperationInfo {
  operationType: OperationType.DELETE;
  targetPath: string[];          // 要删除的对象路径
}

/**
 * 联合操作信息类型
 */
export type OperationInfo = PropertyModificationInfo | ObjectCreationInfo | ObjectDeletionInfo;

// ==================== 代码生成相关类型 ====================

/**
 * 生成的代码片段
 */
export interface CodeSnippet {
  code: string;                  // 代码内容
  description?: string;          // 代码描述
  dependencies?: string[];       // 依赖项
  metadata?: {                   // 元数据，用于代码优化
    objectPath?: string;         // 对象路径
    className?: string;          // 类名
    variableName?: string;       // 变量名
    [key: string]: any;          // 其他元数据
  };
}

/**
 * 场景修改代码
 */
export interface SceneModificationCode {
  sceneName: string;             // 场景名称
  modifications: CodeSnippet[];  // 修改代码片段
}

/**
 * 原型修改代码
 */
export interface PrototypeModificationCode {
  className: string;             // 类名
  modifications: CodeSnippet[];  // 修改代码片段
}

/**
 * 生成的完整代码结构
 */
export interface GeneratedCode {
  sceneModifications: SceneModificationCode[];      // 场景修改
  prototypeModifications: PrototypeModificationCode[]; // 原型修改
  metadata: {
    generatedAt: number;         // 生成时间
    operationCount: number;      // 操作数量
    version: string;             // 版本号
  };
}

// ==================== 生成器配置类型 ====================

/**
 * 代码生成器配置
 */
export interface GeneratorConfig {
  debug: boolean;                // 是否启用调试模式
  minify: boolean;              // 是否压缩代码
  addComments: boolean;         // 是否添加注释
  variablePrefix: string;       // 变量前缀
  indentSize: number;           // 缩进大小
}

/**
 * 属性生成器配置
 */
export interface PropertyGeneratorConfig extends GeneratorConfig {
  handleSpecialProperties: boolean;  // 是否处理特殊属性
  validateValues: boolean;           // 是否验证值
}

/**
 * 对象生成器配置
 */
export interface ObjectGeneratorConfig extends GeneratorConfig {
  addTypeMarkers: boolean;           // 是否添加类型标记
  generateUniqueNames: boolean;      // 是否生成唯一名称
}

// ==================== 上下文类型 ====================

/**
 * 生成上下文
 */
export interface GenerationContext {
  config: GeneratorConfig;       // 生成器配置
  currentScene?: string;         // 当前场景
  currentClass?: string;         // 当前类
  variableCounter: number;       // 变量计数器
  generatedVariables: Map<string, string>; // 已生成的变量映射
}

// ==================== 错误类型 ====================

/**
 * 代码生成错误
 */
export class CodeGenerationError extends Error {
  constructor(
    message: string,
    public operation?: OperationInfo,
    public context?: GenerationContext
  ) {
    super(message);
    this.name = 'CodeGenerationError';
  }
}

/**
 * 属性不支持错误
 */
export class PropertyNotSupportedError extends CodeGenerationError {
  constructor(
    public propertyName: string,
    public objectType: string
  ) {
    super(`属性 ${propertyName} 在 ${objectType} 类型中不支持`);
    this.name = 'PropertyNotSupportedError';
  }
}

/**
 * 对象类型不支持错误
 */
export class ObjectTypeNotSupportedError extends CodeGenerationError {
  constructor(
    public objectType: string
  ) {
    super(`对象类型 ${objectType} 不支持`);
    this.name = 'ObjectTypeNotSupportedError';
  }
}
