// 全局类型定义

interface Window {

  RPGMakerMZTypes?: {
    constructors: Record<string, any>;
    customTypes: Array<{
      name: string;
      parent: string;
      description?: string;
      createdAt?: string;
    }>;
    customObjects: Array<{
      id: string;
      typeName: string;
      name: string;
      params: any;
      createdAt: string;
    }>;
    coreTypes: Record<string, any>;
    utils: {
      getTypeDefinition: (typeName: string) => any;
      addCustomType: (typeName: string, parentType: string, description?: string) => boolean;
      getAllTypeNames: () => string[];
      deleteCustomType: (typeName: string) => boolean;
      getAllCustomTypes: () => Array<{
        name: string;
        parent: string;
        description?: string;
      }>;
      getAllCustomObjects: () => Array<{
        id: string;
        typeName: string;
        name: string;
        params: any;
        createdAt: string;
      }>;
      createConstructor: (typeName: string, parentTypeName: string) => boolean;
      createInstance: (typeName: string, params?: any) => any;
      initConstructors: () => void;
      saveCustomData: () => boolean;
      loadCustomData: () => boolean;
      restoreCustomTypes: (types: any[]) => void;
      restoreCustomObjects: (objects: any[]) => void;
      setupAutoSave: () => void;
    };
  };

  SceneManager: {
    _scene: any;
  };
  AnimationManager: {
    animate: (targets: any, properties: any, duration?: number, easing?: string, options?: any) => any;
    sequence: (targets: any, propertiesArray: any[], duration?: number, easing?: string, options?: any) => any[];
    update: () => void;
    stopAll: () => void;
    stopByTarget: (target: any) => void;
  };
  extendWithAnimationMethods: (obj: any) => void;
  extendContainerWithAnimationMethods: (container: any) => void;
}
