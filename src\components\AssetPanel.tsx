import { useState, useEffect, useCallback } from "react";
import {
  Paper,
  Typography,
  Box,
  Breadcrumbs,
  Link,
  Card,
  CardContent,
  IconButton,
  CircularProgress,
  Tooltip,
  Menu,
  MenuItem,
  Divider
} from "@mui/material";
// 图标已移至各个拖拽组件中
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import RefreshIcon from '@mui/icons-material/Refresh';
import { invoke } from '@tauri-apps/api/core';
import {
  DndContext,
  DragOverlay,
  DragStartEvent,
  DragEndEvent,
  PointerSensor,
  useSensor,
  useSensors
} from '@dnd-kit/core';
import useProjectStore, { AssetInfo, AssetType } from '../store/Store';
import DraggableBase from './drag/DraggableBase';
// 导入图标
import ImageIcon from '@mui/icons-material/Image';
import AudioFileIcon from '@mui/icons-material/AudioFile';
import TextSnippetIcon from '@mui/icons-material/TextSnippet';
import FontDownloadIcon from '@mui/icons-material/FontDownload';
import VideoFileIcon from '@mui/icons-material/VideoFile';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import FolderIcon from '@mui/icons-material/Folder';

const AssetPanel = () => {
  // 从 Zustand store 获取状态和方法
  const projectName = useProjectStore(state => state.projectName);
  const assets = useProjectStore(state => state.assets);
  const currentAssetPath = useProjectStore(state => state.currentAssetPath);
  const selectedAsset = useProjectStore(state => state.selectedAsset);
  const loadingAssets = useProjectStore(state => state.loadingAssets);
  const setAssets = useProjectStore(state => state.setAssets);
  const setCurrentAssetPath = useProjectStore(state => state.setCurrentAssetPath);
  const setSelectedAsset = useProjectStore(state => state.setSelectedAsset);
  const setLoadingAssets = useProjectStore(state => state.setLoadingAssets);

  // 本地状态
  const [error, setError] = useState<string | null>(null);
  const [breadcrumbs, setBreadcrumbs] = useState<{ name: string; path: string }[]>([]);
  const [contextMenu, setContextMenu] = useState<{ mouseX: number; mouseY: number; asset: AssetInfo | null } | null>(null);

  // 拖拽状态
  const [activeAsset, setActiveAsset] = useState<AssetInfo | null>(null);

  // 配置拖拽传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      // 配置拖拽激活约束，防止误触发
      activationConstraint: {
        distance: 5, // 至少移动 5px 才激活拖拽
      },
    })
  );

  // 加载项目资源
  const loadProjectAssets = useCallback(async () => {
    if (!projectName) return;

    setLoadingAssets(true);
    setError(null);

    try {
      const result = await invoke<AssetInfo[]>('get_project_assets', { projectName });
      console.log('获取项目资源成功:', result);
      setAssets(result);
      setCurrentAssetPath('');
      setBreadcrumbs([{ name: '根目录', path: '' }]);
    } catch (err) {
      console.error('获取项目资源失败:', err);
      setError(`获取项目资源失败: ${err}`);
    } finally {
      setLoadingAssets(false);
    }
  }, [projectName, setAssets, setCurrentAssetPath, setLoadingAssets]);

  // 加载目录资源
  const loadDirectoryAssets = useCallback(async (directoryPath: string) => {
    if (!projectName) return;

    setLoadingAssets(true);
    setError(null);

    try {
      const result = await invoke<AssetInfo[]>('get_directory_assets', {
        projectName,
        directoryPath
      });
      console.log('获取目录资源成功:', result);
      setAssets(result);
      setCurrentAssetPath(directoryPath);

      // 更新面包屑
      const pathParts = directoryPath.split('/').filter(Boolean);
      const newBreadcrumbs = [{ name: '根目录', path: '' }];
      let currentPath = '';

      for (const part of pathParts) {
        currentPath = currentPath ? `${currentPath}/${part}` : part;
        newBreadcrumbs.push({ name: part, path: currentPath });
      }

      setBreadcrumbs(newBreadcrumbs);
    } catch (err) {
      console.error('获取目录资源失败:', err);
      setError(`获取目录资源失败: ${err}`);
    } finally {
      setLoadingAssets(false);
    }
  }, [projectName, setAssets, setCurrentAssetPath, setLoadingAssets]);

  // 初始加载
  useEffect(() => {
    if (projectName) {
      loadProjectAssets();
    }
  }, [projectName, loadProjectAssets]);

  // 处理目录点击
  const handleDirectoryClick = (asset: AssetInfo) => {
    if (asset.is_directory) {
      loadDirectoryAssets(asset.game_path);
    } else {
      setSelectedAsset(asset);
    }
  };

  // 处理面包屑点击
  const handleBreadcrumbClick = (path: string) => {
    if (path === '') {
      loadProjectAssets();
    } else {
      loadDirectoryAssets(path);
    }
  };

  // 处理刷新按钮点击
  const handleRefresh = () => {
    if (currentAssetPath === '') {
      loadProjectAssets();
    } else {
      loadDirectoryAssets(currentAssetPath);
    }
  };

  // 处理返回按钮点击
  const handleGoBack = () => {
    if (breadcrumbs.length <= 1) {
      return;
    }

    const previousBreadcrumb = breadcrumbs[breadcrumbs.length - 2];
    handleBreadcrumbClick(previousBreadcrumb.path);
  };

  // 处理右键菜单打开
  const handleContextMenu = (event: React.MouseEvent, asset: AssetInfo) => {
    event.preventDefault();
    setContextMenu({
      mouseX: event.clientX,
      mouseY: event.clientY,
      asset
    });
  };

  // 处理右键菜单关闭
  const handleContextMenuClose = () => {
    setContextMenu(null);
  };

  // 处理拖拽开始
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const assetId = active.id.toString();

    // 从 ID 中提取资源路径
    // 格式: image-/path/to/asset.png 或 audio-/path/to/audio.ogg
    const parts = assetId.split('-');
    if (parts.length >= 2) {
      const type = parts[0]; // image, audio, etc.
      const path = parts.slice(1).join('-'); // 重新组合路径部分

      // 查找匹配的资源
      const asset = assets.find(a => a.absolute_path === path);
      if (asset) {
        // 设置活动资源，用于 DragOverlay
        setActiveAsset(asset);
        console.log('拖拽开始:', type, path, asset);
      }
    }
  };

  // 处理拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over) {
      console.log('拖拽结束:', active.id, '放置到', over.id);
    }

    // 清除活动资源
    setActiveAsset(null);
  };

  // 渲染资源预览
  const renderAssetPreview = (asset: AssetInfo) => {
    const isSelected = selectedAsset && selectedAsset.absolute_path === asset.absolute_path ? true : false;

    // 根据资源类型生成不同的UI内容
    const getAssetContent = () => {
      // 如果是目录，返回目录样式（不可拖动）
      if (asset.is_directory) {
        return (
          <Card
            sx={{
              height: 120,
              display: 'flex',
              flexDirection: 'column',
              cursor: 'pointer',
              border: isSelected ? '2px solid #1976d2' : '1px solid #e0e0e0',
              borderRadius: 1,
              overflow: 'hidden',
              '&:hover': {
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
              },
            }}
            onClick={() => handleDirectoryClick(asset)}
            onContextMenu={(e: React.MouseEvent) => handleContextMenu(e, asset)}
          >
            <Box sx={{
              height: 80,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              p: 1,
              backgroundColor: 'rgba(33, 150, 243, 0.05)'
            }}>
              <FolderIcon sx={{ fontSize: 40, color: 'primary.main' }} />
            </Box>
            <CardContent sx={{ p: 1, textAlign: 'center' }}>
              <Typography noWrap variant="body2">{asset.name}</Typography>
            </CardContent>
          </Card>
        );
      }

      // 根据资源类型返回相应的UI
      switch (asset.asset_type) {
        case AssetType.Image:
          return (
            <Card
              sx={{
                height: 120,
                display: 'flex',
                flexDirection: 'column',
                cursor: 'grab',
                border: isSelected ? '2px solid #1976d2' : 'none',
                '&:hover': {
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
                },
              }}
              onClick={() => setSelectedAsset(asset)}
              onContextMenu={(e: React.MouseEvent) => handleContextMenu(e, asset)}
            >
              <Box sx={{
                height: 80,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                overflow: 'hidden',
                p: 1
              }}>
                {asset.display_path ? (
                  <img
                    src={asset.display_path}
                    alt={asset.name}
                    style={{
                      maxWidth: '100%',
                      maxHeight: '100%',
                      objectFit: 'contain'
                    }}
                  />
                ) : (
                  <ImageIcon sx={{ fontSize: 40, color: 'primary.main' }} />
                )}
              </Box>
              <CardContent sx={{ p: 1, textAlign: 'center' }}>
                <Typography noWrap variant="body2">{asset.name}</Typography>
              </CardContent>
            </Card>
          );

        case AssetType.Audio:
          return (
            <Card
              sx={{
                height: 120,
                display: 'flex',
                flexDirection: 'column',
                cursor: 'grab',
                border: isSelected ? '2px solid #1976d2' : '1px solid #e0e0e0',
                borderRadius: 1,
                overflow: 'hidden',
                '&:hover': {
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
                },
              }}
              onClick={() => setSelectedAsset(asset)}
              onContextMenu={(e: React.MouseEvent) => handleContextMenu(e, asset)}
            >
              <Box sx={{
                height: 80,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                p: 1,
                backgroundColor: 'rgba(227, 242, 253, 0.2)'
              }}>
                <AudioFileIcon sx={{ fontSize: 40, color: '#1976d2' }} />
              </Box>
              <CardContent sx={{ p: 1, textAlign: 'center' }}>
                <Typography noWrap variant="body2">{asset.name}</Typography>
              </CardContent>
            </Card>
          );

        case AssetType.Data:
          return (
            <Card
              sx={{
                height: 120,
                display: 'flex',
                flexDirection: 'column',
                cursor: 'grab',
                border: isSelected ? '2px solid #1976d2' : '1px solid #e0e0e0',
                borderRadius: 1,
                overflow: 'hidden',
                '&:hover': {
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
                },
              }}
              onClick={() => setSelectedAsset(asset)}
              onContextMenu={(e: React.MouseEvent) => handleContextMenu(e, asset)}
            >
              <Box sx={{
                height: 80,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                p: 1,
                backgroundColor: 'rgba(255, 243, 224, 0.2)'
              }}>
                <TextSnippetIcon sx={{ fontSize: 40, color: '#ff9800' }} />
              </Box>
              <CardContent sx={{ p: 1, textAlign: 'center' }}>
                <Typography noWrap variant="body2">{asset.name}</Typography>
              </CardContent>
            </Card>
          );

        case AssetType.Font:
          return (
            <Card
              sx={{
                height: 120,
                display: 'flex',
                flexDirection: 'column',
                cursor: 'grab',
                border: isSelected ? '2px solid #1976d2' : '1px solid #e0e0e0',
                borderRadius: 1,
                overflow: 'hidden',
                '&:hover': {
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
                },
              }}
              onClick={() => setSelectedAsset(asset)}
              onContextMenu={(e: React.MouseEvent) => handleContextMenu(e, asset)}
            >
              <Box sx={{
                height: 80,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                p: 1,
                backgroundColor: 'rgba(232, 245, 233, 0.2)'
              }}>
                <FontDownloadIcon sx={{ fontSize: 40, color: '#4caf50' }} />
              </Box>
              <CardContent sx={{ p: 1, textAlign: 'center' }}>
                <Typography noWrap variant="body2">{asset.name}</Typography>
              </CardContent>
            </Card>
          );

        default:
          return (
            <Card
              sx={{
                height: 120,
                display: 'flex',
                flexDirection: 'column',
                cursor: 'grab',
                border: isSelected ? '2px solid #1976d2' : '1px solid #e0e0e0',
                borderRadius: 1,
                overflow: 'hidden',
                '&:hover': {
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
                },
              }}
              onClick={() => setSelectedAsset(asset)}
              onContextMenu={(e: React.MouseEvent) => handleContextMenu(e, asset)}
            >
              <Box sx={{
                height: 80,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                p: 1
              }}>
                {asset.asset_type === AssetType.Video ? (
                  <VideoFileIcon sx={{ fontSize: 40 }} />
                ) : (
                  <InsertDriveFileIcon sx={{ fontSize: 40 }} />
                )}
              </Box>
              <CardContent sx={{ p: 1, textAlign: 'center' }}>
                <Typography noWrap variant="body2">{asset.name}</Typography>
              </CardContent>
            </Card>
          );
      }
    };

    // 如果是目录，直接返回内容（不可拖动）
    if (asset.is_directory) {
      return getAssetContent();
    }

    // 如果是文件，使用DraggableBase包装
    return (
      <DraggableBase
        id={`${asset.asset_type}-${asset.absolute_path}`}
        data={{
          type: asset.asset_type,
          asset: asset,
          path: asset.game_path,
          name: asset.name,
        }}
      >
        {getAssetContent()}
      </DraggableBase>
    );
  };

  return (
    <Paper
      elevation={3}
      sx={{
        height: "100%",
        overflow: "auto",
        display: "flex",
        flexDirection: "column"
      }}
    >
      <Box sx={{
        p: 1,
        display: 'flex',
        alignItems: 'center',
        borderBottom: 1,
        borderColor: 'divider'
      }}>
        <Typography variant="h6" sx={{ flexGrow: 1 }}>资源面板</Typography>
        <Tooltip title="刷新">
          <IconButton onClick={handleRefresh} size="small">
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      <Box sx={{ p: 1, display: 'flex', alignItems: 'center', borderBottom: 1, borderColor: 'divider' }}>
        <IconButton
          onClick={handleGoBack}
          size="small"
          disabled={breadcrumbs.length <= 1}
        >
          <ArrowBackIcon />
        </IconButton>
        <Breadcrumbs sx={{ ml: 1, flex: 1, overflow: 'hidden' }}>
          {breadcrumbs.map((crumb, index) => (
            <Link
              key={index}
              component="button"
              variant="body2"
              onClick={() => handleBreadcrumbClick(crumb.path)}
              sx={{
                cursor: 'pointer',
                textDecoration: 'none',
                color: index === breadcrumbs.length - 1 ? 'text.primary' : 'primary.main'
              }}
            >
              {crumb.name}
            </Link>
          ))}
        </Breadcrumbs>
      </Box>

      <Box sx={{ p: 1, flex: 1, overflow: 'auto' }}>
        {loadingAssets ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Typography color="error" sx={{ p: 2 }}>{error}</Typography>
        ) : assets.length === 0 ? (
          <Typography sx={{ p: 2, textAlign: 'center' }}>此目录为空</Typography>
        ) : (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
            {assets.map((asset, index) => (
              <Box
                key={index}
                sx={{
                  width: {
                    xs: 'calc(50% - 8px)',
                    sm: 'calc(33.33% - 10.67px)',
                    md: 'calc(25% - 12px)',
                    lg: 'calc(20% - 12.8px)'
                  }
                }}
              >
                {renderAssetPreview(asset)}
              </Box>
            ))}
          </Box>
        )}
      </Box>

      {/* 右键菜单 */}
      <Menu
        open={contextMenu !== null}
        onClose={handleContextMenuClose}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        <MenuItem onClick={handleContextMenuClose}>
          查看详情
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleContextMenuClose}>
          复制路径
        </MenuItem>
      </Menu>
    </Paper>
  );
};

export default AssetPanel;
