import React, { useState, useCallback, useEffect, useRef } from "react";
import { Box, TextField, Typography, IconButton } from "@mui/material";
import EditIcon from '@mui/icons-material/Edit';
import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';

interface TextInputProps {
  label?: string;
  value: string;
  onChange?: (newValue: string, oldValue?: string) => void;
  multiline?: boolean;
  rows?: number;
  width?: string | number;
  height?: string | number;
  placeholder?: string;
  disabled?: boolean;
  object?: any;
  propertyName?: string;
}

/**
 * 文本输入组件，支持编辑模式和显示模式切换
 */
const TextLabel: React.FC<TextInputProps> = ({
  label = "文本",
  value,
  onChange,
  multiline = false,
  rows = 1,
  width = "100%",
  height = "auto",
  placeholder = "输入文本...",
  disabled = false,
  object,
  propertyName
}) => {
  // 编辑模式状态
  const [isEditing, setIsEditing] = useState(false);
  // 临时存储编辑中的值
  const [tempValue, setTempValue] = useState(value);
  // 存储上一次的值，用于撤销
  const prevValueRef = useRef(value);
  // 输入框引用
  const inputRef = useRef<HTMLInputElement>(null);

  // 当外部 value 变化时，更新临时值
  useEffect(() => {
    setTempValue(value);
    prevValueRef.current = value;
  }, [value]);

  // 进入编辑模式
  const handleEdit = useCallback(() => {
    if (disabled) return;
    setIsEditing(true);
    // 保存当前值作为上一次的值
    prevValueRef.current = value;
  }, [disabled, value]);

  // 确认编辑
  const handleConfirm = useCallback(() => {
    setIsEditing(false);
    // 如果值有变化且有 onChange 回调，则调用
    if (tempValue !== prevValueRef.current && onChange) {
      onChange(tempValue, prevValueRef.current);
    }
  }, [tempValue, onChange]);

  // 取消编辑
  const handleCancel = useCallback(() => {
    setIsEditing(false);
    // 恢复为原始值
    setTempValue(prevValueRef.current);
  }, []);

  // 处理输入变化
  const handleChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setTempValue(event.target.value);
  }, []);

  // 处理按键事件
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !multiline) {
      // 回车键确认（非多行模式）
      e.preventDefault();
      handleConfirm();
    } else if (e.key === 'Escape') {
      // ESC 键取消
      e.preventDefault();
      handleCancel();
    }
  }, [handleConfirm, handleCancel, multiline]);

  // 当进入编辑模式时，自动聚焦输入框
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      // 选中所有文本
      inputRef.current.select();
    }
  }, [isEditing]);

  return (
    <Box sx={{
      width,
      height,
      position: 'relative',
      mb: 1,
      border: isEditing ? '1px solid #2196f3' : '1px solid transparent',
      borderRadius: 1,
      transition: 'all 0.2s',
      '&:hover': {
        backgroundColor: disabled ? 'transparent' : 'rgba(0, 0, 0, 0.04)'
      }
    }}>
      {isEditing ? (
        // 编辑模式
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <TextField
            inputRef={inputRef}
            label={label}
            value={tempValue}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            variant="outlined"
            size="small"
            fullWidth
            multiline={multiline}
            rows={rows}
            placeholder={placeholder}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 1,
                fontSize: '0.875rem'
              }
            }}
          />
          <Box sx={{ display: 'flex', flexDirection: 'column', ml: 0.5 }}>
            <IconButton
              size="small"
              onClick={handleConfirm}
              sx={{ mb: 0.5, color: 'success.main' }}
            >
              <CheckIcon fontSize="small" />
            </IconButton>
            <IconButton
              size="small"
              onClick={handleCancel}
              sx={{ color: 'error.main' }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
        </Box>
      ) : (
        // 显示模式
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            cursor: disabled ? 'default' : 'pointer',
            p: 1,
            borderRadius: 1,
            minHeight: '32px',
            opacity: disabled ? 0.7 : 1
          }}
          onClick={handleEdit}
        >
          <Typography
            variant="body2"
            sx={{
              flex: 1,
              whiteSpace: multiline ? 'pre-wrap' : 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }}
          >
            {label && (
              <span style={{ fontWeight: 'bold', marginRight: '8px' }}>{label}:</span>
            )}
            {value || <span style={{ color: '#aaa' }}>{placeholder}</span>}
          </Typography>
          {!disabled && (
            <IconButton
              size="small"
              sx={{
                ml: 1,
                opacity: 0.5,
                '&:hover': {
                  opacity: 1
                }
              }}
            >
              <EditIcon fontSize="small" />
            </IconButton>
          )}
        </Box>
      )}
    </Box>
  );
};

export default TextLabel;
