{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window", "windows": ["main"], "permissions": ["core:default", "opener:default", "fs:default", {"identifier": "fs:allow-exists", "allow": [{"path": "$APPDATA/*"}, {"path": "$PUBLIC/**"}, {"path": "$RESOURCE/**"}, {"path": "$APP/**"}]}, {"identifier": "fs:allow-read", "allow": [{"path": "$PUBLIC/**"}, {"path": "$RESOURCE/**"}, {"path": "$APP/**"}]}, {"identifier": "fs:allow-write", "allow": [{"path": "$PUBLIC/**"}, {"path": "$RESOURCE/**"}, {"path": "$APP/**"}]}, "dialog:default", "core:path:default"]}