import React, { useState } from 'react';
import { Box, Tabs, Tab } from '@mui/material';
import { Resizable } from 're-resizable';
import AssetPanel from './AssetPanel';
import HistoryPanel from './HistoryPanel';

interface BottomPanelProps {
  // 可以添加其他属性
}

const BottomPanel: React.FC<BottomPanelProps> = () => {
  const [tabIndex, setTabIndex] = useState(0);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabIndex(newValue);
  };

  return (
    <Resizable
      defaultSize={{ width: '100%', height: '30%' }}
      minHeight="20%"
      maxHeight="50%"
      enable={{ top: true }}
      style={{ display: 'flex' }}
    >
      <Box sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabIndex} onChange={handleTabChange}>
            <Tab label="资源" />
            <Tab label="历史记录" />
          </Tabs>
        </Box>

        <Box sx={{ flex: 1, overflow: 'auto', display: tabIndex === 0 ? 'block' : 'none' }}>
          <AssetPanel />
        </Box>

        <Box sx={{ flex: 1, overflow: 'auto', display: tabIndex === 1 ? 'block' : 'none' }}>
          <HistoryPanel />
        </Box>
      </Box>
    </Resizable>
  );
};

export default BottomPanel;
