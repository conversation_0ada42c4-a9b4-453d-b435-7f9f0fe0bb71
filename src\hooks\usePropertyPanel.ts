import { useState, useEffect, useReducer, useCallback } from 'react';
import useProjectStore from '../store/Store';
import { getObjectTypeName } from '../utils/object/ObjectFinder';

/**
 * PropertyPanel业务逻辑Hook
 * 处理属性面板的状态管理和业务逻辑
 */
export const usePropertyPanel = () => {
  // 获取全局状态
  const selectedObjectsState = useProjectStore((state) => state.selectedObjects);
  const currentProject = useProjectStore((state) => state.currentProject);
  const on = useProjectStore((state) => state.on);
  const off = useProjectStore((state) => state.off);

  // 本地状态
  const [typeStats, setTypeStats] = useState<{ [key: string]: number }>({});
  const [spriteEditorOpen, setSpriteEditorOpen] = useState(false);
  
  // 强制重新渲染
  const [, forceUpdate] = useReducer((x) => x + 1, 0);

  // 计算派生状态
  const selectedObject = selectedObjectsState.objects.length > 0 
    ? selectedObjectsState.objects[0] as any 
    : null;
  
  const isMultiSelectMode = selectedObjectsState.selectionType === "multiple";
  const isClassSelected = selectedObjectsState.type === "class";
  
  // 检查是否应该显示Sprite编辑器按钮
  const shouldShowSpriteEditor = selectedObject &&
    selectedObject._bitmap &&
    Array.isArray(selectedObject._bitmap.elements);

  // 统计多选对象的类型
  useEffect(() => {
    if (selectedObjectsState.selectionType !== "multiple") {
      setTypeStats({});
      return;
    }

    const stats: { [key: string]: number } = {};
    selectedObjectsState.objects.forEach((obj) => {
      const typeName = getObjectTypeName(obj);
      stats[typeName] = (stats[typeName] || 0) + 1;
    });

    setTypeStats(stats);
  }, [selectedObjectsState]);

  // 监听属性变化事件
  useEffect(() => {
    const handlePropertyChanged = () => forceUpdate();
    const handleUndo = () => forceUpdate();
    const handleRedo = () => forceUpdate();

    // 添加事件监听器
    window.addEventListener("property-changed", handlePropertyChanged as EventListener);
    on("undo-operation", handleUndo);
    on("redo-operation", handleRedo);

    // 清理函数
    return () => {
      window.removeEventListener("property-changed", handlePropertyChanged as EventListener);
      off("undo-operation", handleUndo);
      off("redo-operation", handleRedo);
    };
  }, [selectedObject, on, off]);

  // 处理Sprite编辑器应用更改
  const handleSpriteEditorApply = useCallback((result: any) => {
    if (!selectedObject || !selectedObject._bitmap) return;

    // 如果有编辑结果，更新bitmap的elements数组
    if (result && result.elements) {
      selectedObject._bitmap.elements = result.elements;
    }

    // 重绘
    if (selectedObject._bitmap.redrawAll) {
      selectedObject._bitmap.redrawAll();
    }

    // 关闭对话框
    setSpriteEditorOpen(false);

    // 强制更新
    forceUpdate();
  }, [selectedObject]);

  // 获取项目资源路径
  const getProjectResourcePath = useCallback(() => {
    if (!currentProject?.name) return '';
    
    const basePath = (window as any).mzl ? `../projects/${currentProject.name}` : '';
    return basePath;
  }, [currentProject?.name]);

  return {
    // 状态
    selectedObject,
    selectedObjectsState,
    isMultiSelectMode,
    isClassSelected,
    shouldShowSpriteEditor,
    typeStats,
    spriteEditorOpen,
    
    // 方法
    setSpriteEditorOpen,
    handleSpriteEditorApply,
    getProjectResourcePath,
    forceUpdate,
  };
};
