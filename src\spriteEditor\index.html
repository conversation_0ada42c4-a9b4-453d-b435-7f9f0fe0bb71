<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Sprite Editor</title>
  <!-- <script src="https://unpkg.com/react-scan/dist/auto.global.js"></script> -->

  <!-- 预先定义对外暴露的方法，确保它们在组件渲染前就可用 -->
  <script>
    // 初始化 SpriteEditor 全局对象
    window.SpriteEditor = {
      currentSprite: null,
      externalResourcePath: null,
      editResult: null
    };


    // window.SpriteEditor.setBasePath = (path) => {
    //   console.log('预定义的setBasePath被调用，path:', path);
    //   window.SpriteEditor.basePath = path;
    // };
    // window.SpriteEditor.setBasePath = 'src/spriteEditor/';

    // 预先定义对外暴露的方法，确保它们在组件渲染前就可用
    window.SpriteEditor.setExternalSpriteForEdit = (sprite) => {
      console.log('预定义的setExternalSpriteForEdit被调用，sprite:', sprite);
      window.SpriteEditor.currentSprite = sprite;

      // 如果React应用已经加载，通知它更新
      if (window.SpriteEditor.updateSprite) {
        window.SpriteEditor.updateSprite(sprite);
      }
    };

    window.SpriteEditor.setExternalResourcePathForAccess = (path) => {
      console.log('预定义的setExternalResourcePathForAccess被调用，path:', path);
      window.SpriteEditor.externalResourcePath = path;

      // 如果React应用已经加载，通知它更新
      if (window.SpriteEditor.updateResourcePath) {
        window.SpriteEditor.updateResourcePath(path);
      }
    };

    // 获取编辑结果的方法
    window.SpriteEditor.getEditResult = () => {
      console.log('获取编辑结果:', window.SpriteEditor.editResult);
      return window.SpriteEditor.editResult;
    };

    // 设置编辑结果的方法（供React应用内部使用）
    window.SpriteEditor.setEditResult = (result) => {
      console.log('设置编辑结果:', result);
      window.SpriteEditor.editResult = result;
    };
  </script>

  <script type="module" crossorigin src="./assets/index-uvm9499N.js"></script>
  <link rel="stylesheet" crossorigin href="./assets/index-BonBy80v.css">
</head>

<body>
  <div id="root"></div>
</body>

</html>