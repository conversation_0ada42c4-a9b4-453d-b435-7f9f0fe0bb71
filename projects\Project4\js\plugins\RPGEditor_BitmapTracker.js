/*:
 * @target MZ
 * @plugindesc 跟踪并记录Bitmap上的文本和图像绘制操作
 * <AUTHOR> Agent
 * @help
 * 这个插件重写了Bitmap的drawText和blt方法，
 * 记录所有绘制的文本和图像信息到elements数组中。
 *
 * 特点:
 * 1. 只记录参数信息，不记录上下文状态
 * 2. 支持区域性重绘，只更新需要的部分
 * 3. 可以选择性地更新特定文本或图像
 * 4. 支持根据文本内容匹配和更新
 * 5. 使用统一的elements数组存储所有元素，通过type属性区分类型
 *
 * 使用方法:
 * - bitmap.elements 数组包含所有绘制的元素信息，每个元素有type属性("text"或"image")
 * - bitmap.getTextElements() 获取所有文本元素
 * - bitmap.getImageElements() 获取所有图像元素
 * - bitmap.redrawElementByContent(content) 根据文本内容重绘特定元素
 * - bitmap.updateElementByContent(content, newText) 根据内容更新文本元素
 * - bitmap.redrawElement(index) 重新绘制特定元素
 * - bitmap.redrawRect(x, y, width, height) 重新绘制指定区域内的所有内容
 * - bitmap.redrawAll() 重新绘制所有内容
 *
 * 示例:
 * // 根据内容更新文本
 * bitmap.updateElementByContent("原始文本", "新文本");
 *
 * // 根据内容重绘文本
 * bitmap.redrawElementByContent("某个文本");
 */

(function () {
    'use strict';

    //=============================================================================
    // 保存原始方法
    //=============================================================================
    const _Bitmap_initialize = Bitmap.prototype.initialize;
    const _Bitmap_drawText = Bitmap.prototype.drawText;
    const _Bitmap_blt = Bitmap.prototype.blt;
    const _Bitmap_clear = Bitmap.prototype.clear;

    //=============================================================================
    // 重写初始化方法，添加elements数组
    //=============================================================================
    Bitmap.prototype.initialize = function (width, height) {
        _Bitmap_initialize.call(this, width, height);
        this.elements = [];

        // 为了向后兼容，添加getter方法
        Object.defineProperty(this, 'texts', {
            get: function () {
                return this.getTextElements();
            }
        });

        Object.defineProperty(this, 'imgs', {
            get: function () {
                return this.getImageElements();
            }
        });
    };

    // 获取所有文本元素
    Bitmap.prototype.getTextElements = function () {
        return this.elements.filter(element => element.type === 'text');
    };

    // 获取所有图像元素
    Bitmap.prototype.getImageElements = function () {
        return this.elements.filter(element => element.type === 'image');
    };

    //=============================================================================
    // 重写drawText方法，只记录参数信息
    //=============================================================================
    Bitmap.prototype.drawText = function (text, x, y, maxWidth, lineHeight, align) {
        // console.log("draw text", text)
        // 调用原始方法绘制文本
        _Bitmap_drawText.call(this, text, x, y, maxWidth, lineHeight, align);

        // 只记录参数信息
        const textInfo = {
            type: 'text',  // 添加类型标识
            text: text,
            x: x,
            y: y,
            maxWidth: maxWidth || 0xffffffff,
            lineHeight: lineHeight || 36,
            align: align || "left"
        };

        // 计算文本的大致边界框，用于区域重绘
        const width = maxWidth || this.measureTextWidth(text) + 4;
        const height = lineHeight || 36;
        textInfo.bounds = {
            x: x,
            y: y,
            width: width,
            height: height
        };

        // 检查是否已存在相同位置的文本，如果是则更新
        const existingIndex = this.elements.findIndex(e =>
            e.type === 'text' && e.x === x && e.y === y &&
            e.maxWidth === maxWidth && e.lineHeight === lineHeight &&
            e.align === align
        );

        if (existingIndex >= 0) {
            this.elements[existingIndex] = textInfo;
        } else {
            this.elements.push(textInfo);
        }

        return textInfo;
    };

    //=============================================================================
    // 重写blt方法，记录图像信息并添加边界框
    //=============================================================================
    Bitmap.prototype.blt = function (source, sx, sy, sw, sh, dx, dy, dw, dh) {
        // 设置默认值
        dw = dw || sw;
        dh = dh || sh;

        // 调用原始方法绘制图像
        _Bitmap_blt.call(this, source, sx, sy, sw, sh, dx, dy, dw, dh);

        // 在source上创建regions属性（如果不存在）
        if (source && !source.regions) {
            source.regions = [];
        }

        // 如果source存在，检查是否已有相同的区域，如果没有则添加
        if (source && source.regions) {
            // 检查是否已存在相同的区域
            const existingRegion = source.regions.find(region =>
                region.sx === sx &&
                region.sy === sy &&
                region.sw === sw &&
                region.sh === sh
            );

            // 如果不存在相同区域，则添加新区域
            if (!existingRegion) {
                const regionInfo = {
                    id: `region_${Date.now()}`,
                    label: `区域 ${source.regions.length + 1}`,
                    sx: sx,
                    sy: sy,
                    sw: sw,
                    sh: sh
                };
                source.regions.push(regionInfo);
                console.log('添加新区域到source.regions:', regionInfo);
            }
        }

        // 记录图像信息
        const imgInfo = {
            type: 'image',  // 添加类型标识
            source: source,
            sx: sx,
            sy: sy,
            sw: sw,
            sh: sh,
            dx: dx,
            dy: dy,
            dw: dw,
            dh: dh,
            // 添加边界框信息，用于区域重绘
            bounds: {
                x: dx,
                y: dy,
                width: dw,
                height: dh
            }
        };

        // 检查是否已存在相同位置的图像，如果是则更新
        const existingIndex = this.elements.findIndex(e =>
            e.type === 'image' && e.dx === dx && e.dy === dy &&
            e.dw === dw && e.dh === dh
        );

        if (existingIndex >= 0) {
            this.elements[existingIndex] = imgInfo;
        } else {
            this.elements.push(imgInfo);
        }

        return imgInfo;
    };

    Bitmap.prototype.redrawing = function () {
        // 临时禁用记录，避免无限循环
        const originalDrawText = Bitmap.prototype.drawText;
        const originalBlt = Bitmap.prototype.blt;
        Bitmap.prototype.drawText = _Bitmap_drawText;
        Bitmap.prototype.blt = _Bitmap_blt;

        // 保存当前内容
        const savedElements = [...this.elements];

        // 清除位图和记录
        this.clear();

        // 先绘制所有图像元素
        const imageElements = savedElements.filter(e => e.type === 'image');
        console.log('重绘图像元素:', imageElements.length);

        for (const imgItem of imageElements) {
            console.log('重绘图像:', imgItem);
            console.log('blt方法被调用:', imgItem);

            try {
                // 检查源图像是否有效
                if (!imgItem.source || !imgItem.source._image) {
                    console.error("源图像无效:", imgItem.source);

                    // 如果源图像无效，绘制一个红色矩形作为占位符
                    this.fillRect(
                        imgItem.dx,
                        imgItem.dy,
                        imgItem.dw,
                        imgItem.dh,
                        imgItem.color || 'rgba(255, 0, 0, 0.5)'
                    );

                    // 不再绘制边框和标签

                    continue;
                }

                console.log("重绘图像源:", imgItem.source);

                // 确保源图像已加载
                if (imgItem.source._loadingState !== "loaded") {
                    console.log("源图像未加载完成");

                    // 如果图像未加载，绘制一个占位符
                    this.fillRect(
                        imgItem.dx,
                        imgItem.dy,
                        imgItem.dw,
                        imgItem.dh,
                        imgItem.color || 'rgba(0, 0, 255, 0.5)'
                    );

                    // 不再绘制边框和标签

                    continue;
                }

                // 调用原始方法绘制图像
                _Bitmap_blt.call(
                    this,
                    imgItem.source,
                    imgItem.sx,
                    imgItem.sy,
                    imgItem.sw,
                    imgItem.sh,
                    imgItem.dx,
                    imgItem.dy,
                    imgItem.dw,
                    imgItem.dh
                );

                // 不再绘制边框

                // 不再绘制标签

                console.log("图像重绘完成");
            } catch (error) {
                console.error("重绘图像元素时出错:", error, imgItem);

                // 如果绘制失败，绘制一个红色矩形作为占位符
                try {
                    this.fillRect(
                        imgItem.dx,
                        imgItem.dy,
                        imgItem.dw,
                        imgItem.dh,
                        imgItem.color || 'rgba(255, 0, 0, 0.5)'
                    );

                    // 不再绘制边框和标签
                } catch (e) {
                    console.error("绘制占位符失败:", e);
                }
            }
        }

        // 再绘制所有文本元素
        const textElements = savedElements.filter(e => e.type === 'text');
        for (const textItem of textElements) {
            _Bitmap_drawText.call(
                this,
                textItem.text,
                textItem.x,
                textItem.y,
                textItem.maxWidth,
                textItem.lineHeight,
                textItem.align
            );
        }

        // 恢复记录功能
        Bitmap.prototype.drawText = originalDrawText;
        Bitmap.prototype.blt = originalBlt;

        // 恢复保存的内容记录
        this.elements = savedElements;

        // 更新纹理
        this._baseTexture.update();
    };

    // 根据内容查找文本元素
    Bitmap.prototype.findElementByContent = function (content) {
        return this.elements.find(e => e.type === 'text' && e.text === content);
    };

    // 根据内容更新文本元素
    Bitmap.prototype.updateElementByContent = function (content, newText) {
        const element = this.findElementByContent(content);
        if (element) {
            element.text = newText;
            this.redrawing();
            return true;
        }
        return false;
    };

    // 根据索引重绘元素
    Bitmap.prototype.redrawElement = function (index) {
        if (index >= 0 && index < this.elements.length) {
            const element = this.elements[index];

            // 临时禁用记录
            const originalDrawText = Bitmap.prototype.drawText;
            const originalBlt = Bitmap.prototype.blt;
            Bitmap.prototype.drawText = _Bitmap_drawText;
            Bitmap.prototype.blt = _Bitmap_blt;

            // 根据元素类型调用相应的绘制方法
            if (element.type === 'text') {
                _Bitmap_drawText.call(
                    this,
                    element.text,
                    element.x,
                    element.y,
                    element.maxWidth,
                    element.lineHeight,
                    element.align
                );
            } else if (element.type === 'image') {
                _Bitmap_blt.call(
                    this,
                    element.source,
                    element.sx,
                    element.sy,
                    element.sw,
                    element.sh,
                    element.dx,
                    element.dy,
                    element.dw,
                    element.dh
                );
            }

            // 恢复记录功能
            Bitmap.prototype.drawText = originalDrawText;
            Bitmap.prototype.blt = originalBlt;

            // 更新纹理
            this._baseTexture.update();
            return true;
        }
        return false;
    };

    // 重绘指定区域内的所有元素
    Bitmap.prototype.redrawRect = function (x, y, width, height) {
        // 找出与指定区域相交的所有元素
        const elementsInRect = this.elements.filter(e => {
            const bounds = e.bounds;
            return (
                bounds.x < x + width &&
                bounds.x + bounds.width > x &&
                bounds.y < y + height &&
                bounds.y + bounds.height > y
            );
        });

        if (elementsInRect.length > 0) {
            // 临时禁用记录
            const originalDrawText = Bitmap.prototype.drawText;
            const originalBlt = Bitmap.prototype.blt;
            Bitmap.prototype.drawText = _Bitmap_drawText;
            Bitmap.prototype.blt = _Bitmap_blt;

            // 清除指定区域
            this.clearRect(x, y, width, height);

            // 重绘区域内的所有元素
            for (const element of elementsInRect) {
                if (element.type === 'text') {
                    _Bitmap_drawText.call(
                        this,
                        element.text,
                        element.x,
                        element.y,
                        element.maxWidth,
                        element.lineHeight,
                        element.align
                    );
                } else if (element.type === 'image') {
                    _Bitmap_blt.call(
                        this,
                        element.source,
                        element.sx,
                        element.sy,
                        element.sw,
                        element.sh,
                        element.dx,
                        element.dy,
                        element.dw,
                        element.dh
                    );
                }
            }

            // 恢复记录功能
            Bitmap.prototype.drawText = originalDrawText;
            Bitmap.prototype.blt = originalBlt;

            // 更新纹理
            this._baseTexture.update();
            return true;
        }
        return false;
    };

    // 重绘所有内容
    Bitmap.prototype.redrawAll = function () {
        this.redrawing();
        return true;
    };

    console.log("RPGEditor_BitmapTracker 插件已加载 - 使用统一的elements数组");
})();
