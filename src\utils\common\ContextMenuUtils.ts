import { TreeNode } from '../tree/TreeNodeTypes';
import { Container } from 'pixi.js';

/**
 * 添加子对象到节点
 * @param nodes 节点数组
 * @param nodeId 父节点ID
 * @param childName 子节点名称
 * @returns 更新后的节点数组
 */
export const addChildToNode = (
  nodes: TreeNode[],
  nodeId: string,
  childName: string
): TreeNode[] => {
  return nodes.map(node => {
    if (node.id === nodeId) {
      // 创建新的子对象
      const newChild = new Container();
      newChild.name = childName;

      // 将新对象添加到PIXI容器中
      if (node.object && 'addChild' in node.object) {
        node.object.addChild(newChild);
      }

      // 创建新的子节点
      const newChildNode: TreeNode = {
        id: `${node.id}/${childName}_${node.children.length}`,
        name: childName,
        object: newChild,
        children: [],
        visible: true,
        hasEvents: false
      };

      // 更新节点的子节点列表
      return {
        ...node,
        children: [...node.children, newChildNode]
      };
    } else if (node.children.length > 0) {
      // 递归处理子节点
      return {
        ...node,
        children: addChildToNode(node.children, nodeId, childName)
      };
    }

    return node;
  });
};

/**
 * 从节点中删除子对象
 * @param nodes 节点数组
 * @param nodeId 要删除的节点ID
 * @returns 更新后的节点数组和是否删除成功
 */
export const deleteNode = (
  nodes: TreeNode[],
  nodeId: string
): { updatedNodes: TreeNode[]; success: boolean } => {
  let success = false;

  // 查找父节点和要删除的节点
  const findParentAndNode = (
    currentNodes: TreeNode[],
    targetId: string
  ): { parent: TreeNode | null; index: number } => {
    for (let i = 0; i < currentNodes.length; i++) {
      const node = currentNodes[i];

      // 检查当前节点的子节点
      for (let j = 0; j < node.children.length; j++) {
        if (node.children[j].id === targetId) {
          return { parent: node, index: j };
        }
      }

      // 递归检查子节点
      if (node.children.length > 0) {
        const result = findParentAndNode(node.children, targetId);
        if (result.parent) {
          return result;
        }
      }
    }

    return { parent: null, index: -1 };
  };

  const { parent, index } = findParentAndNode(nodes, nodeId);

  if (parent && index !== -1) {
    // 从PIXI容器中移除对象
    const nodeToRemove = parent.children[index];
    if (nodeToRemove.object && parent.object && 'removeChild' in parent.object) {
      parent.object.removeChild(nodeToRemove.object);
    }

    // 更新节点树
    const updatedNodes = nodes.map(node => {
      if (node.id === parent.id) {
        // 从父节点的子节点列表中移除
        const updatedChildren = [...node.children];
        updatedChildren.splice(index, 1);

        return {
          ...node,
          children: updatedChildren
        };
      } else if (node.children.length > 0) {
        // 递归处理子节点
        const result = deleteNode(node.children, nodeId);
        if (result.success) {
          success = true;
          return {
            ...node,
            children: result.updatedNodes
          };
        }
      }

      return node;
    });

    success = true;
    return { updatedNodes, success };
  }

  // 如果是顶层节点，直接从数组中移除
  const topLevelIndex = nodes.findIndex(node => node.id === nodeId);
  if (topLevelIndex !== -1) {
    const updatedNodes = [...nodes];
    updatedNodes.splice(topLevelIndex, 1);
    success = true;
    return { updatedNodes, success };
  }

  return { updatedNodes: nodes, success };
};
