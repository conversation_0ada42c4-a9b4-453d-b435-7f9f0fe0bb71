import useProjectStore from '../../store/Store';
// 如果您想进一步扩展全局事件处理功能，可以考虑以下几点：

// 添加更多类型的全局事件处理，如键盘事件、鼠标移动事件等
// 实现事件订阅机制，允许组件注册自定义事件处理函数
// 添加事件过滤和优先级机制，更精细地控制事件处理流程
// 这种模块化的实现方式使得扩展和维护变得更加容易，同时也使代码更加清晰和可读。

// 全局变量，用于标记是否应该忽略下一次点击事件
// 这是一个临时解决方案，用于处理SelectInput组件的点击事件
let shouldIgnoreNextClick = false;

// 导出函数，用于设置shouldIgnoreNextClick标记
export const setIgnoreNextClick = (ignore: boolean) => {
  shouldIgnoreNextClick = ignore;
};
/**
 * 全局事件处理器
 * 处理应用程序范围内的事件，如全局点击事件
 */
class GlobalEventHandler {
  private static instance: GlobalEventHandler;
  private isInitialized: boolean = false;
  private clickHandler: ((e: MouseEvent) => void) | null = null;
  private lastClickTime: number = 0; // 添加时间戳，用于防止短时间内多次处理同一个点击事件
  private isProcessingClick: boolean = false; // 添加标志，表示是否正在处理点击事件

  // 私有构造函数，确保单例模式
  private constructor() { }

  /**
   * 获取GlobalEventHandler实例
   * @returns GlobalEventHandler实例
   */
  public static getInstance(): GlobalEventHandler {
    if (!GlobalEventHandler.instance) {
      GlobalEventHandler.instance = new GlobalEventHandler();
    }
    return GlobalEventHandler.instance;
  }

  /**
   * 初始化全局事件处理
   */
  public initialize(): void {
    if (this.isInitialized) return;

    this.setupGlobalClickHandler();
    this.isInitialized = true;
    console.log('GlobalEventHandler: 初始化完成');
  }

  /**
   * 清理全局事件处理
   */
  public cleanup(): void {
    if (!this.isInitialized) return;

    this.removeGlobalClickHandler();
    this.isInitialized = false;
    console.log('GlobalEventHandler: 清理完成');
  }

  /**
   * 设置全局点击事件处理
   */
  private setupGlobalClickHandler(): void {
    this.clickHandler = this.handleGlobalClick.bind(this);
    document.addEventListener('click', this.clickHandler);
    console.log('GlobalEventHandler: 添加全局点击事件监听');
  }

  /**
   * 移除全局点击事件处理
   */
  private removeGlobalClickHandler(): void {
    if (this.clickHandler) {
      document.removeEventListener('click', this.clickHandler);
      this.clickHandler = null;
      console.log('GlobalEventHandler: 移除全局点击事件监听');
    }
  }

  /**
   * 处理全局点击事件
   * @param e 鼠标事件
   */
  private handleGlobalClick(e: MouseEvent): void {
    // 检查是否应该忽略此次点击事件
    if (shouldIgnoreNextClick) {
      console.debug('GlobalEventHandler: 忽略此次点击事件（由SelectInput组件设置）');
      shouldIgnoreNextClick = false; // 重置标记
      return;
    }

    // 防止短时间内多次处理同一个点击事件
    const now = Date.now();
    if (now - this.lastClickTime < 100) { // 100毫秒内不重复处理
      console.debug('GlobalEventHandler: 点击事件太频繁，忽略此次点击');
      return;
    }
    this.lastClickTime = now;

    // 如果正在处理点击事件，忽略此次点击
    if (this.isProcessingClick) {
      console.debug('GlobalEventHandler: 正在处理点击事件，忽略此次点击');
      return;
    }

    // 标记为正在处理点击事件
    this.isProcessingClick = true;

    // 获取store中的状态和方法
    const store = useProjectStore.getState();
    const {
      projectSelected,
      selectedObjects,
      selectedAsset,
      clearSelectedObjects,
      setSelectedAsset
    } = store;

    // 只有在选择了项目后才处理点击事件
    if (!projectSelected) {
      this.isProcessingClick = false; // 重置标记
      return;
    }

    // 检查点击的元素是否在TreeNodeComponent内
    const isTreeNodeClick = e.target instanceof Element &&
      (e.target.closest('.MuiListItemButton-root') || // TreeNodeComponent的ListItemButton
        e.target.closest('.tree-node-component')); // TreeNodeComponent的类名

    // 检查点击的元素是否在资源面板内
    const isAssetClick = e.target instanceof Element &&
      e.target.closest('.asset-item'); // 资源项的类名

    // 检查点击的是否是TypeTreeWindow组件内的元素
    const isTypeTreeWindowClick = e.target instanceof Element && (
      e.target.closest('[data-component="type-tree-window"]') !== null || // 检查是否在TypeTreeWindow容器内
      e.target.closest('.type-tree-window') !== null || // 检查是否在TypeTreeWindow类名内
      e.target.closest('.type-tree-panel') !== null // 检查是否在TypeTreePanel类名内
    );

    // 检查点击的是否是RightPanel组件内的元素或Material-UI的弹出组件
    const isRightPanelClick = e.target instanceof Element && (
      e.target.closest('.right-panel-container') !== null || // 检查是否在RightPanel容器内
      e.target.closest('.MuiPopover-root') !== null || // 检查是否是弹出菜单
      e.target.closest('.MuiMenu-root') !== null || // 检查是否是菜单
      e.target.closest('.MuiMenuItem-root') !== null || // 检查是否是菜单项
      e.target.closest('.MuiSelect-root') !== null || // 检查是否是Select组件
      e.target.closest('.MuiBackdrop-root') !== null || // 检查是否是背景遮罩
      e.target.closest('.MuiDialog-root') !== null || // 检查是否是对话框（动画编辑器弹窗）
      e.target.closest('.MuiDialog-container') !== null || // 检查是否是对话框容器
      e.target.closest('.MuiDialogContent-root') !== null || // 检查是否是对话框内容
      e.target.closest('.MuiDialogActions-root') !== null || // 检查是否是对话框操作区
      e.target.closest('.animation-panel-container') !== null || // 检查是否是动画面板容器
      (e.target instanceof Element && e.target.closest('button') !== null &&
        (e.target.closest('.right-panel-container') !== null ||
          e.target.closest('.MuiDialog-root') !== null)) // 检查是否是RightPanel或对话框中的按钮
    );

    // 如果点击的不是树节点组件、资源项、RightPanel或TypeTreeWindow，则清除选中对象
    if (!isTreeNodeClick && !isAssetClick && !isRightPanelClick && !isTypeTreeWindowClick) {
      // 检查点击的是否是游戏画布
      const isGameCanvasClick = e.target instanceof Element &&
        (e.target.id === 'gameCanvas' || e.target.closest('.game-container'));

      // 如果点击的是游戏画布，不清除选中对象，因为游戏内部会处理
      if (!isGameCanvasClick) {
        // 添加更详细的调试信息
        if (e.target instanceof Element) {
          const target = e.target;
          console.debug('GlobalEventHandler: 点击元素:', target.tagName);
          console.debug('GlobalEventHandler: 点击元素类名:', target.className);

          // 检查是否是Material-UI组件
          const isMuiComponent =
            target.closest('[class*="Mui"]') !== null;
          console.debug('GlobalEventHandler: 点击元素是Material-UI组件:', isMuiComponent);

          if (isMuiComponent) {
            console.debug('GlobalEventHandler: Material-UI组件类型:',
              target.closest('.MuiPopover-root') ? 'Popover' :
                target.closest('.MuiMenu-root') ? 'Menu' :
                  target.closest('.MuiMenuItem-root') ? 'MenuItem' :
                    target.closest('.MuiSelect-root') ? 'Select' :
                      target.closest('.MuiBackdrop-root') ? 'Backdrop' : '其他'
            );
          }
        }

        console.log('GlobalEventHandler: 点击了非树节点、非资源项和非RightPanel区域，清除选中对象');

        // 清除选中对象
        if (selectedObjects.objects.length > 0) {
          clearSelectedObjects();
        }

        // 清除选中资源
        if (selectedAsset) {
          setSelectedAsset(null);
        }
      }
    } else if (isRightPanelClick) {
      // 如果点击的是RightPanel，记录日志但不做任何处理
      console.log('GlobalEventHandler: 点击了RightPanel区域，不做任何处理');

      // 添加更详细的调试信息
      if (e.target instanceof Element) {
        const target = e.target;
        console.debug('GlobalEventHandler: RightPanel点击元素:', target.tagName);

        // 检查是否是Material-UI组件
        if (target.closest('[class*="Mui"]') !== null) {
          console.debug('GlobalEventHandler: 点击的是Material-UI组件:',
            target.closest('.MuiPopover-root') ? 'Popover' :
              target.closest('.MuiMenu-root') ? 'Menu' :
                target.closest('.MuiMenuItem-root') ? 'MenuItem' :
                  target.closest('.MuiSelect-root') ? 'Select' :
                    target.closest('.MuiBackdrop-root') ? 'Backdrop' : '其他'
          );
        }
      }
    }

    // 重置标记
    setTimeout(() => {
      this.isProcessingClick = false;
    }, 0); // 使用setTimeout确保在当前事件循环结束后重置
  }
}

export default GlobalEventHandler;
