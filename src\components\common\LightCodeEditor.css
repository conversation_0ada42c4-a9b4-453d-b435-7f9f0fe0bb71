/* Monaco Editor 样式 */
.monaco-editor-container {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

/* 确保编辑器填充整个容器 */
.monaco-editor-container .monaco-editor {
  width: 100%;
  height: 100%;
}

/* 自定义滚动条 */
.monaco-scrollable-element > .scrollbar > .slider {
  background: rgba(100, 100, 100, 0.5) !important;
  border-radius: 4px !important;
}

.monaco-scrollable-element > .scrollbar {
  background: rgba(0, 0, 0, 0.1) !important;
}

/* 编辑器内容区域 */
.monaco-editor .monaco-editor-background {
  background-color: inherit;
}

/* 行号区域 */
.monaco-editor .margin {
  background-color: inherit;
}

/* 活动行高亮 */
.monaco-editor .current-line {
  border: none !important;
  background-color: rgba(100, 100, 100, 0.1) !important;
}

/* 匹配括号高亮 */
.monaco-editor .bracket-match {
  background-color: rgba(0, 255, 0, 0.1) !important;
  border: 1px solid #0f0 !important;
}

/* 确保对话框内容区域正确显示 */
.MuiDialogContent-root {
  padding: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
  overflow: hidden !important;
}

/* 确保编辑器容器正确显示 */
.MuiDialogContent-root > div {
  flex: 1 !important;
  overflow: hidden !important;
}

/* 加载指示器样式 */
.monaco-editor-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  background-color: #1e1e1e;
  color: #d4d4d4;
}

/* 确保编辑器获取焦点时没有奇怪的边框 */
.monaco-editor:focus,
.monaco-editor-background:focus,
.monaco-editor .inputarea:focus {
  outline: none !important;
}

/* 确保对话框在最上层 */
.MuiDialog-root {
  z-index: 9999 !important;
  position: relative;
  isolation: isolate;
}

/* 确保编辑器容器样式 */
.code-editor-container {
  position: relative;
  z-index: 9999;
  isolation: isolate;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

/* 确保对话框内容区域正确显示 */
.MuiDialogContent-root {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  padding: 0 !important;
  overflow: hidden !important;
  min-height: 300px !important;
}
