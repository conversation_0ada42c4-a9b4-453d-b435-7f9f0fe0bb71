use serde::{Deserialize, Serialize};
use std::env;
use std::fs;
use std::path::{Path, PathBuf};

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub enum FileType {
    Image,
    Audio,
    Font,
    Other,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FileItem {
    pub name: String,
    pub path: String,
    pub is_directory: bool,
    pub file_type: FileType,
    pub url: Option<String>,
}

// 根据文件扩展名确定文件类型
fn determine_file_type(path: &Path) -> FileType {
    if let Some(extension) = path.extension() {
        let ext = extension.to_string_lossy().to_lowercase();

        match ext.as_str() {
            // 图像文件
            "png" | "jpg" | "jpeg" | "gif" | "bmp" | "webp" => FileType::Image,

            // 音频文件
            "mp3" | "wav" | "ogg" | "m4a" | "flac" => FileType::Audio,

            // 字体文件
            "ttf" | "otf" | "woff" | "woff2" => FileType::Font,

            // 其他文件
            _ => FileType::Other,
        }
    } else {
        FileType::Other
    }
}

// 解析路径，支持相对路径和绝对路径
fn resolve_path(path: &str) -> PathBuf {
    let path_buf = PathBuf::from(path);

    // 如果是绝对路径，直接返回
    if path_buf.is_absolute() {
        return path_buf;
    }

    // 如果是相对路径，尝试从不同的基础路径解析
    let current_dir = env::current_dir().unwrap_or_default();

    // 获取项目根目录（通常是 src-tauri 的父目录）
    let project_root = if current_dir.ends_with("src-tauri") {
        match current_dir.parent() {
            Some(parent) => parent.to_path_buf(),
            None => current_dir.clone(),
        }
    } else {
        current_dir.clone()
    };

    println!("项目根目录: {}", project_root.display());

    // 1. 尝试从项目根目录解析
    let from_project_root = project_root.join(path);
    println!("从项目根目录解析: {}", from_project_root.display());
    if from_project_root.exists() {
        return from_project_root;
    }

    // 2. 尝试从当前目录解析
    let from_current = current_dir.join(path);
    println!("从当前目录解析: {}", from_current.display());
    if from_current.exists() {
        return from_current;
    }

    // 3. 尝试从 public 目录解析
    let from_public = project_root
        .join("public")
        .join(path.trim_start_matches("public/"));
    println!("从 public 目录解析: {}", from_public.display());
    if from_public.exists() {
        return from_public;
    }

    // 4. 尝试从上级目录解析
    if path.starts_with("../") {
        let parent = current_dir.parent().unwrap_or(&current_dir);
        let from_parent = parent.join(path.trim_start_matches("../"));
        println!("从上级目录解析: {}", from_parent.display());
        if from_parent.exists() {
            return from_parent;
        }
    }

    // 默认返回从项目根目录解析的路径
    from_project_root
}

// 列出目录内容
#[tauri::command]
pub fn list_directory_contents(path: String) -> Result<Vec<FileItem>, String> {
    // 打印当前工作目录，帮助调试
    let current_dir = match env::current_dir() {
        Ok(dir) => dir.to_string_lossy().to_string(),
        Err(e) => format!("无法获取当前工作目录: {}", e),
    };
    println!("当前工作目录: {}", current_dir);
    println!("请求的路径: {}", path);

    // 解析路径
    let resolved_path = resolve_path(&path);
    println!("解析后的路径: {}", resolved_path.display());

    if !resolved_path.exists() {
        return Err(format!("路径不存在: {}", resolved_path.display()));
    }

    if !resolved_path.is_dir() {
        return Err(format!("路径不是目录: {}", resolved_path.display()));
    }

    let entries = match fs::read_dir(&resolved_path) {
        Ok(entries) => entries,
        Err(e) => return Err(format!("无法读取目录: {}", e)),
    };

    let mut items = Vec::new();

    for entry in entries {
        let entry = match entry {
            Ok(entry) => entry,
            Err(e) => {
                println!("读取目录项时出错: {}", e);
                continue;
            }
        };

        let path = entry.path();
        let is_dir = path.is_dir();
        let file_type = if is_dir {
            FileType::Other
        } else {
            determine_file_type(&path)
        };

        let name = match path.file_name() {
            Some(name) => name.to_string_lossy().to_string(),
            None => continue,
        };

        // 构建文件项
        let file_path = path.to_string_lossy().to_string();

        // 为图片文件添加 URL
        let url = if !is_dir && matches!(file_type, FileType::Image) {
            // 这里我们不生成 URL，而是在前端使用 convertFileSrc 函数
            Some(file_path.clone())
        } else {
            None
        };

        items.push(FileItem {
            name,
            path: file_path,
            is_directory: is_dir,
            file_type,
            url,
        });
    }

    // 排序：先文件夹，再文件
    items.sort_by(|a, b| {
        if a.is_directory && !b.is_directory {
            std::cmp::Ordering::Less
        } else if !a.is_directory && b.is_directory {
            std::cmp::Ordering::Greater
        } else {
            a.name.to_lowercase().cmp(&b.name.to_lowercase())
        }
    });

    Ok(items)
}
