use std::fs;
use std::sync::Mute<PERSON>;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};

/// 更新 plugins.js 文件
pub fn update_plugins_file(app_handle: &AppHandle) -> Result<(), String> {
    println!("更新 plugins.js 文件");

    // 获取项目名称
    let state_manager = app_handle.state::<Mutex<String>>();
    let project_name = state_manager
        .lock()
        .map_err(|e| format!("Failed to lock project name: {}", e))?
        .clone();

    if project_name.is_empty() {
        return Err("项目名称为空".to_string());
    }

    // 获取项目根目录
    let current_dir =
        std::env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))?;
    let project_root = current_dir
        .parent()
        .ok_or_else(|| "Failed to get project root directory".to_string())?;

    // 构建 plugins.js 文件路径
    let plugins_js_path = project_root
        .join("projects")
        .join(&project_name)
        .join("js")
        .join("plugins.js");

    println!("plugins.js 文件路径: {:?}", plugins_js_path);

    // 如果文件不存在，创建一个空的插件列表
    if !plugins_js_path.exists() {
        println!("plugins.js 文件不存在，创建新文件");
        let empty_plugins = create_empty_plugins_content();
        fs::write(&plugins_js_path, empty_plugins)
            .map_err(|e| format!("Failed to create plugins.js: {}", e))?;
        return Ok(());
    }

    // 读取现有文件
    let content = fs::read_to_string(&plugins_js_path)
        .map_err(|e| format!("Failed to read plugins.js: {}", e))?;

    // 解析插件列表
    let plugins_match = content.find("var $plugins =");
    if plugins_match.is_none() {
        println!("无法找到 'var $plugins =' 字符串，尝试创建新的插件文件");
        let empty_plugins = create_empty_plugins_content();
        fs::write(&plugins_js_path, empty_plugins)
            .map_err(|e| format!("Failed to create plugins.js: {}", e))?;
        return Ok(());
    }

    let start_idx = plugins_match.unwrap();
    let mut bracket_level = 0;
    let mut end_idx = content.len();
    let mut found_opening_bracket = false;

    for (i, c) in content[start_idx..].char_indices() {
        if c == '[' {
            found_opening_bracket = true;
            bracket_level += 1;
        } else if c == ']' && found_opening_bracket {
            bracket_level -= 1;
            if bracket_level == 0 {
                end_idx = start_idx + i + 1;
                break;
            }
        }
    }

    if !found_opening_bracket {
        println!("无法找到插件数组的开始符号 '['，尝试创建新的插件文件");
        let empty_plugins = create_empty_plugins_content();
        fs::write(&plugins_js_path, empty_plugins)
            .map_err(|e| format!("Failed to create plugins.js: {}", e))?;
        return Ok(());
    }

    let plugins_str = &content[start_idx..end_idx];
    let plugins_json_start = plugins_str.find('[').unwrap_or(0);
    let plugins_json_end = plugins_str.rfind(']').unwrap_or(plugins_str.len());

    if plugins_json_start >= plugins_json_end {
        println!("无法正确解析插件数组，尝试创建新的插件文件");
        let empty_plugins = create_empty_plugins_content();
        fs::write(&plugins_js_path, empty_plugins)
            .map_err(|e| format!("Failed to create plugins.js: {}", e))?;
        return Ok(());
    }

    let plugins_json = &plugins_str[plugins_json_start..=plugins_json_end];

    // 解析 JSON
    let mut plugins: Vec<serde_json::Value> = match serde_json::from_str(plugins_json) {
        Ok(p) => p,
        Err(e) => {
            println!("解析 plugins.js 失败: {}", e);
            println!("JSON 内容: {}", plugins_json);
            println!("尝试创建新的插件文件");

            let empty_plugins = create_empty_plugins_content();
            fs::write(&plugins_js_path, empty_plugins)
                .map_err(|e| format!("Failed to create plugins.js: {}", e))?;

            println!("已创建新的 plugins.js 文件");
            return Ok(());
        }
    };

    // 检查是否已包含我们的插件
    let plugin_name = "RPGEditor_PrototypeModifications";
    let mut found = false;

    // 打印现有插件列表
    println!("现有插件列表:");
    for (i, plugin) in plugins.iter().enumerate() {
        if let Some(name) = plugin.get("name").and_then(|n| n.as_str()) {
            println!("  插件 {}: {}", i + 1, name);

            if name == plugin_name {
                found = true;
                println!("  已找到我们的插件: {}", plugin_name);
            }
        }
    }

    // 如果没有找到，添加新插件
    if !found {
        println!("未找到插件 {}，将添加到列表中", plugin_name);
        plugins.push(serde_json::json!({
            "name": plugin_name,
            "status": true,
            "description": "RPG Editor - 自动生成的原型链修改插件",
            "parameters": {}
        }));
    } else {
        println!("插件 {} 已存在，不需要添加", plugin_name);

        // 确保插件状态为启用
        for plugin in &mut plugins {
            if let Some(name) = plugin.get("name") {
                if name.as_str() == Some(plugin_name) {
                    if let Some(obj) = plugin.as_object_mut() {
                        if let Some(status) = obj.get("status") {
                            if !status.as_bool().unwrap_or(false) {
                                println!("  更新插件状态为启用");
                                obj.insert("status".to_string(), serde_json::json!(true));
                            }
                        } else {
                            println!("  添加插件状态为启用");
                            obj.insert("status".to_string(), serde_json::json!(true));
                        }
                    }
                    break;
                }
            }
        }
    }

    // 生成新的 plugins.js 内容
    let new_plugins_json = serde_json::to_string_pretty(&plugins)
        .map_err(|e| format!("Failed to serialize plugins: {}", e))?;

    // 保持原有的格式
    let new_content = if content.contains("\r\n") {
        format!("var $plugins =\r\n{};\r\n", new_plugins_json)
    } else {
        format!("var $plugins =\n{};\n", new_plugins_json)
    };

    // 检查内容是否有变化
    let current_content = fs::read_to_string(&plugins_js_path)
        .map_err(|e| format!("Failed to read plugins.js: {}", e))?;

    if current_content == new_content {
        println!("plugins.js 文件内容没有变化，不需要更新");
        return Ok(());
    }

    println!("plugins.js 文件内容有变化，需要更新");

    // 写入文件前先备份原文件
    if plugins_js_path.exists() {
        let backup_path = plugins_js_path.with_extension("js.bak");
        if let Err(e) = fs::copy(&plugins_js_path, &backup_path) {
            println!("警告: 无法创建备份文件: {}", e);
        } else {
            println!("已创建备份文件: {:?}", backup_path);
        }
    }

    // 写入文件
    fs::write(&plugins_js_path, new_content)
        .map_err(|e| format!("Failed to write plugins.js: {}", e))?;

    // 打印详细信息
    println!("plugins.js 文件已更新，包含 {} 个插件", plugins.len());
    for (i, plugin) in plugins.iter().enumerate() {
        if let Some(name) = plugin.get("name").and_then(|n| n.as_str()) {
            println!("  插件 {}: {}", i + 1, name);
        }
    }

    println!("plugins.js 文件已更新");
    Ok(())
}

/// 创建空的插件内容
fn create_empty_plugins_content() -> String {
    r#"var $plugins =
[
  {"name":"RPGEditor_PrototypeModifications","status":true,"description":"RPG Editor - 自动生成的原型链修改插件","parameters":{}}
];
"#.to_string()
}
