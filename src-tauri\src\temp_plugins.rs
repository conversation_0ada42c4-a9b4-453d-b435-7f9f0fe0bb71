use std::fs;
use std::path::PathBuf;
use std::sync::Mutex;
use tauri::App<PERSON><PERSON><PERSON>;
use tauri::Manager;

// 获取临时插件目录路径
pub fn get_temp_plugins_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {
    // 获取项目根目录
    let current_dir =
        std::env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))?;
    let project_root = current_dir
        .parent()
        .ok_or_else(|| "Failed to get project root directory".to_string())?;

    // 创建临时插件目录（与 projects 同级）
    let temp_plugins_dir = project_root.join("temp_plugins");

    // 确保目录存在
    if !temp_plugins_dir.exists() {
        fs::create_dir_all(&temp_plugins_dir)
            .map_err(|e| format!("Failed to create temp_plugins directory: {}", e))?;
    }

    Ok(temp_plugins_dir)
}

// 获取项目的临时插件目录
pub fn get_project_temp_plugins_dir(
    app_handle: &AppHandle,
    project_name: &str,
) -> Result<PathBuf, String> {
    let temp_plugins_dir = get_temp_plugins_dir(app_handle)?;
    let project_temp_dir = temp_plugins_dir.join(project_name);

    // 确保项目临时目录存在
    if !project_temp_dir.exists() {
        fs::create_dir_all(&project_temp_dir)
            .map_err(|e| format!("Failed to create project temp directory: {}", e))?;
    }

    Ok(project_temp_dir)
}

// 将临时插件复制到项目目录
pub fn copy_temp_plugins_to_project(
    app_handle: &AppHandle,
    project_name: &str,
) -> Result<(), String> {
    println!("复制临时插件到项目目录: {}", project_name);

    // 获取临时插件目录
    let project_temp_dir = get_project_temp_plugins_dir(app_handle, project_name)?;

    // 获取项目根目录
    let current_dir =
        std::env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))?;
    let project_root = current_dir
        .parent()
        .ok_or_else(|| "Failed to get project root directory".to_string())?;

    // 获取项目插件目录
    let project_plugins_dir = project_root
        .join("projects")
        .join(project_name)
        .join("js")
        .join("plugins");

    // 确保项目插件目录存在
    if !project_plugins_dir.exists() {
        fs::create_dir_all(&project_plugins_dir)
            .map_err(|e| format!("Failed to create project plugins directory: {}", e))?;
    }

    // 获取引擎插件目录
    let engine_plugins_dir = project_root
        .join("src")
        .join("engine")
        .join("js")
        .join("plugins");

    // 确保引擎插件目录存在
    if !engine_plugins_dir.exists() {
        fs::create_dir_all(&engine_plugins_dir)
            .map_err(|e| format!("Failed to create engine plugins directory: {}", e))?;
    }

    // 记录复制的插件名称，用于更新 plugins.js
    let mut copied_plugins = Vec::new();

    // 用于存储原型修改插件的内容
    let mut prototype_modifications_content = String::new();

    // 遍历临时插件目录中的所有文件
    if project_temp_dir.exists() {
        for entry in fs::read_dir(&project_temp_dir)
            .map_err(|e| format!("Failed to read temp plugins directory: {}", e))?
        {
            let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
            let path = entry.path();

            if path.is_file() {
                let file_name = path
                    .file_name()
                    .ok_or_else(|| "Failed to get file name".to_string())?
                    .to_string_lossy()
                    .to_string();

                // 处理带项目名前缀的插件文件
                let target_file_name =
                    if file_name.starts_with(&format!("RPGEditor_{}_", project_name)) {
                        // 移除项目名前缀
                        let prefix = format!("RPGEditor_{}_", project_name);
                        let new_name = file_name.replace(&prefix, "RPGEditor_");

                        // 记录插件名（不带扩展名）
                        if let Some(plugin_name) = new_name.strip_suffix(".js") {
                            copied_plugins.push(plugin_name.to_string());
                        }

                        new_name
                    } else {
                        // 记录插件名（不带扩展名）
                        if let Some(plugin_name) = file_name.strip_suffix(".js") {
                            copied_plugins.push(plugin_name.to_string());
                        }

                        file_name
                    };

                let target_path = project_plugins_dir.join(&target_file_name);

                // 复制文件到项目目录
                fs::copy(&path, &target_path).map_err(|e| format!("Failed to copy file: {}", e))?;

                println!("已复制插件: {:?} -> {:?}", path, target_path);

                // 如果是原型修改插件，保存内容以便复制到引擎目录
                if target_file_name == "RPGEditor_PrototypeModifications.js" {
                    prototype_modifications_content = fs::read_to_string(&path).map_err(|e| {
                        format!("Failed to read prototype modifications file: {}", e)
                    })?;
                }
            }
        }
    }

    // 如果找到了原型修改插件，将其复制到引擎目录
    if !prototype_modifications_content.is_empty() {
        let engine_plugin_path = engine_plugins_dir.join("RPGEditor_PrototypeModifications.js");

        // 写入文件
        fs::write(&engine_plugin_path, &prototype_modifications_content)
            .map_err(|e| format!("Failed to write engine plugin file: {}", e))?;

        println!("已复制原型修改插件到引擎目录: {:?}", engine_plugin_path);
    } else {
        println!("未找到原型修改插件，跳过复制到引擎目录");
    }

    // 更新 plugins.js 文件，确保包含所有复制的插件
    update_plugins_js(app_handle, project_name, &copied_plugins)?;

    println!("临时插件已成功复制到项目目录和引擎目录");
    Ok(())
}

// 更新 plugins.js 文件，确保包含指定的插件
fn update_plugins_js(
    app_handle: &AppHandle,
    project_name: &str,
    plugin_names: &[String],
) -> Result<(), String> {
    println!("更新 plugins.js 文件，添加插件: {:?}", plugin_names);

    if plugin_names.is_empty() {
        println!("没有需要添加的插件，跳过更新 plugins.js");
        return Ok(());
    }

    // 获取项目根目录
    let current_dir =
        std::env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))?;
    let project_root = current_dir
        .parent()
        .ok_or_else(|| "Failed to get project root directory".to_string())?;

    // 构建 plugins.js 文件路径
    let plugins_js_path = project_root
        .join("projects")
        .join(project_name)
        .join("js")
        .join("plugins.js");

    // 检查文件是否存在
    if !plugins_js_path.exists() {
        println!("plugins.js 文件不存在，创建新文件");

        // 创建默认的 plugins.js 内容
        let mut plugins_json = Vec::new();

        // 添加所有插件
        for plugin_name in plugin_names {
            plugins_json.push(serde_json::json!({
                "name": plugin_name,
                "status": true,
                "description": "RPG Editor - 自动生成的插件",
                "parameters": {}
            }));
        }

        // 生成 plugins.js 内容
        let plugins_js_content = format!(
            "var $plugins =\n{};",
            serde_json::to_string_pretty(&plugins_json)
                .map_err(|e| format!("Failed to serialize plugins: {}", e))?
        );

        // 写入文件
        fs::write(&plugins_js_path, plugins_js_content)
            .map_err(|e| format!("Failed to write plugins.js: {}", e))?;

        return Ok(());
    }

    // 读取现有文件
    let content = fs::read_to_string(&plugins_js_path)
        .map_err(|e| format!("Failed to read plugins.js: {}", e))?;

    // 解析插件列表
    let plugins_match = content.find("var $plugins =");
    if plugins_match.is_none() {
        println!("无法找到 'var $plugins =' 字符串，创建新的插件文件");

        // 创建默认的 plugins.js 内容
        let mut plugins_json = Vec::new();

        // 添加所有插件
        for plugin_name in plugin_names {
            plugins_json.push(serde_json::json!({
                "name": plugin_name,
                "status": true,
                "description": "RPG Editor - 自动生成的插件",
                "parameters": {}
            }));
        }

        // 生成 plugins.js 内容
        let plugins_js_content = format!(
            "var $plugins =\n{};",
            serde_json::to_string_pretty(&plugins_json)
                .map_err(|e| format!("Failed to serialize plugins: {}", e))?
        );

        // 写入文件
        fs::write(&plugins_js_path, plugins_js_content)
            .map_err(|e| format!("Failed to write plugins.js: {}", e))?;

        return Ok(());
    }

    let start_idx = plugins_match.unwrap();
    let mut bracket_level = 0;
    let mut end_idx = content.len();
    let mut found_opening_bracket = false;

    for (i, c) in content[start_idx..].char_indices() {
        if c == '[' {
            found_opening_bracket = true;
            bracket_level += 1;
        } else if c == ']' && found_opening_bracket {
            bracket_level -= 1;
            if bracket_level == 0 {
                end_idx = start_idx + i + 1;
                break;
            }
        }
    }

    if !found_opening_bracket {
        println!("无法找到插件数组的开始符号 '['，创建新的插件文件");

        // 创建默认的 plugins.js 内容
        let mut plugins_json = Vec::new();

        // 添加所有插件
        for plugin_name in plugin_names {
            plugins_json.push(serde_json::json!({
                "name": plugin_name,
                "status": true,
                "description": "RPG Editor - 自动生成的插件",
                "parameters": {}
            }));
        }

        // 生成 plugins.js 内容
        let plugins_js_content = format!(
            "var $plugins =\n{};",
            serde_json::to_string_pretty(&plugins_json)
                .map_err(|e| format!("Failed to serialize plugins: {}", e))?
        );

        // 写入文件
        fs::write(&plugins_js_path, plugins_js_content)
            .map_err(|e| format!("Failed to write plugins.js: {}", e))?;

        return Ok(());
    }

    let plugins_str = &content[start_idx..end_idx];
    let plugins_json_start = plugins_str.find('[').unwrap_or(0);
    let plugins_json_end = plugins_str.rfind(']').unwrap_or(plugins_str.len());

    if plugins_json_start >= plugins_json_end {
        println!("无法正确解析插件数组，创建新的插件文件");

        // 创建默认的 plugins.js 内容
        let mut plugins_json = Vec::new();

        // 添加所有插件
        for plugin_name in plugin_names {
            plugins_json.push(serde_json::json!({
                "name": plugin_name,
                "status": true,
                "description": "RPG Editor - 自动生成的插件",
                "parameters": {}
            }));
        }

        // 生成 plugins.js 内容
        let plugins_js_content = format!(
            "var $plugins =\n{};",
            serde_json::to_string_pretty(&plugins_json)
                .map_err(|e| format!("Failed to serialize plugins: {}", e))?
        );

        // 写入文件
        fs::write(&plugins_js_path, plugins_js_content)
            .map_err(|e| format!("Failed to write plugins.js: {}", e))?;

        return Ok(());
    }

    let plugins_json_str = &plugins_str[plugins_json_start..=plugins_json_end];

    // 解析现有插件列表
    let mut plugins: Vec<serde_json::Value> = match serde_json::from_str(plugins_json_str) {
        Ok(p) => p,
        Err(e) => {
            println!("解析插件列表失败: {}，创建新的插件文件", e);

            // 创建默认的 plugins.js 内容
            let mut plugins_json = Vec::new();

            // 添加所有插件
            for plugin_name in plugin_names {
                plugins_json.push(serde_json::json!({
                    "name": plugin_name,
                    "status": true,
                    "description": "RPG Editor - 自动生成的插件",
                    "parameters": {}
                }));
            }

            // 生成 plugins.js 内容
            let plugins_js_content = format!(
                "var $plugins =\n{};",
                serde_json::to_string_pretty(&plugins_json)
                    .map_err(|e| format!("Failed to serialize plugins: {}", e))?
            );

            // 写入文件
            fs::write(&plugins_js_path, plugins_js_content)
                .map_err(|e| format!("Failed to write plugins.js: {}", e))?;

            return Ok(());
        }
    };

    // 检查每个插件是否已存在，如果不存在则添加
    let mut has_changes = false;

    for plugin_name in plugin_names {
        // 检查插件是否已存在
        let mut found = false;

        for plugin in &mut plugins {
            if let Some(name) = plugin.get("name") {
                if name.as_str() == Some(plugin_name) {
                    found = true;

                    // 确保插件状态为启用
                    if let Some(obj) = plugin.as_object_mut() {
                        if let Some(status) = obj.get("status") {
                            if !status.as_bool().unwrap_or(false) {
                                println!("  更新插件状态为启用: {}", plugin_name);
                                obj.insert("status".to_string(), serde_json::json!(true));
                                has_changes = true;
                            }
                        } else {
                            println!("  添加插件状态为启用: {}", plugin_name);
                            obj.insert("status".to_string(), serde_json::json!(true));
                            has_changes = true;
                        }
                    }

                    break;
                }
            }
        }

        // 如果没有找到，添加新插件
        if !found {
            println!("  添加新插件: {}", plugin_name);
            plugins.push(serde_json::json!({
                "name": plugin_name,
                "status": true,
                "description": "RPG Editor - 自动生成的插件",
                "parameters": {}
            }));
            has_changes = true;
        }
    }

    // 如果有变化，更新文件
    if has_changes {
        // 生成新的 plugins.js 内容
        let new_plugins_json = serde_json::to_string_pretty(&plugins)
            .map_err(|e| format!("Failed to serialize plugins: {}", e))?;

        // 保持原有的格式，确保插件列表的格式与原来一致
        let new_content = if content.contains("\r\n") {
            // Windows 风格的换行符
            format!("var $plugins =\r\n{};\r\n", new_plugins_json)
        } else {
            // Unix 风格的换行符
            format!("var $plugins =\n{};\n", new_plugins_json)
        };

        // 写入文件前先备份原文件
        if plugins_js_path.exists() {
            let backup_path = plugins_js_path.with_extension("js.bak");
            if let Err(e) = fs::copy(&plugins_js_path, &backup_path) {
                println!("警告: 无法创建备份文件: {}", e);
            } else {
                println!("已创建备份文件: {:?}", backup_path);
            }
        }

        // 写入文件
        fs::write(&plugins_js_path, new_content)
            .map_err(|e| format!("Failed to write plugins.js: {}", e))?;

        println!("plugins.js 文件已更新，包含 {} 个插件", plugins.len());
    } else {
        println!("plugins.js 文件内容没有变化，不需要更新");
    }

    Ok(())
}

// Tauri 命令：手动刷新预览（将临时插件复制到项目目录）
#[tauri::command]
pub fn refresh_preview(app_handle: AppHandle) -> Result<String, String> {
    // 获取项目名称
    let state_manager = app_handle.state::<Mutex<String>>();
    let project_name = state_manager
        .lock()
        .map_err(|e| format!("Failed to lock project name: {}", e))?
        .clone();

    if project_name.is_empty() {
        return Err("未选择项目，无法刷新".to_string());
    }

    // 复制临时插件到项目目录
    copy_temp_plugins_to_project(&app_handle, &project_name)?;

    Ok(format!("项目 {} 已刷新", project_name))
}

// Tauri 命令：退出项目时调用
#[tauri::command]
pub fn exit_project(app_handle: AppHandle) -> Result<String, String> {
    // 获取项目名称
    let state_manager = app_handle.state::<Mutex<String>>();
    let project_name = state_manager
        .lock()
        .map_err(|e| format!("Failed to lock project name: {}", e))?
        .clone();

    if !project_name.is_empty() {
        // 复制临时插件到项目目录
        copy_temp_plugins_to_project(&app_handle, &project_name)?;

        // 清空当前项目名称
        *state_manager
            .lock()
            .map_err(|e| format!("Failed to lock project name: {}", e))? = String::new();
    }

    Ok("已退出项目".to_string())
}
