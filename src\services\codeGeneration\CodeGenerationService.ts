/**
 * 前端代码生成服务
 * 整合代码生成器和后端API调用
 */

import { invoke } from '@tauri-apps/api/core';
import {
  CodeGenerator,
  PluginFormatter,
  PropertyModificationBuilder,
  ObjectCreationBuilder,
  ObjectDeletionBuilder,
  OperationInfo,
  GeneratorConfig,
  PluginFormatterConfig,
  OperationType,
  OperationMode
} from '../../generators';
import { getObjectPath } from '../../utils/object/objectPro';
import useProjectStore from '../../store/Store';

/**
 * 代码生成服务配置
 */
export interface CodeGenerationServiceConfig {
  generator?: Partial<GeneratorConfig>;
  formatter?: Partial<PluginFormatterConfig>;
  batchSize?: number;
  autoSave?: boolean;
  debugMode?: boolean;
}

/**
 * 代码生成服务主类
 */
export class CodeGenerationService {
  private codeGenerator: CodeGenerator;
  private pluginFormatter: PluginFormatter;
  private config: CodeGenerationServiceConfig;
  private operationQueue: OperationInfo[] = [];
  private saveTimeout: NodeJS.Timeout | null = null;

  constructor(config: CodeGenerationServiceConfig = {}) {
    this.config = {
      batchSize: 10,
      autoSave: true,
      debugMode: false,
      ...config
    };

    this.codeGenerator = new CodeGenerator(this.config.generator);
    this.pluginFormatter = new PluginFormatter(this.config.formatter);

    if (this.config.debugMode) {
      console.log('[CodeGenerationService] 初始化完成', this.config);
    }
  }

  /**
   * 记录属性修改操作
   */
  public async recordPropertyModification(
    object: any,
    propertyName: string,
    oldValue: any,
    newValue: any
  ): Promise<string> {
    try {
      const operation = this.buildPropertyModification(object, propertyName, oldValue, newValue);
      
      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 记录属性修改:', operation);
      }

      this.addToQueue(operation);
      
      if (this.config.autoSave) {
        this.scheduleAutoSave();
      }

      return `属性 ${propertyName} 修改已记录`;
    } catch (error) {
      console.error('[CodeGenerationService] 记录属性修改失败:', error);
      throw error;
    }
  }

  /**
   * 记录对象创建操作
   */
  public async recordObjectCreation(
    parentObject: any,
    objectType: string,
    initialProperties: Record<string, any> = {},
    objectName?: string
  ): Promise<string> {
    try {
      const operation = this.buildObjectCreation(parentObject, objectType, initialProperties, objectName);
      
      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 记录对象创建:', operation);
      }

      this.addToQueue(operation);
      
      if (this.config.autoSave) {
        this.scheduleAutoSave();
      }

      return `对象创建操作已记录`;
    } catch (error) {
      console.error('[CodeGenerationService] 记录对象创建失败:', error);
      throw error;
    }
  }

  /**
   * 记录对象删除操作
   */
  public async recordObjectDeletion(object: any): Promise<string> {
    try {
      const operation = this.buildObjectDeletion(object);
      
      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 记录对象删除:', operation);
      }

      this.addToQueue(operation);
      
      if (this.config.autoSave) {
        this.scheduleAutoSave();
      }

      return `对象删除操作已记录`;
    } catch (error) {
      console.error('[CodeGenerationService] 记录对象删除失败:', error);
      throw error;
    }
  }

  /**
   * 立即保存所有操作
   */
  public async saveAllOperations(): Promise<string> {
    if (this.operationQueue.length === 0) {
      return '没有待保存的操作';
    }

    try {
      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 开始保存操作，数量:', this.operationQueue.length);
      }

      // 生成代码
      const generatedCode = this.codeGenerator.generateCode(this.operationQueue);
      const pluginCode = this.pluginFormatter.formatAsPlugin(generatedCode);

      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 生成的插件代码长度:', pluginCode.length);
      }

      // 调用后端API保存
      const result = await invoke('save_generated_plugin_code', {
        plugin_code: pluginCode,
        project_name: this.getCurrentProjectName()
      });

      // 清空队列
      this.operationQueue = [];
      this.codeGenerator.reset();

      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 保存完成:', result);
      }

      return result as string;
    } catch (error) {
      console.error('[CodeGenerationService] 保存失败:', error);
      throw error;
    }
  }

  /**
   * 生成代码预览
   */
  public generatePreview(): string {
    if (this.operationQueue.length === 0) {
      return '没有待生成的操作';
    }

    return this.codeGenerator.generatePreview(this.operationQueue);
  }

  /**
   * 获取当前队列状态
   */
  public getQueueStatus() {
    return {
      operationCount: this.operationQueue.length,
      operations: this.operationQueue.map(op => ({
        id: op.id,
        type: op.operationType,
        mode: op.operationMode,
        timestamp: op.timestamp
      }))
    };
  }

  /**
   * 清空操作队列
   */
  public clearQueue(): void {
    this.operationQueue = [];
    this.codeGenerator.reset();
    
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
      this.saveTimeout = null;
    }

    if (this.config.debugMode) {
      console.log('[CodeGenerationService] 队列已清空');
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 构建属性修改操作
   */
  private buildPropertyModification(
    object: any,
    propertyName: string,
    oldValue: any,
    newValue: any
  ): OperationInfo {
    const operationMode = this.determineOperationMode(object);
    
    return PropertyModificationBuilder.create()
      .target(object)
      .path(getObjectPath(object))
      .property(propertyName, oldValue, newValue)
      .mode(operationMode)
      .build();
  }

  /**
   * 构建对象创建操作
   */
  private buildObjectCreation(
    parentObject: any,
    objectType: string,
    initialProperties: Record<string, any>,
    objectName?: string
  ): OperationInfo {
    const operationMode = this.determineOperationMode(parentObject);
    const parentPath = getObjectPath(parentObject);
    
    return ObjectCreationBuilder.create()
      .target(parentObject)
      .path([...parentPath, 'new_child']) // 临时路径，实际路径会在创建后确定
      .objectType(objectType)
      .parent(parentPath)
      .properties(initialProperties)
      .name(objectName)
      .mode(operationMode)
      .build();
  }

  /**
   * 构建对象删除操作
   */
  private buildObjectDeletion(object: any): OperationInfo {
    const operationMode = this.determineOperationMode(object);
    
    return ObjectDeletionBuilder.create()
      .target(object)
      .path(getObjectPath(object))
      .mode(operationMode)
      .build();
  }

  /**
   * 确定操作模式
   */
  private determineOperationMode(object: any): OperationMode {
    // 检查对象是否有类型创建标记
    if (object._rpgEditorTypeCreated === true) {
      return OperationMode.TYPE;
    }

    // 检查当前选中对象状态
    const selectedObjects = useProjectStore.getState().selectedObjects;
    if (selectedObjects.className) {
      return OperationMode.TYPE;
    }

    return OperationMode.OBJECT;
  }

  /**
   * 添加操作到队列
   */
  private addToQueue(operation: OperationInfo): void {
    this.operationQueue.push(operation);

    // 如果队列过大，自动保存
    if (this.operationQueue.length >= (this.config.batchSize || 10)) {
      this.saveAllOperations().catch(error => {
        console.error('[CodeGenerationService] 自动保存失败:', error);
      });
    }
  }

  /**
   * 调度自动保存
   */
  private scheduleAutoSave(): void {
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }

    this.saveTimeout = setTimeout(() => {
      this.saveAllOperations().catch(error => {
        console.error('[CodeGenerationService] 自动保存失败:', error);
      });
    }, 1000); // 1秒后自动保存
  }

  /**
   * 获取当前项目名称
   */
  private getCurrentProjectName(): string | undefined {
    return useProjectStore.getState().projectName || undefined;
  }
}

// ==================== 单例实例 ====================

let codeGenerationServiceInstance: CodeGenerationService | null = null;

/**
 * 获取代码生成服务单例实例
 */
export function getCodeGenerationService(config?: CodeGenerationServiceConfig): CodeGenerationService {
  if (!codeGenerationServiceInstance) {
    codeGenerationServiceInstance = new CodeGenerationService(config);
  }
  return codeGenerationServiceInstance;
}

/**
 * 重置代码生成服务实例
 */
export function resetCodeGenerationService(): void {
  if (codeGenerationServiceInstance) {
    codeGenerationServiceInstance.clearQueue();
    codeGenerationServiceInstance = null;
  }
}
