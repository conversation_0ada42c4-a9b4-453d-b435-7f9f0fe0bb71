/**
 * 前端代码生成服务
 * 整合代码生成器和后端API调用
 */

import { invoke } from '@tauri-apps/api/core';
import {
  CodeGenerator,
  PluginFormatter,
  PropertyModificationBuilder,
  ObjectCreationBuilder,
  ObjectDeletionBuilder,
  OperationInfo,
  GeneratorConfig,
  PluginFormatterConfig,
  OperationType,
  OperationMode
} from '../../generators';
import { getObjectPath } from '../../utils/object/objectPro';
import useProjectStore from '../../store/Store';

/**
 * 代码生成服务配置
 */
export interface CodeGenerationServiceConfig {
  generator?: Partial<GeneratorConfig>;
  formatter?: Partial<PluginFormatterConfig>;
  batchSize?: number;
  autoSave?: boolean;
  debugMode?: boolean;
  saveToTempOnly?: boolean; // 新增：是否只保存到临时目录
}

/**
 * 代码生成服务主类
 */
export class CodeGenerationService {
  private codeGenerator: CodeGenerator;
  private pluginFormatter: PluginFormatter;
  private config: CodeGenerationServiceConfig;
  private operationQueue: OperationInfo[] = [];
  private saveTimeout: NodeJS.Timeout | null = null;

  constructor(config: CodeGenerationServiceConfig = {}) {
    this.config = {
      batchSize: 10,
      autoSave: false, // 默认关闭自动保存
      debugMode: false,
      saveToTempOnly: true, // 默认只保存到临时目录
      ...config
    };

    this.codeGenerator = new CodeGenerator(this.config.generator);
    this.pluginFormatter = new PluginFormatter(this.config.formatter);

    if (this.config.debugMode) {
      console.log('[CodeGenerationService] 初始化完成', this.config);
    }
  }

  /**
   * 记录属性修改操作
   */
  public async recordPropertyModification(
    object: any,
    propertyName: string,
    oldValue: any,
    newValue: any
  ): Promise<string> {
    try {
      const operation = this.buildPropertyModification(object, propertyName, oldValue, newValue);

      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 记录属性修改:', operation);
      }

      this.addToQueue(operation);

      if (this.config.autoSave) {
        this.scheduleAutoSave();
      }

      return `属性 ${propertyName} 修改已记录`;
    } catch (error) {
      console.error('[CodeGenerationService] 记录属性修改失败:', error);
      throw error;
    }
  }

  /**
   * 记录对象创建操作
   */
  public async recordObjectCreation(
    parentObject: any,
    objectType: string,
    initialProperties: Record<string, any> = {},
    objectName?: string
  ): Promise<string> {
    try {
      const operation = this.buildObjectCreation(parentObject, objectType, initialProperties, objectName);

      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 记录对象创建:', operation);
      }

      this.addToQueue(operation);

      if (this.config.autoSave) {
        this.scheduleAutoSave();
      }

      return `对象创建操作已记录`;
    } catch (error) {
      console.error('[CodeGenerationService] 记录对象创建失败:', error);
      throw error;
    }
  }

  /**
   * 记录对象删除操作
   */
  public async recordObjectDeletion(object: any): Promise<string> {
    try {
      const operation = this.buildObjectDeletion(object);

      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 记录对象删除:', operation);
      }

      this.addToQueue(operation);

      if (this.config.autoSave) {
        this.scheduleAutoSave();
      }

      return `对象删除操作已记录`;
    } catch (error) {
      console.error('[CodeGenerationService] 记录对象删除失败:', error);
      throw error;
    }
  }

  /**
   * 保存到临时目录（Ctrl+S 触发）
   */
  public async saveToTemp(): Promise<string> {
    if (this.operationQueue.length === 0) {
      return '没有待保存的操作';
    }

    try {
      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 保存到临时目录，操作数量:', this.operationQueue.length);
      }

      // 生成代码
      const generatedCode = this.codeGenerator.generateCode(this.operationQueue);
      const pluginCode = this.pluginFormatter.formatAsPlugin(generatedCode);

      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 生成的插件代码长度:', pluginCode.length);
      }

      // 调用后端API保存到临时目录
      const result = await invoke('save_to_temp_plugins', {
        request: {
          plugin_code: pluginCode,
          project_name: this.getCurrentProjectName()
        }
      });

      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 临时保存完成:', result);
      }

      return result as string;
    } catch (error) {
      console.error('[CodeGenerationService] 临时保存失败:', error);
      throw error;
    }
  }

  /**
   * 刷新预览（复制到引擎和项目目录）
   */
  public async refreshPreview(): Promise<string> {
    try {
      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 刷新预览');
      }

      // 调用后端API复制文件
      const result = await invoke('refresh_preview_from_temp', {
        project_name: this.getCurrentProjectName()
      });

      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 预览刷新完成:', result);
      }

      return result as string;
    } catch (error) {
      console.error('[CodeGenerationService] 预览刷新失败:', error);
      throw error;
    }
  }

  /**
   * 立即保存所有操作（兼容性方法）
   */
  public async saveAllOperations(): Promise<string> {
    // 根据配置决定保存方式
    if (this.config.saveToTempOnly) {
      return await this.saveToTemp();
    } else {
      // 保持原有的完整保存逻辑
      return await this.saveToTemp();
    }
  }

  /**
   * 生成代码预览
   */
  public generatePreview(): string {
    if (this.operationQueue.length === 0) {
      return '没有待生成的操作';
    }

    return this.codeGenerator.generatePreview(this.operationQueue);
  }

  /**
   * 获取当前队列状态
   */
  public getQueueStatus() {
    return {
      operationCount: this.operationQueue.length,
      operations: this.operationQueue.map(op => ({
        id: op.id,
        type: op.operationType,
        mode: op.operationMode,
        timestamp: op.timestamp
      }))
    };
  }

  /**
   * 清空操作队列
   */
  public clearQueue(): void {
    this.operationQueue = [];
    this.codeGenerator.reset();

    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
      this.saveTimeout = null;
    }

    if (this.config.debugMode) {
      console.log('[CodeGenerationService] 队列已清空');
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 构建属性修改操作
   */
  private buildPropertyModification(
    object: any,
    propertyName: string,
    oldValue: any,
    newValue: any
  ): OperationInfo {
    const operationMode = this.determineOperationMode(object);

    return PropertyModificationBuilder.create()
      .target(object)
      .path(getObjectPath(object))
      .property(propertyName, oldValue, newValue)
      .mode(operationMode)
      .build();
  }

  /**
   * 构建对象创建操作
   */
  private buildObjectCreation(
    parentObject: any,
    objectType: string,
    initialProperties: Record<string, any>,
    objectName?: string
  ): OperationInfo {
    const operationMode = this.determineOperationMode(parentObject);
    const parentPath = getObjectPath(parentObject);

    return ObjectCreationBuilder.create()
      .target(parentObject)
      .path([...parentPath, 'new_child']) // 临时路径，实际路径会在创建后确定
      .objectType(objectType)
      .parent(parentPath)
      .properties(initialProperties)
      .name(objectName)
      .mode(operationMode)
      .build();
  }

  /**
   * 构建对象删除操作
   */
  private buildObjectDeletion(object: any): OperationInfo {
    const operationMode = this.determineOperationMode(object);

    return ObjectDeletionBuilder.create()
      .target(object)
      .path(getObjectPath(object))
      .mode(operationMode)
      .build();
  }

  /**
   * 确定操作模式
   */
  private determineOperationMode(object: any): OperationMode {
    // 检查对象是否有类型创建标记
    if (object._rpgEditorTypeCreated === true) {
      return OperationMode.TYPE;
    }

    // 检查当前选中对象状态
    const selectedObjects = useProjectStore.getState().selectedObjects;
    if (selectedObjects.className) {
      return OperationMode.TYPE;
    }

    return OperationMode.OBJECT;
  }

  /**
   * 添加操作到队列
   */
  private addToQueue(operation: OperationInfo): void {
    console.log(`[CodeGenerationService] 添加操作到队列: ${operation.operationType} - ${operation.objectPath.join('/')} - ${operation.className}`);
    console.log(`[CodeGenerationService] 队列长度: ${this.operationQueue.length} -> ${this.operationQueue.length + 1}`);

    this.operationQueue.push(operation);

    // 如果队列过大，自动保存
    if (this.operationQueue.length >= (this.config.batchSize || 10)) {
      this.saveAllOperations().catch(error => {
        console.error('[CodeGenerationService] 自动保存失败:', error);
      });
    }
  }

  /**
   * 调度自动保存
   */
  private scheduleAutoSave(): void {
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }

    this.saveTimeout = setTimeout(() => {
      this.saveAllOperations().catch(error => {
        console.error('[CodeGenerationService] 自动保存失败:', error);
      });
    }, 1000); // 1秒后自动保存
  }

  /**
   * 获取当前项目名称
   */
  private getCurrentProjectName(): string | undefined {
    return useProjectStore.getState().projectName || undefined;
  }
}

// ==================== 单例实例 ====================

let codeGenerationServiceInstance: CodeGenerationService | null = null;

/**
 * 获取代码生成服务单例实例
 */
export function getCodeGenerationService(config?: CodeGenerationServiceConfig): CodeGenerationService {
  if (!codeGenerationServiceInstance) {
    codeGenerationServiceInstance = new CodeGenerationService(config);
    console.log('[CodeGenerationService] 创建新的单例实例');
  } else if (config) {
    // 如果实例已存在但传入了新配置，更新配置但不重新创建实例
    console.log('[CodeGenerationService] 使用现有实例，忽略新配置');
  }
  return codeGenerationServiceInstance;
}

/**
 * 重置代码生成服务实例
 */
export function resetCodeGenerationService(): void {
  if (codeGenerationServiceInstance) {
    codeGenerationServiceInstance.clearQueue();
    codeGenerationServiceInstance = null;
  }
}
