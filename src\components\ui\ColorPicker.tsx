import React, { useState, useRef } from "react";
import { Box, Popover, Typography, IconButton, Tooltip, Paper } from "@mui/material";
import ColorLensIcon from '@mui/icons-material/ColorLens';
import { styled } from "@mui/material/styles";

interface ColorPickerProps {
  label?: string;
  value: string;
  onChange: (value: string) => void;
  width?: string | number;
  compact?: boolean;
}

// 预定义的颜色选项
const PRESET_COLORS = [
  "#FF0000", "#FF4500", "#FFA500", "#FFFF00", "#9ACD32",
  "#008000", "#00FFFF", "#0000FF", "#4B0082", "#800080",
  "#FFFFFF", "#F5F5F5", "#D3D3D3", "#A9A9A9", "#808080",
  "#696969", "#000000", "#8B4513", "#A52A2A", "#D2691E"
];

// 样式化的颜色方块
const ColorBox = styled(Box)(() => ({
  width: 24,
  height: 24,
  borderRadius: 4,
  cursor: 'pointer',
  transition: 'transform 0.2s, box-shadow 0.2s',
  border: '1px solid rgba(0, 0, 0, 0.1)',
  '&:hover': {
    transform: 'scale(1.1)',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'
  }
}));

// 样式化的颜色显示框
const ColorDisplay = styled(Box)(() => ({
  width: 32,
  height: 32,
  borderRadius: 4,
  cursor: 'pointer',
  border: '1px solid rgba(0, 0, 0, 0.1)',
  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
  transition: 'transform 0.2s',
  '&:hover': {
    transform: 'scale(1.05)',
    boxShadow: '0 2px 5px rgba(0, 0, 0, 0.2)'
  }
}));

const ColorPicker: React.FC<ColorPickerProps> = ({
  label,
  value,
  onChange,
  width = "100%",
  compact = false,
}) => {
  // 状态管理
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [customColor, setCustomColor] = useState(value);
  const colorInputRef = useRef<HTMLInputElement>(null);

  // 打开颜色选择器弹出窗口
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
    setCustomColor(value);
  };

  // 关闭颜色选择器弹出窗口
  const handleClose = () => {
    setAnchorEl(null);
  };

  // 选择预设颜色
  const handleSelectColor = (color: string) => {
    onChange(color);
    handleClose();
  };

  // 处理自定义颜色变化
  const handleCustomColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = event.target.value;
    setCustomColor(newColor);
    onChange(newColor);
  };

  // 打开原生颜色选择器
  const openNativeColorPicker = () => {
    if (colorInputRef.current) {
      colorInputRef.current.click();
    }
  };

  // 弹出窗口是否打开
  const open = Boolean(anchorEl);

  // 紧凑模式
  if (compact) {
    return (
      <Box sx={{
        display: "flex",
        alignItems: "center",
        width: width,
        justifyContent: label ? "space-between" : "flex-end"
      }}>
        {label && (
          <Typography
            variant="caption"
            sx={{
              fontSize: '0.75rem',
              color: 'text.secondary',
              mr: 1
            }}
          >
            {label}
          </Typography>
        )}

        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <ColorDisplay
            sx={{ bgcolor: value }}
            onClick={handleClick}
          />

          {/* 隐藏的原生颜色输入 */}
          <input
            ref={colorInputRef}
            type="color"
            value={customColor}
            onChange={handleCustomColorChange}
            style={{
              position: 'absolute',
              opacity: 0,
              pointerEvents: 'none',
              height: 0,
              width: 0
            }}
          />

          {/* 颜色选择器弹出窗口 */}
          <Popover
            open={open}
            anchorEl={anchorEl}
            onClose={handleClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'center',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'center',
            }}
          >
            <Paper sx={{ p: 2, width: 220, maxWidth: '90vw' }}>
              {/* 预设颜色网格 */}
              <Typography variant="caption" sx={{ mb: 1, display: 'block', color: 'text.secondary' }}>
                预设颜色
              </Typography>
              <Box sx={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: 1,
                mb: 2
              }}>
                {PRESET_COLORS.map((color, index) => (
                  <ColorBox
                    key={index}
                    sx={{ bgcolor: color }}
                    onClick={() => handleSelectColor(color)}
                  />
                ))}
              </Box>

              {/* 自定义颜色选择器 */}
              <Typography variant="caption" sx={{ mb: 1, display: 'block', color: 'text.secondary' }}>
                自定义颜色
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box
                  sx={{
                    width: 40,
                    height: 40,
                    borderRadius: 1,
                    bgcolor: customColor,
                    border: '1px solid rgba(0, 0, 0, 0.1)',
                    mr: 1
                  }}
                />
                <Box
                  sx={{
                    flex: 1,
                    border: '1px solid rgba(0, 0, 0, 0.1)',
                    borderRadius: 1,
                    overflow: 'hidden'
                  }}
                >
                  <input
                    type="color"
                    value={customColor}
                    onChange={handleCustomColorChange}
                    style={{
                      width: '100%',
                      height: 40,
                      border: 'none',
                      padding: 0,
                      background: 'transparent',
                      cursor: 'pointer'
                    }}
                  />
                </Box>
              </Box>
            </Paper>
          </Popover>
        </Box>
      </Box>
    );
  }

  // 标准模式
  return (
    <Box sx={{
      width: width,
      mb: 1,
      display: "flex",
      flexDirection: "column"
    }}>
      {label && (
        <Typography
          variant="body2"
          sx={{
            mb: 0.5,
            color: 'text.secondary',
            fontWeight: 'medium'
          }}
        >
          {label}
        </Typography>
      )}

      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <ColorDisplay
          sx={{
            bgcolor: value,
            width: 40,
            height: 40
          }}
          onClick={handleClick}
        />

        <Tooltip title="选择颜色">
          <IconButton
            size="small"
            onClick={openNativeColorPicker}
            sx={{ ml: 1 }}
          >
            <ColorLensIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        <Typography
          variant="body2"
          sx={{
            ml: 1,
            color: 'text.secondary',
            fontSize: '0.875rem'
          }}
        >
          {value.toUpperCase()}
        </Typography>

        {/* 隐藏的原生颜色输入 */}
        <input
          ref={colorInputRef}
          type="color"
          value={customColor}
          onChange={handleCustomColorChange}
          style={{
            position: 'absolute',
            opacity: 0,
            pointerEvents: 'none',
            height: 0,
            width: 0
          }}
        />

        {/* 颜色选择器弹出窗口 */}
        <Popover
          open={open}
          anchorEl={anchorEl}
          onClose={handleClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'left',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left',
          }}
        >
          <Paper sx={{ p: 2, width: 220, maxWidth: '90vw' }}>
            {/* 预设颜色网格 */}
            <Typography variant="caption" sx={{ mb: 1, display: 'block', color: 'text.secondary' }}>
              预设颜色
            </Typography>
            <Box sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: 1,
              mb: 2
            }}>
              {PRESET_COLORS.map((color, index) => (
                <ColorBox
                  key={index}
                  sx={{ bgcolor: color }}
                  onClick={() => handleSelectColor(color)}
                />
              ))}
            </Box>

            {/* 自定义颜色选择器 */}
            <Typography variant="caption" sx={{ mb: 1, display: 'block', color: 'text.secondary' }}>
              自定义颜色
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: 1,
                  bgcolor: customColor,
                  border: '1px solid rgba(0, 0, 0, 0.1)',
                  mr: 1
                }}
              />
              <Box
                sx={{
                  flex: 1,
                  border: '1px solid rgba(0, 0, 0, 0.1)',
                  borderRadius: 1,
                  overflow: 'hidden'
                }}
              >
                <input
                  type="color"
                  value={customColor}
                  onChange={handleCustomColorChange}
                  style={{
                    width: '100%',
                    height: 40,
                    border: 'none',
                    padding: 0,
                    background: 'transparent',
                    cursor: 'pointer'
                  }}
                />
              </Box>
            </Box>
          </Paper>
        </Popover>
      </Box>
    </Box>
  );
};

export default ColorPicker;
