// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::fs;
use std::sync::Mutex;
use tauri::Manager;

// 导入自定义模块
mod asset_manager;
mod coreRPGtype;
mod file_utils;
mod history;
mod project;
mod save;
mod temp_plugins;
mod utils;

// // 模板文件将从 temp.js 读取

// #[tauri::command]
// fn save_modified_properties(
//     app_handle: tauri::AppHandle,
//     properties: serde_json::Value,
// ) -> Result<(), String> {
//     // 打印接收到的参数
//     println!("接收到 save_modified_properties 调用");
//     println!("属性已更新但未保存到文件（按Ctrl+S保存）");

//     // 只记录属性但不写入文件
//     return Ok(());
// }

// // 此函数已被移除，因为我们不再使用RPGEditor_prototype方案
// #[tauri::command]
// fn save_properties_to_file(
//     _app_handle: tauri::AppHandle,
//     _properties: serde_json::Value,
// ) -> Result<(), String> {
//     println!("save_properties_to_file 函数已被弃用，请使用场景重写方案");
//     println!("请使用 save_scene_extensions 函数保存属性变化");

//     Ok(())
// }

#[tauri::command]
fn read_data_file(app_handle: tauri::AppHandle, file: String) -> Result<String, String> {
    use utils::{file_ops, logging, path, project_state};

    logging::log_function_call!("FileManager", "read_data_file", &file);

    // 获取当前项目名称
    let project_name = project_state::get_current_project_name(&app_handle)?;

    // 确定文件路径
    let file_path = if file.starts_with("plugins/") {
        // 如果是插件文件，使用plugins目录
        let plugins_dir = path::get_project_plugins_dir(&project_name)?;
        let plugin_file = file.strip_prefix("plugins/").unwrap_or(&file);
        plugins_dir.join(plugin_file)
    } else {
        // 否则使用data目录
        let data_dir = path::get_project_data_dir(&project_name)?;
        data_dir.join(&file)
    };

    logging::log_debug("FileManager", &format!("读取文件: {:?}", file_path));

    // 读取文件内容
    file_ops::read_file_safe(&file_path)
}

#[tauri::command]
fn write_data_file(
    app_handle: tauri::AppHandle,
    file: String,
    content: String,
) -> Result<(), String> {
    use utils::{file_ops, logging, path, project_state};

    logging::log_function_call!("FileManager", "write_data_file", &file, &content.len());

    // 获取当前项目名称
    let project_name = project_state::get_current_project_name(&app_handle)?;

    // 确定文件路径
    let file_path = if file.starts_with("plugins/") {
        // 如果是插件文件，使用plugins目录
        let plugins_dir = path::get_project_plugins_dir(&project_name)?;
        let plugin_file = file.strip_prefix("plugins/").unwrap_or(&file);
        plugins_dir.join(plugin_file)
    } else {
        // 否则使用data目录
        let data_dir = path::get_project_data_dir(&project_name)?;
        data_dir.join(&file)
    };

    logging::log_debug("FileManager", &format!("写入文件: {:?}", file_path));

    // 写入文件
    file_ops::write_file_safe(&file_path, &content)?;

    logging::log_info("FileManager", &format!("文件写入成功: {:?}", file_path));

    Ok(())
}

// 场景扩展相关函数已删除

// 设置当前项目名称
#[tauri::command]
fn set_current_project(app_handle: tauri::AppHandle, project_name: String) -> Result<(), String> {
    use utils::{cache, logging, project_state, validation};

    logging::log_function_call!("ProjectManager", "set_current_project", &project_name);

    // 验证项目名称
    validation::validate_project_name(&project_name)?;

    // 检查是否有未保存的修改
    if !cache::is_cache_empty() {
        if let Some(cached_modifications) = cache::get_cached_modifications() {
            if !cached_modifications.is_empty() {
                logging::log_info(
                    "ProjectManager",
                    &format!(
                        "检测到 {} 个未保存的修改，保留缓存",
                        cached_modifications.len()
                    ),
                );
                // 不清空缓存，让用户先保存
                // 或者可以选择自动保存
            } else {
                // 清空缓存（切换项目时）
                if let Err(e) = cache::clear_cache() {
                    logging::log_error("ProjectManager", &format!("清空缓存失败: {}", e));
                }
            }
        }
    } else {
        logging::log_debug("ProjectManager", "缓存为空，无需清空");
    }

    // 设置项目名称
    project_state::set_current_project_name(&app_handle, project_name.clone())?;

    logging::log_info(
        "ProjectManager",
        &format!("项目名称已设置: {}", project_name),
    );

    Ok(())
}

// 获取公共目录路径
#[tauri::command]
fn get_public_dir_path(app_handle: tauri::AppHandle) -> Result<String, String> {
    // 获取项目根目录
    let resource_dir = app_handle
        .app_handle()
        .path()
        .resource_dir()
        .map_err(|e| format!("无法获取应用目录: {}", e))?;

    // 打印资源目录路径，用于调试
    println!("资源目录路径: {}", resource_dir.display());

    // 获取当前工作目录
    let current_dir =
        std::env::current_dir().map_err(|e| format!("无法获取当前工作目录: {}", e))?;
    println!("当前工作目录: {}", current_dir.display());

    // 尝试找到项目根目录
    // 如果当前目录包含 "src-tauri"，则向上查找到项目根目录
    let mut root_dir = current_dir.clone();
    while root_dir
        .file_name()
        .map_or(false, |name| name != "src-tauri")
    {
        if !root_dir.pop() {
            break;
        }
    }

    // 如果找到了 src-tauri 目录，再向上一级获取项目根目录
    if root_dir
        .file_name()
        .map_or(false, |name| name == "src-tauri")
    {
        root_dir.pop();
    } else {
        // 如果没有找到 src-tauri 目录，使用当前目录的父目录
        root_dir = current_dir
            .parent()
            .ok_or_else(|| "无法获取当前目录的父目录".to_string())?
            .to_path_buf();
    }

    println!("项目根目录: {}", root_dir.display());

    // 使用 projects 目录而不是 public 目录
    let projects_dir = root_dir.join("projects");

    if !projects_dir.exists() {
        fs::create_dir_all(&projects_dir).map_err(|e| format!("创建 projects 目录失败: {}", e))?;
    }

    println!("项目目录路径: {}", projects_dir.display());
    Ok(projects_dir.to_string_lossy().to_string())
}

// 获取当前项目的文件列表
#[tauri::command]
fn get_project_files(app_handle: tauri::AppHandle) -> Result<Vec<String>, String> {
    // 获取当前项目名称
    let state_manager = app_handle.state::<std::sync::Mutex<String>>();
    let project_name = state_manager
        .lock()
        .map_err(|e| format!("Failed to lock project name: {}", e))?
        .clone();

    if project_name.is_empty() {
        return Err("项目名称为空，请先选择项目".to_string());
    }

    // 获取项目根目录
    let current_dir =
        std::env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))?;
    let project_root = current_dir
        .parent()
        .ok_or_else(|| "Failed to get project root directory".to_string())?;

    // 构建项目路径
    let project_path = project_root.join("projects").join(&project_name);

    if !project_path.exists() {
        return Err(format!("项目目录不存在: {}", project_path.display()));
    }

    // 递归获取所有文件
    let mut files = Vec::new();
    collect_project_files(&project_path, &project_path, &mut files)?;

    // 过滤文件类型，只保留图片、音频等资源文件
    let filtered_files: Vec<String> = files
        .into_iter()
        .filter(|file| {
            let ext = std::path::Path::new(file)
                .extension()
                .and_then(|s| s.to_str())
                .unwrap_or("")
                .to_lowercase();

            // 只保留常见的资源文件类型
            matches!(
                ext.as_str(),
                "png" | "jpg" | "jpeg" | "gif" | "bmp" | "webp" |  // 图片
                "mp3" | "wav" | "ogg" | "m4a" |                    // 音频
                "mp4" | "webm" | "ogv" |                           // 视频
                "json" | "txt" | "js" // 数据文件
            )
        })
        .collect();

    Ok(filtered_files)
}

// 递归收集项目文件
fn collect_project_files(
    current_path: &std::path::Path,
    project_root: &std::path::Path,
    files: &mut Vec<String>,
) -> Result<(), String> {
    let entries = fs::read_dir(current_path)
        .map_err(|e| format!("无法读取目录 {}: {}", current_path.display(), e))?;

    for entry in entries {
        let entry = entry.map_err(|e| format!("读取目录项失败: {}", e))?;
        let path = entry.path();

        if path.is_dir() {
            // 跳过一些不需要的目录
            if let Some(dir_name) = path.file_name().and_then(|s| s.to_str()) {
                if matches!(dir_name, "node_modules" | ".git" | "save" | "movies") {
                    continue;
                }
            }
            // 递归处理子目录
            collect_project_files(&path, project_root, files)?;
        } else if path.is_file() {
            // 获取相对于项目根目录的路径
            if let Ok(relative_path) = path.strip_prefix(project_root) {
                if let Some(path_str) = relative_path.to_str() {
                    files.push(path_str.replace("\\", "/"));
                }
            }
        }
    }

    Ok(())
}

// 读取文件为字节数组
#[tauri::command]
fn read_file_as_bytes(file_path: String) -> Result<Vec<u8>, String> {
    println!("读取文件: {}", file_path);

    // 读取文件内容
    match fs::read(&file_path) {
        Ok(bytes) => {
            println!("文件读取成功，大小: {} bytes", bytes.len());
            Ok(bytes)
        }
        Err(e) => {
            let error_msg = format!("读取文件失败: {}", e);
            println!("{}", error_msg);
            Err(error_msg)
        }
    }
}

fn main() {
    // 创建项目名称状态
    let project_name = std::sync::Mutex::new(String::new());

    // 创建历史记录管理器，最大保存50条记录
    let history_manager = history::HistoryManager(Mutex::new(history::HistoryState::new(50)));

    tauri::Builder::default()
        .manage(project_name)
        .manage(history_manager)
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_opener::init())
        // 添加应用退出事件处理
        .on_window_event(move |window, event| {
            if let tauri::WindowEvent::CloseRequested { api, .. } = event {
                // 获取当前项目名称
                let state_manager = window.state::<Mutex<String>>();
                let mut project_name = String::new();

                // 使用单独的作用域来确保锁在使用后立即释放
                {
                    if let Ok(state) = state_manager.lock() {
                        project_name = state.clone();
                    }
                }

                // 如果有项目打开，则在退出前将临时插件复制到项目目录
                if !project_name.is_empty() {
                    println!("应用退出，正在保存项目: {}", project_name);

                    // 尝试复制临时插件到项目目录
                    if let Err(e) = temp_plugins::copy_temp_plugins_to_project(
                        &window.app_handle(),
                        &project_name,
                    ) {
                        println!("退出时保存项目失败: {}", e);
                    }
                }

                // 不阻止关闭，直接允许应用退出
                // 注意：不再调用 api.prevent_close() 和 window.close()
            }
        })
        .invoke_handler(tauri::generate_handler![
            read_data_file,
            write_data_file,
            // save::save_prototype_modifications,
            file_utils::list_directory_contents,
            // 项目管理相关命令
            project::get_project_list,
            project::import_project,
            project::update_project_last_opened,
            project::get_project_path,
            project::check_project_exists,
            project::delete_project,
            project::update_custom_resource_path,
            // 项目名称管理
            set_current_project,
            // 获取公共目录路径
            get_public_dir_path,
            // 文件选择相关命令
            get_project_files,
            read_file_as_bytes,
            // 属性修改相关命令 - 统一API
            save::api::entry::record_unified_modification,
            save::save::save_all_modifications,
            // 滤镜相关命令
            save::modification_manager::record_filter_param_modification,
            save::modification_manager::record_add_filter,
            save::modification_manager::record_remove_filter,
            save::modification_manager::record_reorder_filters,
            // 历史记录相关命令
            history::add_operation,
            history::undo_operation,
            history::redo_operation,
            history::clear_history,
            history::get_history_state,
            history::can_undo,
            history::can_redo,
            // 临时插件相关命令
            temp_plugins::refresh_preview,
            temp_plugins::exit_project,
            // 资源管理相关命令
            asset_manager::get_project_assets,
            asset_manager::get_directory_assets,
            asset_manager::get_asset_info,
            // RPG Maker MZ 类型解析
            coreRPGtype::type_reader::get_rpg_types,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
// .run(tauri::generate_context!())
