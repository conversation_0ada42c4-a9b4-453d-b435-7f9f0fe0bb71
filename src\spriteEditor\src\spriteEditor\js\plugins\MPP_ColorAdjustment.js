//=============================================================================
// MPP_ColorAdjustment.js
//=============================================================================
// Copyright (c) 2024 Mokusei Penguin
// Released under the MIT license
// http://opensource.org/licenses/mit-license.php
//=============================================================================

/*:
 * @target MZ
 * @plugindesc 为游戏场景添加全局色彩调节效果。
 * <AUTHOR> Penguin
 * @url
 *
 * @help [version 1.0.0]
 * 此插件用于RPG Maker MZ。
 *
 * ※ 注意
 *  1. 本插件通过WebGL滤镜实现色彩调节，可能会影响游戏性能。
 *  2. 部分效果在不同设备上可能会有细微差异。
 *  3. 建议根据游戏风格适度调整参数，过度使用可能影响视觉体验。
 *
 * ▼ 插件指令
 *  〇 adjustColor
 *   - 动态调整场景的色彩参数。
 *
 *  〇 resetColor
 *   - 重置所有色彩参数为默认值。
 *
 * ================================
 * Mail : wood_penguin＠yahoo.co.jp (＠是半角)
 * Blog : http://woodpenguin.blog.fc2.com/
 * License : MIT license
 *
 *  @command adjustColor
 *      @desc 调整场景色彩
 *      @arg brightness
 *          @text 亮度
 *          @desc -100:最暗 0:正常 100:最亮
 *          @type number
 *              @min -100
 *              @max 100
 *          @default 0
 *      @arg contrast
 *          @text 对比度
 *          @desc -100:最低 0:正常 100:最高
 *          @type number
 *              @min -100
 *              @max 100
 *          @default 0
 *      @arg saturation
 *          @text 饱和度
 *          @desc -100:灰度 0:正常 100:最饱和
 *          @type number
 *              @min -100
 *              @max 100
 *          @default 0
 *      @arg hue
 *          @text 色相
 *          @desc 0-360:色相角度
 *          @type number
 *              @min 0
 *              @max 360
 *          @default 0
 *      @arg vignette
 *          @text 暗角
 *          @desc 0:无 100:最强
 *          @type number
 *              @min 0
 *              @max 100
 *          @default 0
 *      @arg bloom
 *          @text 泛光
 *          @desc 0:无 100:最强
 *          @type number
 *              @min 0
 *              @max 100
 *          @default 0
 *      @arg duration
 *          @text 过渡时间
 *          @desc 效果过渡的帧数
 *          @type number
 *              @min 0
 *              @max 999
 *          @default 60
 *
 *  @command resetColor
 *      @desc 重置色彩参数
 *      @arg duration
 *          @text 过渡时间
 *          @desc 效果过渡的帧数
 *          @type number
 *              @min 0
 *              @max 999
 *          @default 60
 *
 *  @param Default Brightness
 *      @text 默认亮度
 *      @desc 场景的默认亮度值
 *      @type number
 *          @min -100
 *          @max 100
 *      @default 0
 *
 *  @param Default Contrast
 *      @text 默认对比度
 *      @desc 场景的默认对比度值
 *      @type number
 *          @min -100
 *          @max 100
 *      @default 0
 *
 *  @param Default Saturation
 *      @text 默认饱和度
 *      @desc 场景的默认饱和度值
 *      @type number
 *          @min -100
 *          @max 100
 *      @default 0
 *
 *  @param Default Hue
 *      @text 默认色相
 *      @desc 场景的默认色相值
 *      @type number
 *          @min 0
 *          @max 360
 *      @default 0
 *
 *  @param Default Vignette
 *      @text 默认暗角
 *      @desc 场景的默认暗角强度
 *      @type number
 *          @min 0
 *          @max 100
 *      @default 0
 *
 *  @param Default Bloom
 *      @text 默认泛光
 *      @desc 场景的默认泛光强度
 *      @type number
 *          @min 0
 *          @max 100
 *      @default 0
 */

(() => {
  "use strict";

  const pluginName = "MPP_ColorAdjustment";

  // Plugin Parameters
  const parameters = PluginManager.parameters(pluginName);
  const param_DefaultBrightness = Number(parameters["Default Brightness"] || 0);
  const param_DefaultContrast = Number(parameters["Default Contrast"] || 0);
  const param_DefaultSaturation = Number(parameters["Default Saturation"] || 0);
  const param_DefaultHue = Number(parameters["Default Hue"] || 0);
  const param_DefaultVignette = Number(parameters["Default Vignette"] || 0);
  const param_DefaultBloom = Number(parameters["Default Bloom"] || 0);

  //-------------------------------------------------------------------------
  // ColorAdjustment
  //
  // 管理场景色彩调节的静态类

  function ColorAdjustment() {
    throw new Error("This is a static class");
  }

  ColorAdjustment.initMembers = function () {
    this._brightness = param_DefaultBrightness;
    this._contrast = param_DefaultContrast;
    this._saturation = param_DefaultSaturation;
    this._hue = param_DefaultHue;
    this._vignette = param_DefaultVignette;
    this._bloom = param_DefaultBloom;
    this._duration = 0;
    this._targetValues = {};
  };

  ColorAdjustment.update = function () {
    if (this._duration > 0) {
      this._duration--;
      const d = this._duration;
      const t = 1 - d / this._wholeDuration;
      for (const key in this._targetValues) {
        const target = this._targetValues[key];
        const current = this["_" + key];
        this["_" + key] = current + (target - current) * t;
      }
    }
  };

  ColorAdjustment.adjustColor = function (params, duration) {
    this._targetValues = {};
    for (const key in params) {
      if (params[key] !== undefined) {
        this._targetValues[key] = params[key];
      }
    }
    this._duration = duration;
    this._wholeDuration = duration;
  };

  ColorAdjustment.resetColor = function (duration) {
    const params = {
      brightness: param_DefaultBrightness,
      contrast: param_DefaultContrast,
      saturation: param_DefaultSaturation,
      hue: param_DefaultHue,
      vignette: param_DefaultVignette,
      bloom: param_DefaultBloom,
    };
    this.adjustColor(params, duration);
  };

  //-------------------------------------------------------------------------
  // ColorFilter
  //
  // 色彩调节滤镜

  class ColorFilter extends PIXI.Filter {
    constructor() {
      const vertexShader = `
                attribute vec2 aVertexPosition;
                attribute vec2 aTextureCoord;
                uniform mat3 projectionMatrix;
                varying vec2 vTextureCoord;
                void main(void) {
                    gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
                    vTextureCoord = aTextureCoord;
                }
            `;
      const fragmentShader = `
                varying vec2 vTextureCoord;
                uniform sampler2D uSampler;
                uniform float brightness;
                uniform float contrast;
                uniform float saturation;
                uniform float hue;
                uniform float vignette;
                uniform float bloom;

                vec3 rgb2hsv(vec3 c) {
                    vec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
                    vec4 p = mix(vec4(c.bg, K.wz), vec4(c.gb, K.xy), step(c.b, c.g));
                    vec4 q = mix(vec4(p.xyw, c.r), vec4(c.r, p.yzx), step(p.x, c.r));
                    float d = q.x - min(q.w, q.y);
                    float e = 1.0e-10;
                    return vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);
                }

                vec3 hsv2rgb(vec3 c) {
                    vec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
                    vec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);
                    return c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);
                }

                void main(void) {
                    vec4 color = texture2D(uSampler, vTextureCoord);
                    vec3 rgb = color.rgb;

                    // 亮度调节 - 使用更安全的计算方式
                    rgb = rgb + (brightness / 100.0);

                    // 对比度调节 - 确保不会导致全黑
                    float contrastFactor = max(0.1, 1.0 + contrast / 100.0);
                    rgb = (rgb - 0.5) * contrastFactor + 0.5;

                    // HSV色彩空间转换
                    vec3 hsv = rgb2hsv(rgb);

                    // 饱和度调节
                    hsv.y = clamp(hsv.y * (1.0 + saturation / 100.0), 0.0, 1.0);

                    // 色相调节
                    hsv.x = mod(hsv.x + hue / 360.0, 1.0);

                    // 转回RGB
                    rgb = hsv2rgb(hsv);

                    // 暗角效果
                    vec2 center = vec2(0.5, 0.5);
                    float dist = distance(vTextureCoord, center);
                    float vignetteAmount = 1.0 - (vignette / 100.0) * smoothstep(0.2, 0.8, dist);
                    rgb *= vignetteAmount;

                    // 泛光效果
                    float luminance = dot(rgb, vec3(0.2126, 0.7152, 0.0722));
                    vec3 bloomColor = rgb * smoothstep(0.5, 0.8, luminance);
                    rgb = mix(rgb, rgb + bloomColor, bloom / 100.0);

                    // 确保颜色值在有效范围内
                    rgb = clamp(rgb, 0.0, 1.0);
                    
                    gl_FragColor = vec4(rgb, color.a);
                }
            `;

      super(vertexShader, fragmentShader, {
        brightness: { type: "1f", value: param_DefaultBrightness },
        contrast: { type: "1f", value: param_DefaultContrast },
        saturation: { type: "1f", value: param_DefaultSaturation },
        hue: { type: "1f", value: param_DefaultHue },
        vignette: { type: "1f", value: param_DefaultVignette },
        bloom: { type: "1f", value: param_DefaultBloom },
      });

      this.enabled = true;
      this.autoFit = true;
    }

    updateUniforms() {
      if (!this.enabled) return;

      this.uniforms.brightness.value = ColorAdjustment._brightness;
      this.uniforms.contrast.value = ColorAdjustment._contrast;
      this.uniforms.saturation.value = ColorAdjustment._saturation;
      this.uniforms.hue.value = ColorAdjustment._hue;
      this.uniforms.vignette.value = ColorAdjustment._vignette;
      this.uniforms.bloom.value = ColorAdjustment._bloom;

      this.padding = Math.ceil(
        (Math.max(this.uniforms.vignette.value, this.uniforms.bloom.value) /
          100.0) *
          64
      );
    }

    setBlendColor(color) {
      // 实现空方法以兼容Scene_Base的updateColorFilter
    }

    apply(filterManager, input, output, clearMode, currentState) {
      if (this.enabled) {
        this.updateUniforms();
        super.apply(filterManager, input, output, clearMode, currentState);
      }
    }
  }

  //-------------------------------------------------------------------------
  // Scene_Base

  const _Scene_Base_initialize = Scene_Base.prototype.initialize;
  Scene_Base.prototype.initialize = function () {
    _Scene_Base_initialize.call(this);
    ColorAdjustment.initMembers();
    this.createColorFilter();
  };

  const _Scene_Base_createSpriteset = Scene_Base.prototype.createSpriteset;
  Scene_Base.prototype.createSpriteset = function () {
    _Scene_Base_createSpriteset.call(this);
    if (this._spriteset) {
      this._spriteset.filters = this._spriteset.filters || [];
      if (!this._spriteset.filters.includes(this._colorFilter)) {
        this._spriteset.filters.push(this._colorFilter);
      }
    }
  };

  Scene_Base.prototype.createColorFilter = function () {
    if (!this._colorFilter) {
      this._colorFilter = new ColorFilter();
      if (this._spriteset) {
        this._spriteset.filters = this._spriteset.filters || [];
        if (!this._spriteset.filters.includes(this._colorFilter)) {
          this._spriteset.filters.push(this._colorFilter);
        }
      } else {
        this.filters = this.filters || [];
        if (!this.filters.includes(this._colorFilter)) {
          this.filters.push(this._colorFilter);
        }
      }
    }
  };

  const _Scene_Base_update = Scene_Base.prototype.update;
  Scene_Base.prototype.update = function () {
    _Scene_Base_update.call(this);
    ColorAdjustment.update();
    if (this._colorFilter) {
      this._colorFilter.updateUniforms();
    }
  };

  //-------------------------------------------------------------------------
  // Plugin Commands

  PluginManager.registerCommand(pluginName, "adjustColor", (args) => {
    const params = {
      brightness: Number(args.brightness),
      contrast: Number(args.contrast),
      saturation: Number(args.saturation),
      hue: Number(args.hue),
      vignette: Number(args.vignette),
      bloom: Number(args.bloom),
    };
    ColorAdjustment.adjustColor(params, Number(args.duration));
  });

  PluginManager.registerCommand(pluginName, "resetColor", (args) => {
    ColorAdjustment.resetColor(Number(args.duration));
  });

  //-------------------------------------------------------------------------
  // Game_System

  const _Game_System_initialize = Game_System.prototype.initialize;
  Game_System.prototype.initialize = function () {
    _Game_System_initialize.call(this);
    ColorAdjustment.initMembers();
  };
})();
