/* 游戏引擎容器样式 */
.game-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: black;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 游戏画布样式 */
#gameCanvas {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  image-rendering: pixelated;
}

/* 错误信息样式 */
#errorPrinter {
  position: absolute;
  width: 80%;
  text-align: center;
  color: white;
  background-color: rgba(0, 0, 0, 0.5);
  margin: auto;
  padding: 10px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  border-radius: 5px;
  font-family: sans-serif;
}

#errorName {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

#errorMessage {
  font-size: 14px;
  margin-bottom: 10px;
}

/* 重试按钮样式 */
#retryButton {
  font-size: 16px;
  padding: 5px 10px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

#retryButton:hover {
  background-color: #45a049;
}

/* FPS计数器样式 */
#fpsCounterBox {
  position: absolute;
  top: 5px;
  right: 5px;
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 2px 5px;
  border-radius: 3px;
  z-index: 10;
  font-family: sans-serif;
  font-size: 12px;
  color: white;
}

#fpsCounterLabel {
  margin-right: 5px;
}

/* 视频元素样式 */
#gameVideo {
  position: absolute;
  margin: auto;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  z-index: 2;
}
