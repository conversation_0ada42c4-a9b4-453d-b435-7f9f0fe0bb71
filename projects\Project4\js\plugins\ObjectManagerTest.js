//=============================================================================
// ObjectManagerTest.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc ObjectManager插件的测试插件
 * <AUTHOR> Agent
 * @url https://github.com/yourusername/ObjectManager
 *
 * @help
 * ============================================================================
 * 介绍
 * ============================================================================
 *
 * 这个插件用于测试ObjectManager插件的功能。
 * 它会在地图场景中添加一个简单的测试界面，用于演示对象管理功能。
 *
 * 按下O键打开测试界面。
 *
 * 这只是一个演示插件，您可以根据需要修改或删除它。
 */

var Imported = Imported || {};
Imported.ObjectManagerTest = true;

// 确保ObjectManager插件已加载
if (!Imported.ObjectManager) {
    throw new Error("ObjectManagerTest需要ObjectManager插件。");
}

(function() {
    // 添加O键到输入映射
    const _Input_keyMapper = Input.keyMapper;
    Input.keyMapper = Object.assign({}, _Input_keyMapper, {
        79: 'o' // O键的键码是79
    });

    //-----------------------------------------------------------------------------
    // Window_ObjectManagerTest
    //
    // 对象管理器测试窗口

    function Window_ObjectManagerTest() {
        this.initialize.apply(this, arguments);
    }

    Window_ObjectManagerTest.prototype = Object.create(Window_Command.prototype);
    Window_ObjectManagerTest.prototype.constructor = Window_ObjectManagerTest;

    Window_ObjectManagerTest.prototype.initialize = function() {
        const x = (Graphics.boxWidth - this.windowWidth()) / 2;
        const y = (Graphics.boxHeight - this.windowHeight()) / 2;
        const rect = new Rectangle(x, y, this.windowWidth(), this.windowHeight());
        Window_Command.prototype.initialize.call(this, rect);
        this.openness = 0;
        this.open();
        this.activate();
        this.select(0);
    };

    Window_ObjectManagerTest.prototype.windowWidth = function() {
        return 400;
    };

    Window_ObjectManagerTest.prototype.windowHeight = function() {
        return this.fittingHeight(this.numVisibleRows());
    };

    Window_ObjectManagerTest.prototype.numVisibleRows = function() {
        return 8; // 命令数量
    };

    Window_ObjectManagerTest.prototype.makeCommandList = function() {
        this.addCommand("创建测试对象", "create");
        this.addCommand("查找并显示对象", "find");
        this.addCommand("修改对象属性", "modify");
        this.addCommand("添加子对象", "addChild");
        this.addCommand("显示子对象", "showChildren");
        this.addCommand("删除对象", "remove");
        this.addCommand("显示所有对象", "showAll");
        this.addCommand("关闭", "cancel");
    };

    //-----------------------------------------------------------------------------
    // Scene_Map
    //
    // 扩展Scene_Map以添加测试功能

    const _Scene_Map_update = Scene_Map.prototype.update;
    Scene_Map.prototype.update = function() {
        _Scene_Map_update.call(this);

        // 检测O键是否被按下
        if (Input.isTriggered('o') && this._active && !this._objectManagerTestWindow) {
            this.openObjectManagerTestWindow();
        }
    };

    Scene_Map.prototype.openObjectManagerTestWindow = function() {
        this._objectManagerTestWindow = new Window_ObjectManagerTest();
        this.addWindow(this._objectManagerTestWindow);
        this._objectManagerTestWindow.setHandler('create', this.onCreateObject.bind(this));
        this._objectManagerTestWindow.setHandler('find', this.onFindObject.bind(this));
        this._objectManagerTestWindow.setHandler('modify', this.onModifyObject.bind(this));
        this._objectManagerTestWindow.setHandler('addChild', this.onAddChildObject.bind(this));
        this._objectManagerTestWindow.setHandler('showChildren', this.onShowChildren.bind(this));
        this._objectManagerTestWindow.setHandler('remove', this.onRemoveObject.bind(this));
        this._objectManagerTestWindow.setHandler('showAll', this.onShowAllObjects.bind(this));
        this._objectManagerTestWindow.setHandler('cancel', this.onCloseObjectManagerTestWindow.bind(this));
    };

    Scene_Map.prototype.closeObjectManagerTestWindow = function() {
        if (this._objectManagerTestWindow) {
            this._objectManagerTestWindow.close();
            this._objectManagerTestWindow = null;
        }
    };

    Scene_Map.prototype.onCloseObjectManagerTestWindow = function() {
        this.closeObjectManagerTestWindow();
    };

    Scene_Map.prototype.onCreateObject = function() {
        // 创建一个测试对象
        ObjectManager.create('player', {
            name: '勇者',
            level: 1,
            hp: 100,
            mp: 50,
            position: { x: 10, y: 20 }
        });

        this.showMessage("创建了一个ID为'player'的对象");
        this._objectManagerTestWindow.activate();
    };

    Scene_Map.prototype.onFindObject = function() {
        // 查找测试对象
        const player = ObjectManager.find('player');
        if (player) {
            this.showMessage(`找到对象: ${JSON.stringify(player)}`);
        } else {
            this.showMessage("未找到ID为'player'的对象");
        }
        this._objectManagerTestWindow.activate();
    };

    Scene_Map.prototype.onModifyObject = function() {
        // 修改测试对象
        const result = ObjectManager.modify('player', {
            level: 2,
            hp: 120,
            position: { y: 30 }
        });

        if (result) {
            this.showMessage(`修改对象成功: ${JSON.stringify(result)}`);
        } else {
            this.showMessage("修改对象失败，对象可能不存在");
        }
        this._objectManagerTestWindow.activate();
    };

    Scene_Map.prototype.onAddChildObject = function() {
        // 添加子对象
        const sword = ObjectManager.addChild('player', 'sword', {
            name: '铁剑',
            attack: 10
        });

        if (sword) {
            this.showMessage(`添加子对象成功: ${JSON.stringify(sword)}`);
        } else {
            this.showMessage("添加子对象失败，父对象可能不存在");
        }
        this._objectManagerTestWindow.activate();
    };

    Scene_Map.prototype.onShowChildren = function() {
        // 显示子对象
        const children = ObjectManager.getChildren('player');
        if (children.length > 0) {
            this.showMessage(`子对象: ${JSON.stringify(children)}`);
        } else {
            this.showMessage("对象没有子对象或对象不存在");
        }
        this._objectManagerTestWindow.activate();
    };

    Scene_Map.prototype.onRemoveObject = function() {
        // 删除对象
        const result = ObjectManager.remove('sword');
        if (result) {
            this.showMessage("删除对象'sword'成功");
        } else {
            this.showMessage("删除对象失败，对象可能不存在");
        }
        this._objectManagerTestWindow.activate();
    };

    Scene_Map.prototype.onShowAllObjects = function() {
        // 显示所有对象
        const allObjects = ObjectManager.getAllObjects();
        const count = Object.keys(allObjects).length;
        if (count > 0) {
            this.showMessage(`共有${count}个对象: ${JSON.stringify(allObjects)}`);
        } else {
            this.showMessage("没有任何对象");
        }
        this._objectManagerTestWindow.activate();
    };

    Scene_Map.prototype.showMessage = function(message) {
        $gameMessage.add(message);
    };
})();
