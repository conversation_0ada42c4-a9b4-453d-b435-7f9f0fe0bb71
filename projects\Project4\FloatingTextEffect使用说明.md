# 悬浮文本效果插件使用说明

## 插件介绍

悬浮文本效果插件（FloatingTextEffect）允许您在游戏中显示悬浮文本效果，例如伤害数字、恢复数值、状态信息等。文本会从指定位置上升并逐渐消失，为游戏增添视觉效果。

## 安装方法

1. 将 `FloatingTextEffect.js` 文件放入您的项目的 `js/plugins` 目录中
2. 在 RPG Maker MZ 编辑器中打开插件管理器
3. 启用 FloatingTextEffect 插件
4. 根据需要调整插件参数

## 插件参数

插件提供以下可自定义参数：

- **默认持续时间**：悬浮文本显示的默认持续时间（帧数）
- **默认字体大小**：悬浮文本的默认字体大小
- **默认颜色**：悬浮文本的默认颜色（CSS格式）
- **默认描边颜色**：悬浮文本的默认描边颜色（CSS格式）
- **默认描边宽度**：悬浮文本的默认描边宽度
- **默认上升高度**：悬浮文本上升的默认高度（像素）

## 使用方法

### 在事件中使用

在事件的"脚本"命令中，您可以使用以下代码显示悬浮文本：

```javascript
FloatingTextEffect.show("恢复了50点HP!", 400, 300);
```

这将在坐标 (400, 300) 处显示文本 "恢复了50点HP!"，使用默认设置。

您也可以自定义显示效果：

```javascript
FloatingTextEffect.show("暴击！", 400, 300, {
  color: "#FF0000",
  duration: 90,
  fontSize: 32,
  outlineColor: "#000000",
  outlineWidth: 4,
  riseHeight: 60
});
```

### 在插件命令中使用（旧版RPG Maker MV风格）

在事件中，您可以使用以下插件命令：

```
ShowFloatingText 文本 x坐标 y坐标 [颜色] [持续时间] [字体大小]
```

例如：

```
ShowFloatingText "恢复了50点HP!" 400 300 #00FF00 120 28
```

### 在插件命令中使用（RPG Maker MZ风格）

在 RPG Maker MZ 中，您可以使用图形化的插件命令界面来设置悬浮文本效果。

## 示例用法

### 显示伤害数字

```javascript
// 在敌人位置显示伤害数字
const enemy = $gameTroop.members()[0];
const x = enemy.screenX();
const y = enemy.screenY();
FloatingTextEffect.show("125", x, y, {
  color: "#FF0000",
  fontSize: 28
});
```

### 显示状态信息

```javascript
// 在角色位置显示状态信息
const actor = $gameParty.members()[0];
const x = actor.screenX();
const y = actor.screenY();
FloatingTextEffect.show("中毒!", x, y, {
  color: "#800080",
  duration: 90
});
```

### 显示获得物品信息

```javascript
// 在屏幕中央显示获得物品信息
const x = Graphics.width / 2;
const y = Graphics.height / 2;
FloatingTextEffect.show("获得了宝剑!", x, y, {
  color: "#FFFF00",
  duration: 120,
  fontSize: 28
});
```

## 测试插件

项目中还包含一个测试插件 `FloatingTextEffectTest.js`，它会在地图场景中添加一个按键监听，当按下 T 键或点击屏幕时，会在随机位置显示一个随机颜色的悬浮文本。这个插件仅用于演示目的，您可以根据需要启用或禁用它。

## 注意事项

- 悬浮文本效果在战斗场景和地图场景中都可以使用
- 过多的悬浮文本同时显示可能会影响游戏性能
- 文本会自动居中显示，无需手动计算位置

## 兼容性

- 本插件兼容 RPG Maker MZ
- 本插件应该与大多数其他插件兼容

## 许可证

本插件采用 MIT 许可证。您可以自由使用、修改和分发本插件，包括用于商业项目。
