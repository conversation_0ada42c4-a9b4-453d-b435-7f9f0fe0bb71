{"$message_type":"diagnostic","message":"unused import: `entry::record_unified_modification`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\api\\mod.rs","byte_start":413,"byte_end":447,"line_start":15,"line_end":15,"column_start":9,"column_end":43,"is_primary":true,"text":[{"text":"pub use entry::record_unified_modification;","highlight_start":9,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\api\\mod.rs","byte_start":405,"byte_end":449,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":true,"text":[{"text":"pub use entry::record_unified_modification;","highlight_start":1,"highlight_end":45}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `entry::record_unified_modification`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\api\\mod.rs:15:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use entry::record_unified_modification;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::types`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\data\\mod.rs","byte_start":230,"byte_end":248,"line_start":9,"line_end":9,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"pub use crate::save::types;","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\data\\mod.rs","byte_start":0,"byte_end":250,"line_start":1,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"/// 数据管理模块","highlight_start":1,"highlight_end":11},{"text":"/// ","highlight_start":1,"highlight_end":5},{"text":"/// 分类：","highlight_start":1,"highlight_end":8},{"text":"/// 1. types - 数据结构定义","highlight_start":1,"highlight_end":22},{"text":"/// 2. modification_manager - 修改记录管理","highlight_start":1,"highlight_end":37},{"text":"/// 3. cache - 缓存管理（通过 utils::cache）","highlight_start":1,"highlight_end":37},{"text":"","highlight_start":1,"highlight_end":1},{"text":"// 重新导出现有的数据管理模块","highlight_start":1,"highlight_end":17},{"text":"pub use crate::save::types;","highlight_start":1,"highlight_end":28},{"text":"pub use crate::save::modification_manager;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::types`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\data\\mod.rs:9:9\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::types;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::modification_manager`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\data\\mod.rs","byte_start":258,"byte_end":291,"line_start":10,"line_end":10,"column_start":9,"column_end":42,"is_primary":true,"text":[{"text":"pub use crate::save::modification_manager;","highlight_start":9,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\data\\mod.rs","byte_start":250,"byte_end":293,"line_start":10,"line_end":10,"column_start":1,"column_end":44,"is_primary":true,"text":[{"text":"pub use crate::save::modification_manager;","highlight_start":1,"highlight_end":44}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::modification_manager`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\data\\mod.rs:10:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::modification_manager;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::plugin_generator`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\generators\\mod.rs","byte_start":317,"byte_end":346,"line_start":10,"line_end":10,"column_start":9,"column_end":38,"is_primary":true,"text":[{"text":"pub use crate::save::plugin_generator;","highlight_start":9,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\generators\\mod.rs","byte_start":0,"byte_end":348,"line_start":1,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"/// 代码生成器模块","highlight_start":1,"highlight_end":12},{"text":"/// ","highlight_start":1,"highlight_end":5},{"text":"/// 分类：","highlight_start":1,"highlight_end":8},{"text":"/// 1. plugin_generator - 主插件代码生成器","highlight_start":1,"highlight_end":35},{"text":"/// 2. object_lifecycle_generator - 对象生命周期代码生成器","highlight_start":1,"highlight_end":48},{"text":"/// 3. bitmap_generator - 位图相关代码生成器","highlight_start":1,"highlight_end":36},{"text":"/// 4. filter_generator - 滤镜相关代码生成器","highlight_start":1,"highlight_end":36},{"text":"","highlight_start":1,"highlight_end":1},{"text":"// 重新导出现有的生成器","highlight_start":1,"highlight_end":14},{"text":"pub use crate::save::plugin_generator;","highlight_start":1,"highlight_end":39},{"text":"pub use crate::save::object_lifecycle_generator;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::plugin_generator`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\generators\\mod.rs:10:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::plugin_generator;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::object_lifecycle_generator`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\generators\\mod.rs","byte_start":356,"byte_end":395,"line_start":11,"line_end":11,"column_start":9,"column_end":48,"is_primary":true,"text":[{"text":"pub use crate::save::object_lifecycle_generator;","highlight_start":9,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\generators\\mod.rs","byte_start":348,"byte_end":397,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use crate::save::object_lifecycle_generator;","highlight_start":1,"highlight_end":49},{"text":"pub use crate::save::bitmap_generator;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::object_lifecycle_generator`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\generators\\mod.rs:11:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::object_lifecycle_generator;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::bitmap_generator`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\generators\\mod.rs","byte_start":405,"byte_end":434,"line_start":12,"line_end":12,"column_start":9,"column_end":38,"is_primary":true,"text":[{"text":"pub use crate::save::bitmap_generator;","highlight_start":9,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\generators\\mod.rs","byte_start":397,"byte_end":436,"line_start":12,"line_end":13,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use crate::save::bitmap_generator;","highlight_start":1,"highlight_end":39},{"text":"pub use crate::save::filter_generator;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::bitmap_generator`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\generators\\mod.rs:12:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::bitmap_generator;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::filter_generator`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\generators\\mod.rs","byte_start":444,"byte_end":473,"line_start":13,"line_end":13,"column_start":9,"column_end":38,"is_primary":true,"text":[{"text":"pub use crate::save::filter_generator;","highlight_start":9,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\generators\\mod.rs","byte_start":436,"byte_end":475,"line_start":13,"line_end":13,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub use crate::save::filter_generator;","highlight_start":1,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::filter_generator`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\generators\\mod.rs:13:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::filter_generator;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::save`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\files\\mod.rs","byte_start":252,"byte_end":269,"line_start":10,"line_end":10,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"pub use crate::save::save;","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\files\\mod.rs","byte_start":0,"byte_end":271,"line_start":1,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"/// 文件处理模块","highlight_start":1,"highlight_end":11},{"text":"/// ","highlight_start":1,"highlight_end":5},{"text":"/// 分类：","highlight_start":1,"highlight_end":8},{"text":"/// 1. save - 保存操作主入口","highlight_start":1,"highlight_end":22},{"text":"/// 2. plugin_parser - 插件文件解析","highlight_start":1,"highlight_end":30},{"text":"/// 3. plugins_file - 插件文件操作","highlight_start":1,"highlight_end":29},{"text":"/// 4. path_utils - 路径处理工具","highlight_start":1,"highlight_end":27},{"text":"","highlight_start":1,"highlight_end":1},{"text":"// 重新导出现有的文件处理模块","highlight_start":1,"highlight_end":17},{"text":"pub use crate::save::save;","highlight_start":1,"highlight_end":27},{"text":"pub use crate::save::plugin_parser;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::save`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\files\\mod.rs:10:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::save;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::plugin_parser`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\files\\mod.rs","byte_start":279,"byte_end":305,"line_start":11,"line_end":11,"column_start":9,"column_end":35,"is_primary":true,"text":[{"text":"pub use crate::save::plugin_parser;","highlight_start":9,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\files\\mod.rs","byte_start":271,"byte_end":307,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use crate::save::plugin_parser;","highlight_start":1,"highlight_end":36},{"text":"pub use crate::save::plugins_file;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::plugin_parser`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\files\\mod.rs:11:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::plugin_parser;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::plugins_file`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\files\\mod.rs","byte_start":315,"byte_end":340,"line_start":12,"line_end":12,"column_start":9,"column_end":34,"is_primary":true,"text":[{"text":"pub use crate::save::plugins_file;","highlight_start":9,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\files\\mod.rs","byte_start":307,"byte_end":342,"line_start":12,"line_end":13,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use crate::save::plugins_file;","highlight_start":1,"highlight_end":35},{"text":"pub use crate::save::path_utils;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::plugins_file`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\files\\mod.rs:12:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::plugins_file;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::path_utils`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\files\\mod.rs","byte_start":350,"byte_end":373,"line_start":13,"line_end":13,"column_start":9,"column_end":32,"is_primary":true,"text":[{"text":"pub use crate::save::path_utils;","highlight_start":9,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\files\\mod.rs","byte_start":342,"byte_end":375,"line_start":13,"line_end":13,"column_start":1,"column_end":34,"is_primary":true,"text":[{"text":"pub use crate::save::path_utils;","highlight_start":1,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::path_utils`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\files\\mod.rs:13:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::path_utils;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::modification_manager::record_property_modification`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\save.rs","byte_start":37,"byte_end":100,"line_start":2,"line_end":2,"column_start":9,"column_end":72,"is_primary":true,"text":[{"text":"pub use crate::save::modification_manager::record_property_modification;","highlight_start":9,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\save.rs","byte_start":29,"byte_end":103,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use crate::save::modification_manager::record_property_modification;","highlight_start":1,"highlight_end":73},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::modification_manager::record_property_modification`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\save.rs:2:9\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::save::modification_manager::record_property_modification;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `merge_modifications`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\save.rs","byte_start":172,"byte_end":191,"line_start":4,"line_end":4,"column_start":68,"column_end":87,"is_primary":true,"text":[{"text":"use crate::save::modification_manager::{get_current_modifications, merge_modifications};","highlight_start":68,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\save\\save.rs","byte_start":170,"byte_end":191,"line_start":4,"line_end":4,"column_start":66,"column_end":87,"is_primary":true,"text":[{"text":"use crate::save::modification_manager::{get_current_modifications, merge_modifications};","highlight_start":66,"highlight_end":87}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\save\\save.rs","byte_start":144,"byte_end":145,"line_start":4,"line_end":4,"column_start":40,"column_end":41,"is_primary":true,"text":[{"text":"use crate::save::modification_manager::{get_current_modifications, merge_modifications};","highlight_start":40,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\save\\save.rs","byte_start":191,"byte_end":192,"line_start":4,"line_end":4,"column_start":87,"column_end":88,"is_primary":true,"text":[{"text":"use crate::save::modification_manager::{get_current_modifications, merge_modifications};","highlight_start":87,"highlight_end":88}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `merge_modifications`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\save.rs:4:68\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::save::modification_manager::{get_current_modifications, merge_modifications};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::save::plugin_parser::parse_existing_modifications`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\save.rs","byte_start":282,"byte_end":338,"line_start":6,"line_end":6,"column_start":5,"column_end":61,"is_primary":true,"text":[{"text":"use crate::save::plugin_parser::parse_existing_modifications;","highlight_start":5,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\save\\save.rs","byte_start":278,"byte_end":341,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::save::plugin_parser::parse_existing_modifications;","highlight_start":1,"highlight_end":62},{"text":"use crate::utils::{cache, logging, path, project_state};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::save::plugin_parser::parse_existing_modifications`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\save.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::save::plugin_parser::parse_existing_modifications;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused macro definition: `format_error`","code":{"code":"unused_macros","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils.rs","byte_start":1450,"byte_end":1462,"line_start":45,"line_end":45,"column_start":18,"column_end":30,"is_primary":true,"text":[{"text":"    macro_rules! format_error {","highlight_start":18,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_macros)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused macro definition: `format_error`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils.rs:45:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m45\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    macro_rules! format_error {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_macros)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `format_error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils.rs","byte_start":1580,"byte_end":1592,"line_start":51,"line_end":51,"column_start":20,"column_end":32,"is_primary":true,"text":[{"text":"    pub(crate) use format_error;","highlight_start":20,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\utils.rs","byte_start":1565,"byte_end":1593,"line_start":51,"line_end":51,"column_start":5,"column_end":33,"is_primary":true,"text":[{"text":"    pub(crate) use format_error;","highlight_start":5,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `format_error`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils.rs:51:20\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub(crate) use format_error;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `app_handle`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\asset_manager.rs","byte_start":1697,"byte_end":1707,"line_start":54,"line_end":54,"column_start":25,"column_end":35,"is_primary":true,"text":[{"text":"fn get_project_root_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {","highlight_start":25,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\asset_manager.rs","byte_start":1697,"byte_end":1707,"line_start":54,"line_end":54,"column_start":25,"column_end":35,"is_primary":true,"text":[{"text":"fn get_project_root_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {","highlight_start":25,"highlight_end":35}],"label":null,"suggested_replacement":"_app_handle","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `app_handle`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\asset_manager.rs:54:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn get_project_root_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_app_handle`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `app_handle`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\project.rs","byte_start":9698,"byte_end":9708,"line_start":323,"line_end":323,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    app_handle: AppHandle,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\project.rs","byte_start":9698,"byte_end":9708,"line_start":323,"line_end":323,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    app_handle: AppHandle,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_app_handle","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `app_handle`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\project.rs:323:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m323\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    app_handle: AppHandle,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_app_handle`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `app_handle`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\api\\handlers\\type_handlers.rs","byte_start":5569,"byte_end":5579,"line_start":187,"line_end":187,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    app_handle: &AppHandle,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\save\\api\\handlers\\type_handlers.rs","byte_start":5569,"byte_end":5579,"line_start":187,"line_end":187,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    app_handle: &AppHandle,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_app_handle","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `app_handle`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\api\\handlers\\type_handlers.rs:187:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m187\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    app_handle: &AppHandle,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_app_handle`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `key`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\filter_generator.rs","byte_start":2025,"byte_end":2028,"line_start":66,"line_end":66,"column_start":10,"column_end":13,"is_primary":true,"text":[{"text":"    for (key, value) in operations {","highlight_start":10,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\save\\filter_generator.rs","byte_start":2025,"byte_end":2028,"line_start":66,"line_end":66,"column_start":10,"column_end":13,"is_primary":true,"text":[{"text":"    for (key, value) in operations {","highlight_start":10,"highlight_end":13}],"label":null,"suggested_replacement":"_key","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `key`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\filter_generator.rs:66:10\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for (key, value) in operations {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_key`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `parent_path`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\object_lifecycle_generator.rs","byte_start":8481,"byte_end":8492,"line_start":230,"line_end":230,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"    let parent_path = creation_info.get(\"parentPath\").and_then(|v| v.as_array());","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\save\\object_lifecycle_generator.rs","byte_start":8481,"byte_end":8492,"line_start":230,"line_end":230,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"    let parent_path = creation_info.get(\"parentPath\").and_then(|v| v.as_array());","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":"_parent_path","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `parent_path`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\object_lifecycle_generator.rs:230:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m230\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let parent_path = creation_info.get(\"parentPath\").and_then(|v| v.as_array());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_parent_path`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `project_name`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\plugin_generator.rs","byte_start":3239,"byte_end":3251,"line_start":93,"line_end":93,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    let project_name = state_manager","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\save\\plugin_generator.rs","byte_start":3239,"byte_end":3251,"line_start":93,"line_end":93,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    let project_name = state_manager","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"_project_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `project_name`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\plugin_generator.rs:93:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m93\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let project_name = state_manager\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_project_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `value_str`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\plugin_generator.rs","byte_start":35685,"byte_end":35694,"line_start":1031,"line_end":1031,"column_start":21,"column_end":30,"is_primary":true,"text":[{"text":"                let value_str = serde_json::to_string(value).unwrap_or_else(|_| \"null\".to_string());","highlight_start":21,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\save\\plugin_generator.rs","byte_start":35685,"byte_end":35694,"line_start":1031,"line_end":1031,"column_start":21,"column_end":30,"is_primary":true,"text":[{"text":"                let value_str = serde_json::to_string(value).unwrap_or_else(|_| \"null\".to_string());","highlight_start":21,"highlight_end":30}],"label":null,"suggested_replacement":"_value_str","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `value_str`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\plugin_generator.rs:1031:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1031\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let value_str = serde_json::to_string(value).unwrap_or_else(|_| \"null\".to_string());\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_value_str`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `existing_content`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\save.rs","byte_start":2310,"byte_end":2326,"line_start":60,"line_end":60,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"    let existing_content = if plugin_file_path.exists() {","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\save\\save.rs","byte_start":2310,"byte_end":2326,"line_start":60,"line_end":60,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"    let existing_content = if plugin_file_path.exists() {","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":"_existing_content","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `existing_content`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\save.rs:60:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m60\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let existing_content = if plugin_file_path.exists() {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_existing_content`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `app_handle`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\temp_plugins.rs","byte_start":164,"byte_end":174,"line_start":8,"line_end":8,"column_start":29,"column_end":39,"is_primary":true,"text":[{"text":"pub fn get_temp_plugins_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {","highlight_start":29,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\temp_plugins.rs","byte_start":164,"byte_end":174,"line_start":8,"line_end":8,"column_start":29,"column_end":39,"is_primary":true,"text":[{"text":"pub fn get_temp_plugins_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {","highlight_start":29,"highlight_end":39}],"label":null,"suggested_replacement":"_app_handle","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `app_handle`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\temp_plugins.rs:8:29\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn get_temp_plugins_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_app_handle`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `app_handle`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\temp_plugins.rs","byte_start":6236,"byte_end":6246,"line_start":170,"line_end":170,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    app_handle: &AppHandle,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\temp_plugins.rs","byte_start":6236,"byte_end":6246,"line_start":170,"line_end":170,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    app_handle: &AppHandle,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_app_handle","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `app_handle`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\temp_plugins.rs:170:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m170\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    app_handle: &AppHandle,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_app_handle`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `api`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":11083,"byte_end":11086,"line_start":336,"line_end":336,"column_start":57,"column_end":60,"is_primary":true,"text":[{"text":"            if let tauri::WindowEvent::CloseRequested { api, .. } = event {","highlight_start":57,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try removing the field","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":11083,"byte_end":11087,"line_start":336,"line_end":336,"column_start":57,"column_end":61,"is_primary":true,"text":[{"text":"            if let tauri::WindowEvent::CloseRequested { api, .. } = event {","highlight_start":57,"highlight_end":61}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `api`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:336:57\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m336\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            if let tauri::WindowEvent::CloseRequested { api, .. } = event {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: try removing the field\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated function `new` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\coreRPGtype\\readJs.rs","byte_start":350,"byte_end":364,"line_start":15,"line_end":15,"column_start":1,"column_end":15,"is_primary":false,"text":[{"text":"impl ClassInfo {","highlight_start":1,"highlight_end":15}],"label":"associated function in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\coreRPGtype\\readJs.rs","byte_start":379,"byte_end":382,"line_start":16,"line_end":16,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(name: String, parent: Option<String>) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated function `new` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\coreRPGtype\\readJs.rs:16:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ClassInfo {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated function in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(name: String, parent: Option<String>) -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `read_js_files` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\coreRPGtype\\readJs.rs","byte_start":636,"byte_end":649,"line_start":26,"line_end":26,"column_start":14,"column_end":27,"is_primary":true,"text":[{"text":"pub async fn read_js_files() -> Result<(), Box<dyn std::error::Error>> {","highlight_start":14,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `read_js_files` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\coreRPGtype\\readJs.rs:26:14\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub async fn read_js_files() -> Result<(), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `parse_js_file` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\coreRPGtype\\readJs.rs","byte_start":5153,"byte_end":5166,"line_start":142,"line_end":142,"column_start":4,"column_end":17,"is_primary":true,"text":[{"text":"fn parse_js_file(content: &str) -> Result<HashMap<String, ClassInfo>, Box<dyn std::error::Error>> {","highlight_start":4,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `parse_js_file` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\coreRPGtype\\readJs.rs:142:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m142\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn parse_js_file(content: &str) -> Result<HashMap<String, ClassInfo>, Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `add_child_to_parent` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\coreRPGtype\\readJs.rs","byte_start":10380,"byte_end":10399,"line_start":275,"line_end":275,"column_start":4,"column_end":23,"is_primary":true,"text":[{"text":"fn add_child_to_parent(","highlight_start":4,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `add_child_to_parent` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\coreRPGtype\\readJs.rs:275:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn add_child_to_parent(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `add_child_to_parent_recursive` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\coreRPGtype\\readJs.rs","byte_start":11509,"byte_end":11538,"line_start":308,"line_end":308,"column_start":4,"column_end":33,"is_primary":true,"text":[{"text":"fn add_child_to_parent_recursive(","highlight_start":4,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `add_child_to_parent_recursive` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\coreRPGtype\\readJs.rs:308:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m308\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn add_child_to_parent_recursive(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `build_class_hierarchy` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\coreRPGtype\\readJs.rs","byte_start":12002,"byte_end":12023,"line_start":325,"line_end":325,"column_start":4,"column_end":25,"is_primary":true,"text":[{"text":"fn build_class_hierarchy(","highlight_start":4,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `build_class_hierarchy` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\coreRPGtype\\readJs.rs:325:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m325\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn build_class_hierarchy(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `build_inheritance_tree` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\coreRPGtype\\readJs.rs","byte_start":12832,"byte_end":12854,"line_start":347,"line_end":347,"column_start":4,"column_end":26,"is_primary":true,"text":[{"text":"fn build_inheritance_tree(all_classes: &HashMap<String, ClassInfo>) -> HashMap<String, ClassInfo> {","highlight_start":4,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `build_inheritance_tree` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\coreRPGtype\\readJs.rs:347:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m347\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn build_inheritance_tree(all_classes: &HashMap<String, ClassInfo>) -> HashMap<String, ClassInfo> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `build_class_tree` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\coreRPGtype\\readJs.rs","byte_start":14498,"byte_end":14514,"line_start":387,"line_end":387,"column_start":4,"column_end":20,"is_primary":true,"text":[{"text":"fn build_class_tree(","highlight_start":4,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `build_class_tree` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\coreRPGtype\\readJs.rs:387:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m387\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn build_class_tree(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `save_json` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\coreRPGtype\\readJs.rs","byte_start":15372,"byte_end":15381,"line_start":409,"line_end":409,"column_start":10,"column_end":19,"is_primary":true,"text":[{"text":"async fn save_json(","highlight_start":10,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `save_json` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\coreRPGtype\\readJs.rs:409:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m409\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn save_json(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"methods `current_operation`, `previous_operation`, and `next_operation` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\history.rs","byte_start":1035,"byte_end":1052,"line_start":42,"line_end":42,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"impl HistoryState {","highlight_start":1,"highlight_end":18}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\history.rs","byte_start":2441,"byte_end":2458,"line_start":80,"line_end":80,"column_start":12,"column_end":29,"is_primary":true,"text":[{"text":"    pub fn current_operation(&self) -> Option<&Operation> {","highlight_start":12,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\history.rs","byte_start":2750,"byte_end":2768,"line_start":89,"line_end":89,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn previous_operation(&self) -> Option<&Operation> {","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\history.rs","byte_start":3069,"byte_end":3083,"line_start":98,"line_end":98,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn next_operation(&self) -> Option<&Operation> {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: methods `current_operation`, `previous_operation`, and `next_operation` are never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\history.rs:80:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl HistoryState {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m80\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn current_operation(&self) -> Option<&Operation> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m89\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn previous_operation(&self) -> Option<&Operation> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn next_operation(&self) -> Option<&Operation> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `record_property_modification` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\modification_manager.rs","byte_start":310,"byte_end":338,"line_start":10,"line_end":10,"column_start":8,"column_end":36,"is_primary":true,"text":[{"text":"pub fn record_property_modification(","highlight_start":8,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `record_property_modification` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\modification_manager.rs:10:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn record_property_modification(\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `record_class_prototype_modification` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\modification_manager.rs","byte_start":2101,"byte_end":2136,"line_start":72,"line_end":72,"column_start":8,"column_end":43,"is_primary":true,"text":[{"text":"pub fn record_class_prototype_modification(","highlight_start":8,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `record_class_prototype_modification` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\modification_manager.rs:72:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn record_class_prototype_modification(\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `merge_modifications` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\modification_manager.rs","byte_start":5288,"byte_end":5307,"line_start":168,"line_end":168,"column_start":8,"column_end":27,"is_primary":true,"text":[{"text":"pub fn merge_modifications(","highlight_start":8,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `merge_modifications` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\modification_manager.rs:168:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn merge_modifications(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `generate_modification_code` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\plugin_generator.rs","byte_start":12132,"byte_end":12158,"line_start":364,"line_end":364,"column_start":4,"column_end":30,"is_primary":true,"text":[{"text":"fn generate_modification_code(m: &Modification) -> String {","highlight_start":4,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `generate_modification_code` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\plugin_generator.rs:364:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m364\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn generate_modification_code(m: &Modification) -> String {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `generate_scene_modification_code` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\plugin_generator.rs","byte_start":14950,"byte_end":14982,"line_start":464,"line_end":464,"column_start":4,"column_end":36,"is_primary":true,"text":[{"text":"fn generate_scene_modification_code(m: &Modification) -> String {","highlight_start":4,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `generate_scene_modification_code` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\plugin_generator.rs:464:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m464\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn generate_scene_modification_code(m: &Modification) -> String {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `generate_class_prototype_modification_code` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\plugin_generator.rs","byte_start":20955,"byte_end":20997,"line_start":634,"line_end":634,"column_start":4,"column_end":46,"is_primary":true,"text":[{"text":"fn generate_class_prototype_modification_code(modification: &Modification) -> String {","highlight_start":4,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `generate_class_prototype_modification_code` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\plugin_generator.rs:634:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m634\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn generate_class_prototype_modification_code(modification: &Modification) -> String {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `generate_single_property_prototype_modification` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\plugin_generator.rs","byte_start":21774,"byte_end":21821,"line_start":662,"line_end":662,"column_start":4,"column_end":51,"is_primary":true,"text":[{"text":"fn generate_single_property_prototype_modification(","highlight_start":4,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `generate_single_property_prototype_modification` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\plugin_generator.rs:662:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m662\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn generate_single_property_prototype_modification(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `generate_simple_class_prototype_modification_code` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\plugin_generator.rs","byte_start":27283,"byte_end":27332,"line_start":807,"line_end":807,"column_start":4,"column_end":53,"is_primary":true,"text":[{"text":"fn generate_simple_class_prototype_modification_code(modification: &Modification) -> String {","highlight_start":4,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `generate_simple_class_prototype_modification_code` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\plugin_generator.rs:807:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m807\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn generate_simple_class_prototype_modification_code(modification: &Modification) -> String {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `is_primitive_value` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\plugin_generator.rs","byte_start":44484,"byte_end":44502,"line_start":1278,"line_end":1278,"column_start":4,"column_end":22,"is_primary":true,"text":[{"text":"fn is_primitive_value(value: &serde_json::Value) -> bool {","highlight_start":4,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `is_primitive_value` is never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\plugin_generator.rs:1278:4\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1278\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn is_primitive_value(value: &serde_json::Value) -> bool {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `update_plugins_file` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\plugins_file.rs","byte_start":105,"byte_end":124,"line_start":6,"line_end":6,"column_start":8,"column_end":27,"is_primary":true,"text":[{"text":"pub fn update_plugins_file(app_handle: &AppHandle) -> Result<(), String> {","highlight_start":8,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `update_plugins_file` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\plugins_file.rs:6:8\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn update_plugins_file(app_handle: &AppHandle) -> Result<(), String> {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `create_empty_plugins_content` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\save\\plugins_file.rs","byte_start":7705,"byte_end":7733,"line_start":215,"line_end":215,"column_start":4,"column_end":32,"is_primary":true,"text":[{"text":"fn create_empty_plugins_content() -> String {","highlight_start":4,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `create_empty_plugins_content` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\save\\plugins_file.rs:215:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m215\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn create_empty_plugins_content() -> String {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type alias `Result` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils.rs","byte_start":202,"byte_end":208,"line_start":10,"line_end":10,"column_start":14,"column_end":20,"is_primary":true,"text":[{"text":"    pub type Result<T> = std::result::Result<T, AppError>;","highlight_start":14,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: type alias `Result` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils.rs:10:14\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub type Result<T> = std::result::Result<T, AppError>;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `file_exists` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils.rs","byte_start":4950,"byte_end":4961,"line_start":159,"line_end":159,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn file_exists(file_path: &PathBuf) -> bool {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `file_exists` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils.rs:159:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m159\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn file_exists(file_path: &PathBuf) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `validate_scene_path` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils.rs","byte_start":5972,"byte_end":5991,"line_start":196,"line_end":196,"column_start":12,"column_end":31,"is_primary":true,"text":[{"text":"    pub fn validate_scene_path(scene_path: &[String]) -> Result<(), String> {","highlight_start":12,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `validate_scene_path` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils.rs:196:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m196\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn validate_scene_path(scene_path: &[String]) -> Result<(), String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"module `coreRPGtype` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":249,"byte_end":260,"line_start":10,"line_end":10,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"mod coreRPGtype;","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(non_snake_case)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":249,"byte_end":260,"line_start":10,"line_end":10,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"mod coreRPGtype;","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":"core_rpgtype","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: module `coreRPGtype` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mmod coreRPGtype;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `core_rpgtype`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(non_snake_case)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"module `readJs` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\coreRPGtype\\mod.rs","byte_start":8,"byte_end":14,"line_start":1,"line_end":1,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"pub mod readJs;","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\coreRPGtype\\mod.rs","byte_start":8,"byte_end":14,"line_start":1,"line_end":1,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"pub mod readJs;","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":"read_js","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: module `readJs` should have a snake case name\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\coreRPGtype\\mod.rs:1:9\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod readJs;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `read_js`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"53 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 53 warnings emitted\u001b[0m\n\n"}
