/*:
 * @plugindesc 自定义资源路径插件 - 修改游戏资源的加载路径
 * <AUTHOR>
 *
 * @param BasePath
 * @text 基础路径
 * @desc 资源文件的基础路径
 * @default ../projects/Project3/
 *
 * @help
 * 这个插件允许修改RPG Maker MZ游戏资源的加载路径，
 * 使其可以从自定义位置加载资源文件。
 */

(() => {
  const pluginName = "CustomResourcePath";
  const parameters = PluginManager.parameters(pluginName);
  const basePath = parameters["BasePath"] || "../projects/Project4/";

  // 修改数据文件加载路径
  const _DataManager_loadDataFile = DataManager.loadDataFile;
  DataManager.loadDataFile = function (name, src) {
    const xhr = new XMLHttpRequest();
    const url = basePath + "data/" + src;
    window[name] = null;
    xhr.open("GET", url);
    xhr.overrideMimeType("application/json");
    xhr.onload = () => this.onXhrLoad(xhr, name, src, url);
    xhr.onerror = () => this.onXhrError(name, src, url);
    xhr.send();
  };

  // 修改图片加载路径 - 修复版本
  const _ImageManager_loadBitmap = ImageManager.loadBitmap;
  ImageManager.loadBitmap = function (folder, filename) {
    if (filename) {
      const url = basePath + folder + Utils.encodeURI(filename) + ".png";
      console.log('加载图片:', url);
      return this.loadBitmapFromUrl(url);
    } else {
      return this._emptyBitmap;
    }
  };

  // 修改音频加载路径
  const _AudioManager_createBuffer = AudioManager.createBuffer;
  AudioManager.createBuffer = function (folder, name) {
    const ext = this.audioFileExt();
    const url = basePath + "audio/" + folder + Utils.encodeURI(name) + ext;
    console.log('加载音频:', url);
    const buffer = new WebAudio(url);
    buffer.name = name;
    buffer.frameCount = Graphics.frameCount;

    return buffer;
  };

  // 修改效果文件加载路径 (如果存在EffectManager)
  if (typeof EffectManager !== 'undefined') {
    const _EffectManager_load = EffectManager.load;
    EffectManager.load = function (filename) {
      const url = basePath + "effects/" + filename;
      const effect = new Effect(url);
      return effect;
    };
  }

  // 修改视频加载路径
  const _Video_play = Video.play;
  Video.play = function (src) {
    const url = basePath + "movies/" + src;
    this._element.src = url;
    this._element.onended = this._onEnd.bind(this);
    this._element.onerror = this._onError.bind(this);
    this._element.play();
  };

  // 修改字体加载路径
  if (typeof FontManager !== 'undefined') {
    const _FontManager_load = FontManager.load;
    FontManager.load = function (family, filename) {
      if (filename) {
        const url = basePath + "fonts/" + filename;
        const font = new FontFace(family, "url(" + url + ")");
        return font.load().then(function (loadedFont) {
          document.fonts.add(loadedFont);
          return loadedFont;
        });
      } else {
        return null;
      }
    };
  }

  // 修改CSS中的字体URL
  document.addEventListener('DOMContentLoaded', function () {
    // 查找所有样式表
    for (let i = 0; i < document.styleSheets.length; i++) {
      try {
        const styleSheet = document.styleSheets[i];
        // 遍历样式规则
        const rules = styleSheet.cssRules || styleSheet.rules;
        if (!rules) continue;

        for (let j = 0; j < rules.length; j++) {
          const rule = rules[j];
          // 检查是否是字体规则
          if (rule.type === CSSRule.FONT_FACE_RULE) {
            const src = rule.style.getPropertyValue('src');
            if (src && src.includes('url(') && src.includes('/fonts/')) {
              // 替换字体URL
              const newSrc = src.replace(/url\(['"]?([^'")]+)['"]?\)/g, function (match, url) {
                if (url.startsWith('/fonts/')) {
                  return 'url(' + basePath + 'fonts/' + url.split('/fonts/')[1] + ')';
                }
                return match;
              });

              if (newSrc !== src) {
                rule.style.setProperty('src', newSrc);
                console.log('修改字体路径:', src, '->', newSrc);
              }
            }
          }
        }
      } catch (e) {
        // 跨域样式表可能会抛出安全错误，忽略它们
        console.log('无法处理样式表:', e);
      }
    }
  });

  console.log("CustomResourcePath插件已加载，基础路径：" + basePath);


  // 保存原始的坐标转换方法
  const _Graphics_pageToCanvasX = Graphics.pageToCanvasX;
  const _Graphics_pageToCanvasY = Graphics.pageToCanvasY;

  // 重写坐标转换方法,使用getBoundingClientRect获取准确位置
  Graphics.pageToCanvasX = function (x) {
    if (this._canvas) {
      const rect = this._canvas.getBoundingClientRect();
      return Math.round((x - rect.left) / this._realScale);
    }
    return 0;
  };

  Graphics.pageToCanvasY = function (y) {
    if (this._canvas) {
      const rect = this._canvas.getBoundingClientRect();
      return Math.round((y - rect.top) / this._realScale);
    }
    return 0;
  };

  // 重写isInsideCanvas方法,使用新的坐标计算
  const _Graphics_isInsideCanvas = Graphics.isInsideCanvas;
  Graphics.isInsideCanvas = function (x, y) {
    const rect = this._canvas.getBoundingClientRect();
    const canvasX = this.pageToCanvasX(x + rect.left);
    const canvasY = this.pageToCanvasY(y + rect.top);
    return canvasX >= 0 && canvasX < this._width &&
      canvasY >= 0 && canvasY < this._height;
  };

  // 使用更简单的方法：直接修改canvas的样式
  const _Scene_Base_start = Scene_Base.prototype.start;
  Scene_Base.prototype.start = function () {
    _Scene_Base_start.call(this);

    // 获取canvas元素
    const canvas = document.getElementById('gameCanvas');
    if (canvas) {
      // 确保canvas可以接收事件
      canvas.style.pointerEvents = 'auto';
      console.log('Canvas已设置为可点击');
    }
  };
})();