import React, { useState, useEffect } from 'react';
import { Box, Typography, Button, IconButton, Tooltip, Paper, Chip, Divider } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import RepeatIcon from '@mui/icons-material/Repeat';
import StopIcon from '@mui/icons-material/Stop';
import EditIcon from '@mui/icons-material/Edit';
import LoopIcon from '@mui/icons-material/Loop';
import RemoveCircleIcon from '@mui/icons-material/RemoveCircle';
import useProjectStore from '../../../store/Store';
import AnimationEditor from './AnimationEditor';
import SelectInput from '../../ui/SelectInput';
import { AnimationStackItem, AnimationStackConfig, AnimationPlayMode } from '../../../types/components/AnimationTypes';
import './AnimationPanel.css';

// 缓动函数选项 - 仅用于显示标签
const easingOptions = [
  { value: 'linear', label: '线性 (Linear)' },
  { value: 'easeInQuad', label: '二次方缓入 (Quad In)' },
  { value: 'easeOutQuad', label: '二次方缓出 (Quad Out)' },
  { value: 'easeInOutQuad', label: '二次方缓入缓出 (Quad InOut)' },
  { value: 'easeInCubic', label: '三次方缓入 (Cubic In)' },
  { value: 'easeOutCubic', label: '三次方缓出 (Cubic Out)' },
  { value: 'easeInOutCubic', label: '三次方缓入缓出 (Cubic InOut)' },
  { value: 'easeInElastic', label: '弹性缓入 (Elastic In)' },
  { value: 'easeOutElastic', label: '弹性缓出 (Elastic Out)' },
  { value: 'easeInOutElastic', label: '弹性缓入缓出 (Elastic InOut)' },
  { value: 'easeInBounce', label: '反弹缓入 (Bounce In)' },
  { value: 'easeOutBounce', label: '反弹缓出 (Bounce Out)' },
  { value: 'easeInOutBounce', label: '反弹缓入缓出 (Bounce InOut)' },
];

// 动画属性选项 - 仅用于显示标签
const propertyOptions = [
  { value: 'x', label: 'X 位置' },
  { value: 'y', label: 'Y 位置' },
  { value: 'alpha', label: '透明度' },
  { value: 'scale.x', label: 'X 缩放' },
  { value: 'scale.y', label: 'Y 缩放' },
  { value: 'rotation', label: '旋转' },
];



// 动画面板组件
const AnimationPanel: React.FC = () => {
  const selectedObject = useProjectStore((state) => state.selectedObject);
  const [animationStack, setAnimationStack] = useState<AnimationStackItem[]>([]);
  const [editingAnimation, setEditingAnimation] = useState<AnimationStackItem | null>(null);
  const [editorOpen, setEditorOpen] = useState<boolean>(false);
  const [stackConfig, setStackConfig] = useState<AnimationStackConfig>({
    playMode: 'once', // 默认单次播放
    playCount: 1 // 默认播放一次
  });
  const [currentPlayingIndex, setCurrentPlayingIndex] = useState<number>(-1);

  // 创建动画属性对象
  const createAnimationProps = (stackItem: AnimationStackItem): Record<string, any> => {
    console.log('创建动画属性对象:', stackItem.name, '属性数量:', stackItem.properties.length);

    const animProps: Record<string, any> = {};

    if (!stackItem.properties || stackItem.properties.length === 0) {
      console.warn('动画没有属性');
      return animProps;
    }

    stackItem.properties.forEach(prop => {
      if (!prop || typeof prop.property !== 'string') {
        console.warn('无效的属性:', prop);
        return;
      }

      const propValue = prop.relative ? `${prop.value > 0 ? '+' : ''}${prop.value}` : prop.value;
      console.log(`设置属性: ${prop.property} = ${propValue} (${prop.relative ? '相对值' : '绝对值'})`);

      // 处理嵌套属性，如 scale.x
      if (prop.property.includes('.')) {
        const [parent, child] = prop.property.split('.');
        if (!animProps[parent]) animProps[parent] = {};
        animProps[parent][child] = propValue;
      } else {
        animProps[prop.property] = propValue;
      }
    });

    console.log('最终动画属性对象:', animProps);
    return animProps;
  };

  // 播放单个动画
  const playAnimation = (stackItem: AnimationStackItem, onCompleteCallback?: () => void) => {
    console.log('开始播放动画:', stackItem.name, '选中对象:', selectedObject ? '存在' : '不存在', 'AnimationManager:', window.AnimationManager ? '存在' : '不存在');

    if (!selectedObject) {
      console.error('无法播放动画：对象不存在');
      return null;
    }

    if (!window.AnimationManager) {
      console.error('无法播放动画：AnimationManager不存在');
      // 尝试等待AnimationManager初始化
      setTimeout(() => {
        console.log('尝试重新播放动画...');
        if (window.AnimationManager) {
          const animation = playAnimation(stackItem, onCompleteCallback);
          if (animation) {
            // 更新动画栈
            setAnimationStack(prevStack =>
              prevStack.map(item =>
                item.id === stackItem.id ? { ...item, animation, active: true } : item
              )
            );
          }
        } else {
          console.error('AnimationManager仍然不可用');
        }
      }, 500);
      return null;
    }

    // 获取目标对象
    const targetObj = selectedObject;
    if (!targetObj) {
      console.error('无法获取目标对象');
      return null;
    }

    // 构建动画属性对象
    const animProps = createAnimationProps(stackItem);

    // 创建或获取动画栈
    let animStack;
    if (stackItem.targetType === 'self') {
      // 对当前对象应用动画
      animStack = window.AnimationManager.getStack(targetObj);
    } else {
      // 对子元素应用动画
      if (targetObj.children && targetObj.children.length > 0) {
        // 获取子元素延迟时间
        const delay = stackItem.childrenDelay || 0;

        // 为每个子元素创建动画栈
        const childStacks = [];
        const visibleChildren = targetObj.children.filter(child =>
          child && !child._hidden && child.visible !== false
        );

        visibleChildren.forEach((child, index) => {
          const childStack = window.AnimationManager.getStack(child);
          if (childStack) {
            childStack.clear(); // 清空现有动画
            childStack.add(animProps, stackItem.duration, stackItem.easing);
            childStack.setPlayMode(stackItem.playMode, stackItem.playCount);

            // 如果有延迟，则设置延迟启动
            if (delay > 0 && index > 0) {
              // 使用setTimeout延迟启动动画
              setTimeout(() => {
                childStack.play();
              }, delay * index);
            } else {
              // 立即启动动画
              childStack.play();
            }

            childStacks.push(childStack);
          }
        });

        // 返回第一个子元素的动画栈（为了兼容性）
        return childStacks.length > 0 ? childStacks[0] : null;
      } else {
        console.warn('对象没有子元素，无法应用动画');
        return null;
      }
    }

    if (!animStack) {
      console.error('无法创建动画栈');
      return null;
    }

    // 清空现有动画
    animStack.clear();

    // 添加新动画
    animStack.add(animProps, stackItem.duration, stackItem.easing);

    // 设置播放模式
    animStack.setPlayMode(stackItem.playMode, stackItem.playCount);

    // 设置回调函数
    if (onCompleteCallback) {
      // 保存原始的onComplete回调
      const originalOnComplete = animStack.animations[0]?.options?.onComplete;

      // 设置新的onComplete回调
      if (animStack.animations[0]) {
        animStack.animations[0].options.onComplete = () => {
          // 调用原始回调
          if (originalOnComplete) originalOnComplete();

          console.log(`动画 ${stackItem.name} 完成`);

          // 调用传入的回调
          onCompleteCallback();

          // 如果是栈播放模式且不是循环或来回模式，检查是否需要继续播放栈中的下一个动画
          if (stackConfig.playMode === 'once' && stackItem.playMode === 'once') {
            // 获取当前动画的索引
            const currentIndex = animationStack.findIndex(item => item.id === stackItem.id);
            if (currentIndex !== -1 && currentIndex < animationStack.length - 1) {
              // 播放下一个动画
              setTimeout(() => {
                playStackItem(currentIndex + 1);
              }, 50);
            }
          }
        };
      }
    }

    // 开始播放
    animStack.play();

    // 保存动画栈引用到栈项中
    stackItem.animationStack = animStack;

    return animStack;
  };

  // 从栈中移除动画
  const removeFromStack = (id: string) => {
    // 先停止动画
    const item = animationStack.find(item => item.id === id);
    if (item) {
      // 停止动画栈
      if (item.animationStack) {
        item.animationStack.stop();
      }

      // 兼容旧版动画对象
      if (item.animation && item.animation.stop) {
        item.animation.stop();
      }
    }

    // 从栈中移除
    setAnimationStack(prevStack => prevStack.filter(item => item.id !== id));
  };



  // 停止栈中的动画
  const stopStackAnimation = (id: string) => {
    // 获取动画项
    const item = animationStack.find(item => item.id === id);
    if (!item) return;

    // 停止动画栈
    if (item.animationStack) {
      item.animationStack.stop();
    }

    // 兼容旧版动画对象
    if (item.animation && item.animation.stop) {
      item.animation.stop();
    }

    // 更新动画栈
    setAnimationStack(prevStack =>
      prevStack.map(stackItem =>
        stackItem.id === id ? {
          ...stackItem,
          active: false,
          animation: undefined,
          animationStack: undefined
        } : stackItem
      )
    );
  };

  // 切换动画播放模式
  const togglePlayMode = (id: string) => {
    setAnimationStack(prevStack =>
      prevStack.map(item =>
        item.id === id ? {
          ...item,
          playMode: item.playMode === 'once' ? 'loop' : (item.playMode === 'loop' ? 'yoyo' : 'once')
        } : item
      )
    );
  };

  // 编辑栈中的动画
  const editStackAnimation = (id: string) => {
    const item = animationStack.find(item => item.id === id);
    if (!item) return;

    // 如果动画正在播放，先停止
    if (item.active) {
      // 停止动画栈
      if (item.animationStack) {
        item.animationStack.stop();
      }

      // 兼容旧版动画对象
      if (item.animation && item.animation.stop) {
        item.animation.stop();
      }
    }

    // 设置编辑状态
    setEditingAnimation(item);
    setEditorOpen(true);
  };

  // 打开动画编辑器创建新动画
  const openAnimationEditor = () => {
    setEditingAnimation(null);
    setEditorOpen(true);
  };

  // 关闭动画编辑器
  const closeAnimationEditor = () => {
    setEditorOpen(false);
    setEditingAnimation(null);
  };

  // 保存动画
  const handleSaveAnimation = (animation: Omit<AnimationStackItem, 'id' | 'active' | 'animation'>) => {
    if (editingAnimation) {
      // 更新现有动画
      setAnimationStack(prevStack =>
        prevStack.map(item =>
          item.id === editingAnimation.id ? {
            ...item,
            ...animation,
            active: false,
            animation: undefined,
            animationStack: undefined
          } : item
        )
      );
    } else {
      // 创建新动画
      const newStackItem: AnimationStackItem = {
        id: Date.now().toString(),
        ...animation,
        childrenDelay: animation.childrenDelay || 0,
        active: false
      };

      setAnimationStack(prevStack => [newStackItem, ...prevStack]);
    }
  };

  // 停止所有动画
  const stopAllAnimations = () => {
    animationStack.forEach(item => {
      // 停止动画栈
      if (item.animationStack) {
        item.animationStack.stop();
      }

      // 兼容旧版动画对象
      if (item.animation && item.animation.stop) {
        item.animation.stop();
      }
    });

    setAnimationStack(prevStack =>
      prevStack.map(item => ({
        ...item,
        active: false,
        animation: undefined,
        animationStack: undefined
      }))
    );

    setCurrentPlayingIndex(-1);
  };

  // 播放整个动画栈
  const playAnimationStack = () => {
    if (animationStack.length === 0) return;

    // 开始播放第一个动画
    // 注意：playStackItem 函数已经包含了停止所有动画的逻辑
    playStackItem(0);
  };

  // 播放栈中的特定项
  const playStackItem = (index: number, direction: 'forward' | 'backward' = 'forward') => {
    console.log(`播放栈项: 索引=${index}, 方向=${direction}, 播放模式=${stackConfig.playMode}`);

    // 停止所有当前动画，确保一次只播放一个动画
    stopAllAnimations();

    // 处理索引超出范围的情况
    if (index < 0 || index >= animationStack.length) {
      console.log(`索引超出范围: ${index}, 栈长度: ${animationStack.length}`);

      // 根据不同的播放模式处理
      switch (stackConfig.playMode) {
        case 'loop':
          console.log('循环播放模式: 从头开始');
          // 循环播放，从头开始
          setTimeout(() => {
            playStackItem(direction === 'forward' ? 0 : animationStack.length - 1);
          }, 100);
          break;
        case 'remove':
          console.log('播放后删除模式: 删除当前对象');
          // 删除当前对象
          if (selectedObject && selectedObject.parent) {
            selectedObject.parent.removeChild(selectedObject);
          }
          break;
        case 'once':
        default:
          console.log('单次播放模式: 结束');
          // 单次播放，结束
          setCurrentPlayingIndex(-1);
          break;
      }
      return;
    }

    setCurrentPlayingIndex(index);
    const item = animationStack[index];
    console.log(`播放动画: ${item.name}`);

    // 定义动画完成后的回调
    const onAnimationComplete = () => {
      console.log(`动画完成回调: ${item.name}`);

      // 标记当前动画为非活动
      setAnimationStack(prevStack =>
        prevStack.map((stackItem, idx) =>
          idx === index ? { ...stackItem, active: false } : stackItem
        )
      );

      // 如果是单个动画的非单次播放模式，不继续播放下一个
      if (item.playMode !== 'once') {
        console.log(`非单次播放模式: ${item.name}, 播放模式: ${item.playMode}`);
        // 注意：我们不在这里返回，因为循环逻辑已经在 playAnimation 中处理
        return;
      }

      // 检查是否是最后一个动画
      const isLastAnimation = index === animationStack.length - 1 || (direction === 'backward' && index === 0);
      console.log(`是否是最后一个动画: ${isLastAnimation}, 索引: ${index}, 栈长度: ${animationStack.length}`);

      // 如果是最后一个动画，根据播放模式处理
      if (isLastAnimation) {
        console.log(`处理最后一个动画, 播放模式: ${stackConfig.playMode}`);

        // 根据播放模式处理
        switch (stackConfig.playMode) {
          case 'loop':
            console.log('循环播放模式: 从头开始');
            // 循环播放，从头开始
            setTimeout(() => {
              playStackItem(direction === 'forward' ? 0 : animationStack.length - 1);
            }, 100);
            return;
          case 'remove':
            console.log('播放后删除模式: 删除当前对象');
            // 删除当前对象
            if (selectedObject && selectedObject.parent) {
              selectedObject.parent.removeChild(selectedObject);
            }
            return;
          case 'once':
          default:
            console.log('单次播放模式: 结束');
            // 单次播放，结束
            setCurrentPlayingIndex(-1);
            return;
        }
      }

      // 根据方向播放下一个动画
      const nextIndex = direction === 'forward' ? index + 1 : index - 1;
      console.log(`播放下一个动画: 索引=${nextIndex}`);
      setTimeout(() => {
        playStackItem(nextIndex, direction);
      }, 100);
    };

    // 播放当前动画，并传入完成回调
    console.log(`尝试播放动画: ${item.name}, 索引: ${index}, 播放模式: ${item.playMode}`);
    const animation = playAnimation(item, onAnimationComplete);

    if (animation) {
      console.log(`动画创建成功: ${item.name}`);
      // 更新动画栈
      setAnimationStack(prevStack =>
        prevStack.map((stackItem, idx) =>
          idx === index ? { ...stackItem, animation, active: true } : stackItem
        )
      );
    } else {
      console.warn(`动画创建失败: ${item.name}`);
      // 即使动画创建失败，也标记为活动状态，以便用户可以看到停止按钮
      // 这样用户可以点击停止按钮，然后再次尝试播放
      setAnimationStack(prevStack =>
        prevStack.map((stackItem, idx) =>
          idx === index ? { ...stackItem, active: true } : stackItem
        )
      );
    }
  };

  // 设置播放模式
  const setPlayMode = (mode: AnimationPlayMode) => {
    setStackConfig({
      playMode: mode,
      playCount: stackConfig.playCount
    });
  };




  return (
    <Box sx={{ p: 0.5 }} className="animation-panel-container">
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
        <Typography variant="subtitle1">动画面板</Typography>
        <Button
          variant="contained"
          color="primary"
          size="small"
          startIcon={<AddIcon />}
          onClick={openAnimationEditor}
        >
          添加
        </Button>
      </Box>

      {!selectedObject ? (
        <Typography color="text.secondary" variant="body2">请先选择一个对象</Typography>
      ) : (
        <>
          {/* 栈控制选项 */}
          <Box sx={{ mb: 1, display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center' }}>
            <Box sx={{ width: '100%', display: 'flex', alignItems: 'center', gap: 1 }}>
              <SelectInput
                label="播放模式"
                value={stackConfig.playMode}
                options={[
                  { value: 'once', label: '单次播放', icon: undefined },
                  { value: 'loop', label: '循环播放', icon: <LoopIcon fontSize="small" /> },
                  { value: 'remove', label: '播放后删除', icon: <RemoveCircleIcon fontSize="small" /> }
                ]}
                onChange={(value) => setPlayMode(value as AnimationPlayMode)}
                width="100%"
              />
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', width: '100%' }}>
              <Tooltip title="播放整个栈">
                <IconButton
                  size="small"
                  color="primary"
                  onClick={playAnimationStack}
                  disabled={animationStack.length === 0}
                >
                  <PlayArrowIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Tooltip title="停止所有动画">
                <IconButton
                  size="small"
                  color="error"
                  onClick={stopAllAnimations}
                  disabled={animationStack.length === 0}
                >
                  <StopIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          <Divider sx={{ mb: 1 }} />

          {/* 动画栈 */}
          {animationStack.length === 0 ? (
            <Typography color="text.secondary" variant="body2" sx={{ textAlign: 'center', mt: 1 }}>
              暂无动画，请点击"添加"按钮创建
            </Typography>
          ) : (
            <Box sx={{ maxHeight: 'calc(100vh - 150px)', overflow: 'auto' }}>
              {animationStack.map((item, index) => (
                <Paper
                  key={item.id}
                  elevation={1}
                  sx={{
                    p: 0.5,
                    mb: 0.5,
                    border: '1px solid',
                    borderColor: item.active ? 'primary.main' : (currentPlayingIndex === index ? 'secondary.main' : 'divider'),
                    borderRadius: 1
                  }}
                >
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="body2" fontWeight="bold" sx={{ fontSize: '0.8rem' }}>
                      {item.name}
                    </Typography>
                    <Box>
                      <Tooltip title="编辑">
                        <IconButton
                          size="small"
                          onClick={() => editStackAnimation(item.id)}
                          sx={{ padding: 0.5 }}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="删除">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => removeFromStack(item.id)}
                          sx={{ padding: 0.5 }}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>

                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 0.5 }}>
                    <Chip
                      label={`${item.duration}ms`}
                      size="small"
                      variant="outlined"
                      sx={{ height: '20px', fontSize: '0.7rem' }}
                    />
                    <Chip
                      label={easingOptions.find(e => e.value === item.easing)?.label.split(' ')[0] || item.easing}
                      size="small"
                      variant="outlined"
                      sx={{ height: '20px', fontSize: '0.7rem' }}
                    />
                    <Chip
                      label={item.targetType === 'self' ? '当前对象' : `子元素${item.childrenDelay > 0 ? ` (间隔${item.childrenDelay}ms)` : ''}`}
                      size="small"
                      variant="outlined"
                      sx={{ height: '20px', fontSize: '0.7rem' }}
                    />
                    <Chip
                      icon={<RepeatIcon sx={{ fontSize: '0.8rem' }} />}
                      label={item.playMode === 'once' ? '单次' :
                        (item.playMode === 'loop' ? '循环' : '来回') +
                        (item.playCount > 0 ? `(${item.playCount}次)` : '')}
                      size="small"
                      color={item.playMode !== 'once' ? 'primary' : 'default'}
                      variant="outlined"
                      onClick={() => togglePlayMode(item.id)}
                      sx={{ height: '20px', fontSize: '0.7rem', cursor: 'pointer' }}
                    />
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, maxWidth: 'calc(100% - 40px)' }}>
                      {item.properties.map((prop, idx) => (
                        <Chip
                          key={idx}
                          label={`${propertyOptions.find(p => p.value === prop.property)?.label}: ${prop.relative ? (prop.value > 0 ? '+' : '') + prop.value : prop.value}`}
                          size="small"
                          variant="outlined"
                          sx={{ height: '20px', fontSize: '0.7rem' }}
                        />
                      ))}
                    </Box>
                    <Box>
                      {!item.active ? (
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => playStackItem(index)}
                          sx={{ padding: 0.5 }}
                        >
                          <PlayArrowIcon fontSize="small" />
                        </IconButton>
                      ) : (
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => stopStackAnimation(item.id)}
                          sx={{ padding: 0.5 }}
                        >
                          <StopIcon fontSize="small" />
                        </IconButton>
                      )}
                    </Box>
                  </Box>
                </Paper>
              ))}
            </Box>
          )}

          {/* 动画编辑器弹窗 */}
          <AnimationEditor
            open={editorOpen}
            onClose={closeAnimationEditor}
            onSave={handleSaveAnimation}
            editingAnimation={editingAnimation}
          />
        </>
      )}
    </Box>
  );
};

export default AnimationPanel;
