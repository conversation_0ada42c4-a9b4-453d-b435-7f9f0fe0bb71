//=============================================================================
// FloatingTextEffectTest.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc 悬浮文本效果的测试插件
 * <AUTHOR> Agent
 * @url https://github.com/yourusername/FloatingTextEffect
 *
 * @help
 * ============================================================================
 * 介绍
 * ============================================================================
 * 
 * 这个插件用于测试FloatingTextEffect插件的功能。
 * 它会在地图场景中添加一个按键监听，当按下T键时，
 * 会在随机位置显示一个随机颜色的悬浮文本。
 * 
 * 这只是一个演示插件，您可以根据需要修改或删除它。
 */

var Imported = Imported || {};
Imported.FloatingTextEffectTest = true;

// 确保FloatingTextEffect插件已加载
if (!Imported.FloatingTextEffect) {
    throw new Error("FloatingTextEffectTest需要FloatingTextEffect插件。");
}

(function() {
    // 随机颜色生成函数
    function randomColor() {
        const r = Math.floor(Math.random() * 256).toString(16).padStart(2, '0');
        const g = Math.floor(Math.random() * 256).toString(16).padStart(2, '0');
        const b = Math.floor(Math.random() * 256).toString(16).padStart(2, '0');
        return '#' + r + g + b;
    }
    
    // 随机文本生成函数
    function randomText() {
        const texts = [
            "恢复了50点HP!",
            "暴击!",
            "闪避!",
            "获得100金币!",
            "等级提升!",
            "中毒!",
            "眩晕!",
            "防御提升!",
            "攻击提升!",
            "速度提升!"
        ];
        return texts[Math.floor(Math.random() * texts.length)];
    }
    
    // 扩展Scene_Map的update方法，添加按键监听
    const _Scene_Map_update = Scene_Map.prototype.update;
    Scene_Map.prototype.update = function() {
        _Scene_Map_update.call(this);
        
        // 检测T键是否被按下
        if (Input.isTriggered('t') || (TouchInput.isTriggered() && this._active)) {
            // 生成随机位置
            const x = Math.floor(Math.random() * Graphics.width);
            const y = Math.floor(Math.random() * Graphics.height);
            
            // 生成随机颜色
            const color = randomColor();
            
            // 生成随机文本
            const text = randomText();
            
            // 显示悬浮文本
            FloatingTextEffect.show(text, x, y, {
                color: color,
                duration: 60 + Math.floor(Math.random() * 60),
                fontSize: 20 + Math.floor(Math.random() * 20)
            });
        }
    };
    
    // 添加T键到输入映射
    const _Input_keyMapper = Input.keyMapper;
    Input.keyMapper = Object.assign({}, _Input_keyMapper, {
        84: 't' // T键的键码是84
    });
    
    // 在游戏启动时显示提示
    const _Scene_Map_start = Scene_Map.prototype.start;
    Scene_Map.prototype.start = function() {
        _Scene_Map_start.call(this);
        
        // 只在游戏首次启动时显示提示
        if (!$gameSystem._floatingTextTestShown) {
            $gameSystem._floatingTextTestShown = true;
            
            // 在屏幕中央显示提示
            const text = "按T键或点击屏幕测试悬浮文本效果";
            const x = Graphics.width / 2;
            const y = Graphics.height / 2;
            
            FloatingTextEffect.show(text, x, y, {
                color: "#FFFF00",
                duration: 180,
                fontSize: 28
            });
        }
    };
})();
