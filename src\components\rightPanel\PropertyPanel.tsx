import React from "react";
import { Box, Typography, Paper, Chip, Button } from "@mui/material";
import EditIcon from '@mui/icons-material/Edit';
import BaseInfoGroup from "../property/propertyGroup/baseInfoGroup";
import ImgInfoGroup from "../property/propertyGroup/imgInfoGroup";
import TextInfoGroup from "../property/propertyGroup/textInfoGroup";
import SpriteColorGroup from "../property/propertyGroup/spriteColorGroup";
import CollapsibleGroup from "../property/propertyGroup/CollapsibleGroup";
import SpriteEditorDialog from "../../spriteEditor/SpriteEditorDialog";
import FilterGroup from "../property/propertyGroup/filterGroup";
import { usePropertyPanel } from "../../hooks/usePropertyPanel";
import { usePropertyPanelUI } from "../../hooks/usePropertyPanelUI";
import { PropertyPanelService } from "../../services/PropertyPanelService";

const PropertyPanel: React.FC = () => {
  // 使用业务逻辑hooks
  const {
    selectedObject,
    selectedObjectsState,
    isClassSelected,
    spriteEditorOpen,
    setSpriteEditorOpen,
    handleSpriteEditorApply,
    getProjectResourcePath,
    forceUpdate,
  } = usePropertyPanel();

  // 使用UI状态hooks
  const {
    expandedGroups,
    toggleGroupExpanded,
  } = usePropertyPanelUI();

  // 调试输出
  if (selectedObject) {
    console.log("选中对象:", selectedObject);
  }

  // 如果选中的是类型而不是对象
  // if (isClassSelected) {
  //   return (
  //     <Box sx={{ p: 2 }}>
  //       <Typography variant="body1" color="primary">
  //         已选中类型: {selectedObjectsState.className}
  //       </Typography>
  //       <Chip
  //         label="类型"
  //         size="small"
  //         color="secondary"
  //         sx={{ mt: 1 }}
  //       />
  //       <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
  //         类型不支持直接编辑属性。请在对象树中选择具体对象进行编辑。
  //       </Typography>
  //     </Box>
  //   );
  // }

  // 如果没有选中对象，显示提示信息
  if (!selectedObject && selectedObjectsState.objects.length === 0) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography variant="body2" color="text.secondary">
          未选中任何对象
        </Typography>
      </Box>
    );
  }




  return (
    <Box sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
      {/* Sprite编辑器对话框 */}
      <SpriteEditorDialog
        open={spriteEditorOpen}
        onClose={() => setSpriteEditorOpen(false)}
        sprite={selectedObject}
        resourcePath={getProjectResourcePath()}
        onApply={handleSpriteEditorApply}
      />

      <Paper elevation={0} sx={{ p: 1, mb: 1 }}>
        {/* 基础信息组件 - 显示对象名称和位置 */}
        {PropertyPanelService.shouldShowPropertyGroup(selectedObject, 'baseInfo') && (
          <CollapsibleGroup
            title="基础信息"
            expanded={expandedGroups.baseInfo}
            onToggle={() => toggleGroupExpanded('baseInfo')}
          >
            <BaseInfoGroup
              selectedObject={selectedObject}
              forceUpdate={forceUpdate}
            />
          </CollapsibleGroup>
        )}

        {/* 图片信息组件 - 仅对图片 Sprite 类型显示 */}
        {PropertyPanelService.shouldShowPropertyGroup(selectedObject, 'imgInfo') && (
          <CollapsibleGroup
            title='图片信息'
            expanded={expandedGroups.imgInfo}
            onToggle={() => toggleGroupExpanded('imgInfo')}
          >
            <ImgInfoGroup
              selectedObject={selectedObject}
              forceUpdate={forceUpdate}
            />
          </CollapsibleGroup>
        )}

        {/* Sprite 颜色组件 - 仅对 Sprite 类型显示 */}
        {PropertyPanelService.shouldShowPropertyGroup(selectedObject, 'spriteColor') && (
          <CollapsibleGroup
            title="Sprite 颜色"
            expanded={expandedGroups.spriteColor}
            onToggle={() => toggleGroupExpanded('spriteColor')}
          >
            <SpriteColorGroup
              selectedObject={selectedObject}
              forceUpdate={forceUpdate}
            />
          </CollapsibleGroup>
        )}

        {/* 文字信息组件 - 优先显示带有 texts 数组的对象，其次是普通文字 Sprite */}
        {PropertyPanelService.shouldShowPropertyGroup(selectedObject, 'textInfo') && (
          <CollapsibleGroup
            title={
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                <Typography variant="body1">文字信息</Typography>
                <Button
                  startIcon={<EditIcon />}
                  size="small"
                  variant="outlined"
                  onClick={() => setSpriteEditorOpen(true)}
                  sx={{ ml: 1, minWidth: 'auto', p: '2px 8px' }}
                >
                  编辑
                </Button>
              </Box>
            }
            expanded={expandedGroups.textInfo}
            onToggle={() => toggleGroupExpanded('textInfo')}
          >
            <TextInfoGroup
              selectedObject={selectedObject}
              forceUpdate={forceUpdate}
            />
          </CollapsibleGroup>
        )}

        {/* 滤镜组件 - 对所有支持滤镜的对象显示 */}
        {PropertyPanelService.shouldShowPropertyGroup(selectedObject, 'filters') && (
          <CollapsibleGroup
            title="滤镜效果"
            expanded={expandedGroups.filters}
            onToggle={() => toggleGroupExpanded('filters')}
          >
            <FilterGroup
              selectedObject={selectedObject}
              forceUpdate={forceUpdate}
            />
          </CollapsibleGroup>
        )}
      </Paper>
    </Box>
  );
};

export default PropertyPanel;
