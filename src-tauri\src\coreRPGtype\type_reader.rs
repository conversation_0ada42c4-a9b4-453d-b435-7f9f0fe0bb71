use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use tauri::AppHandle;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ClassInfo {
    pub name: String,
    pub parent: Option<String>,
    pub children: HashMap<String, ClassInfo>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TypeTree {
    pub types: HashMap<String, ClassInfo>,
    pub file_classes: HashMap<String, Vec<String>>,
}

/// 读取coreRPGtype文件夹中的所有JSON文件，构建类型树
#[tauri::command]
pub fn get_rpg_types(_app_handle: AppHandle) -> Result<TypeTree, String> {
    let files = vec![
        "rmmz_core",
        "rmmz_managers",
        "rmmz_objects",
        "rmmz_scenes",
        "rmmz_sprites",
        "rmmz_windows",
    ];

    // 获取当前目录
    let current_dir =
        std::env::current_dir().map_err(|e| format!("无法获取当前工作目录: {}", e))?;

    // 查找coreRPGtype目录
    let mut core_dir = current_dir.clone();
    if !core_dir.ends_with("src-tauri") {
        core_dir = core_dir.join("src-tauri");
    }
    core_dir = core_dir.join("src").join("coreRPGtype");

    println!("Core directory: {:?}", core_dir);

    // 确保目录存在
    if !core_dir.exists() {
        return Err(format!("目录不存在: {:?}", core_dir));
    }

    // 存储所有类和它们所属的文件
    let mut all_classes: HashMap<String, ClassInfo> = HashMap::new();
    let mut file_classes: HashMap<String, Vec<String>> = HashMap::new();

    // 读取所有JSON文件
    for file_name in &files {
        let json_path = core_dir.join(format!("{}.json", file_name));
        println!("Reading JSON file: {:?}", json_path);

        if !json_path.exists() {
            println!("JSON file not found: {:?}", json_path);
            continue;
        }

        // 读取JSON文件
        let json_content = fs::read_to_string(&json_path)
            .map_err(|e| format!("读取文件失败 {:?}: {}", json_path, e))?;

        // 解析JSON内容
        let file_class_info: HashMap<String, ClassInfo> = serde_json::from_str(&json_content)
            .map_err(|e| format!("解析JSON失败 {:?}: {}", json_path, e))?;

        // 存储类名
        let class_names: Vec<String> = file_class_info.keys().cloned().collect();
        file_classes.insert((*file_name).to_string(), class_names);

        // 添加到全局集合
        for (class_name, class_info) in file_class_info {
            all_classes.insert(class_name, class_info);
        }
    }

    // 返回类型树
    Ok(TypeTree {
        types: all_classes,
        file_classes,
    })
}
