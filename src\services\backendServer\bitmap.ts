import { recordPropertyModification } from './BackendService';

// ==================== Bitmap相关方法 ====================

/**
 * Bitmap属性类型定义
 */
export type BitmapPropertyType =
  | 'texture'      // bitmap._url
  | 'elements'     // bitmap.elements (复杂bitmap)
  | 'font'         // fontSize, fontFace, textColor, outlineColor, outlineWidth, fontBold, fontItalic
  | 'textItem';    // text, x, y, maxWidth, lineHeight, align (elements数组中的文本项)

/**
 * Bitmap属性映射
 */
export const BITMAP_PROPERTY_MAP: Record<string, BitmapPropertyType> = {
  'bitmap._url': 'texture',
  'bitmap.elements': 'elements',
  'fontSize': 'font',
  'fontFace': 'font',
  'textColor': 'font',
  'outlineColor': 'font',
  'outlineWidth': 'font',
  'fontBold': 'font',
  'fontItalic': 'font',
  'text': 'textItem',
  'maxWidth': 'textItem',
  'lineHeight': 'textItem',
  'align': 'textItem'
};

/**
 * 字体全局属性列表
 */
export const FONT_PROPERTIES = ['fontSize', 'fontFace', 'textColor', 'outlineColor', 'outlineWidth', 'fontBold', 'fontItalic'];

/**
 * 文本项属性列表
 */
export const TEXT_ITEM_PROPERTIES = ['text', 'x', 'y', 'maxWidth', 'lineHeight', 'align'];

/**
 * 检查是否为Bitmap属性
 */
export const isBitmapProperty = (propertyName: string): boolean => {
  return propertyName in BITMAP_PROPERTY_MAP ||
    FONT_PROPERTIES.includes(propertyName) ||
    TEXT_ITEM_PROPERTIES.includes(propertyName);
};

/**
 * 获取Bitmap属性类型
 */
export const getBitmapPropertyType = (propertyName: string): BitmapPropertyType | null => {
  return BITMAP_PROPERTY_MAP[propertyName] || null;
};

/**
 * 处理Bitmap属性修改（前端对象更新）
 * @param objects 要修改的对象数组
 * @param propertyName 属性名称
 * @param value 新值
 * @returns 是否成功修改
 */
export const updateBitmapProperty = (objects: any[], propertyName: string, value: any): boolean => {
  if (!isBitmapProperty(propertyName)) {
    console.warn(`${propertyName} 不是Bitmap属性`);
    return false;
  }

  try {
    console.log(`更新Bitmap属性: ${propertyName} = ${value}`);

    // 获取第一个对象作为参考计算增量
    const firstObject = objects[0];
    if (!firstObject) return false;

    let deltaValue = value;

    // 对数值类型计算增量
    if (FONT_PROPERTIES.includes(propertyName) || TEXT_ITEM_PROPERTIES.includes(propertyName)) {
      const currentValue = getCurrentPropertyValue(firstObject, propertyName);
      if (typeof currentValue === 'number' && typeof value === 'number') {
        deltaValue = value - currentValue;
        console.log(`计算增量值: ${deltaValue} (${value} - ${currentValue})`);
      }
    }

    // 对每个对象应用修改
    for (const object of objects) {
      if (!object) continue;

      if (propertyName === 'bitmap._url') {
        // 处理纹理属性
        if (!handleSpriteTexture(object, value)) {
          console.error(`处理对象 ${object.constructor.name} 的纹理失败`);
        }
      } else if (FONT_PROPERTIES.includes(propertyName)) {
        // 处理字体全局属性
        if (typeof object[propertyName] === 'number' && typeof deltaValue === 'number') {
          object[propertyName] += deltaValue;
        } else {
          object[propertyName] = deltaValue;
        }
        console.log(`设置全局字体属性 ${propertyName} = ${object[propertyName]}`);
      } else if (TEXT_ITEM_PROPERTIES.includes(propertyName)) {
        // 处理文本项属性
        if (object._bitmap && object._bitmap.elements && Array.isArray(object._bitmap.elements)) {
          const textElement = object._bitmap.elements.find((element: any) => element.type === 'text');
          if (textElement) {
            if (typeof textElement[propertyName] === 'number' && typeof deltaValue === 'number') {
              textElement[propertyName] += deltaValue;
            } else {
              textElement[propertyName] = deltaValue;
            }
            console.log(`设置文本元素属性 ${propertyName} = ${textElement[propertyName]}`);

            // 触发重绘
            if (object._bitmap.redrawing && typeof object._bitmap.redrawing === 'function') {
              object._bitmap.redrawing();
            }
          } else {
            console.warn(`未找到文本元素，无法设置属性 ${propertyName}`);
          }
        }
      }
    }

    return true;
  } catch (error) {
    console.error(`更新Bitmap属性 ${propertyName} 失败:`, error);
    return false;
  }
};

/**
 * 记录Bitmap属性修改到后端
 * @param object 要修改的对象
 * @param propertyName 属性名称
 * @param value 新值
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export const recordBitmapPropertyModification = async (
  object: any,
  propertyName: string,
  value: any
): Promise<string> => {
  if (!isBitmapProperty(propertyName)) {
    throw new Error(`${propertyName} 不是Bitmap属性`);
  }

  const propertyType = getBitmapPropertyType(propertyName);
  console.log(`记录Bitmap属性修改: ${propertyName} (类型: ${propertyType}) = ${value}`);

  // 使用统一的后端记录方法
  // 注意：对于特殊的bitmap属性（如bitmap._url, bitmap.elements），
  // 后端会根据propertyName自动选择合适的处理方式
  return await recordPropertyModification(object, propertyName, value);
};

/**
 * 处理 Sprite 的纹理属性
 */
const handleSpriteTexture = (sprite: any, texturePath: string): boolean => {
  try {
    console.log(`处理 Sprite 纹理，路径: ${texturePath}`);

    const PIXI = (window as any).PIXI;
    if (!PIXI) {
      console.error('无法获取 PIXI');
      return false;
    }

    // 创建新的纹理
    const texture = PIXI.Texture.from(texturePath);

    // 设置纹理
    sprite._texture = texture;
    console.log('设置 _texture 属性');

    // 如果有 texture 属性，也设置它
    if (sprite.texture) {
      sprite.texture = texture;
      console.log('设置 texture 属性');
    }

    return true;
  } catch (error) {
    console.error('处理 Sprite 纹理失败:', error);
    return false;
  }
};

/**
 * 获取当前属性值
 */
const getCurrentPropertyValue = (object: any, propertyName: string): any => {
  if (FONT_PROPERTIES.includes(propertyName)) {
    return object[propertyName];
  } else if (TEXT_ITEM_PROPERTIES.includes(propertyName)) {
    if (object._bitmap && object._bitmap.elements && Array.isArray(object._bitmap.elements)) {
      const textElement = object._bitmap.elements.find((element: any) => element.type === 'text');
      return textElement ? textElement[propertyName] : undefined;
    }
  }
  return undefined;
};
