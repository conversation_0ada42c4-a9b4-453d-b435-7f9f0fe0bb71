/*:
 * @plugindesc 布局容器示例插件 - 展示LayoutContainer的使用方法
 * <AUTHOR>
 * @target MZ
 * @base LayoutContainer
 * @orderAfter LayoutContainer
 * @help
 * ============================================================================
 * 布局容器示例插件 v1.0.0
 * ============================================================================
 * 
 * 这个插件展示了如何使用LayoutContainer类创建灵活的UI布局。
 * 它会在标题界面和地图界面添加一些示例UI元素，展示不同的布局方式。
 * 
 * 注意：这个插件依赖于LayoutContainer插件，请确保先加载LayoutContainer插件。
 * 
 * ============================================================================
 */

(() => {
    'use strict';

    //-----------------------------------------------------------------------------
    // 示例1：扩展Scene_Title，添加使用不同布局的按钮组
    //-----------------------------------------------------------------------------
    
    const _Scene_Title_create = Scene_Title.prototype.create;
    Scene_Title.prototype.create = function() {
        _Scene_Title_create.call(this);
        this.createLayoutExamples();
    };
    
    Scene_Title.prototype.createLayoutExamples = function() {
        // 创建示例容器
        this.createRowLayoutExample();
        this.createColumnLayoutExample();
        this.createGridLayoutExample();
        this.createAlignmentExample();
    };
    
    /**
     * 创建行布局示例
     * 在屏幕底部创建一个水平排列的按钮组
     */
    Scene_Title.prototype.createRowLayoutExample = function() {
        // 创建一个LayoutContainer实例
        const container = new LayoutContainer();
        container.x = 0;
        container.y = Graphics.height - 100;
        container.width = Graphics.width;
        container.height = 50;
        
        // 创建几个按钮
        for (let i = 0; i < 5; i++) {
            const button = new Sprite();
            const bitmap = new Bitmap(80, 40);
            bitmap.fillRect(0, 0, 80, 40, 'rgba(0, 100, 200, 0.7)');
            bitmap.drawText(`按钮${i+1}`, 0, 0, 80, 40, 'center');
            button.bitmap = bitmap;
            container.addChild(button);
        }
        
        // 应用行布局，水平排列按钮
        container.applyLayout('row', {
            mainAxisAlignment: 'space-around', // 均匀分布
            crossAxisAlignment: 'center',      // 垂直居中
            padding: 10
        });
        
        this.addChild(container);
        this._rowLayoutExample = container;
    };
    
    /**
     * 创建列布局示例
     * 在屏幕右侧创建一个垂直排列的按钮组
     */
    Scene_Title.prototype.createColumnLayoutExample = function() {
        // 创建一个LayoutContainer实例
        const container = new LayoutContainer();
        container.x = Graphics.width - 100;
        container.y = 100;
        container.width = 90;
        container.height = 300;
        
        // 创建几个按钮
        for (let i = 0; i < 4; i++) {
            const button = new Sprite();
            const bitmap = new Bitmap(80, 40);
            bitmap.fillRect(0, 0, 80, 40, 'rgba(200, 100, 0, 0.7)');
            bitmap.drawText(`按钮${i+1}`, 0, 0, 80, 40, 'center');
            button.bitmap = bitmap;
            container.addChild(button);
        }
        
        // 应用列布局，垂直排列按钮
        container.applyLayout('column', {
            mainAxisAlignment: 'space-between', // 两端对齐
            crossAxisAlignment: 'center',       // 水平居中
            padding: 5
        });
        
        this.addChild(container);
        this._columnLayoutExample = container;
    };
    
    /**
     * 创建网格布局示例
     * 在屏幕左上角创建一个网格排列的图标组
     */
    Scene_Title.prototype.createGridLayoutExample = function() {
        // 创建一个LayoutContainer实例
        const container = new LayoutContainer();
        container.x = 20;
        container.y = 20;
        container.width = 200;
        container.height = 200;
        
        // 创建几个图标
        for (let i = 0; i < 9; i++) {
            const icon = new Sprite();
            const bitmap = new Bitmap(40, 40);
            bitmap.fillRect(0, 0, 40, 40, 'rgba(100, 200, 100, 0.7)');
            bitmap.drawText(`${i+1}`, 0, 0, 40, 40, 'center');
            icon.bitmap = bitmap;
            container.addChild(icon);
        }
        
        // 应用网格布局，3x3网格排列图标
        container.applyLayout('grid', {
            columns: 3,                    // 3列
            mainAxisAlignment: 'center',   // 居中对齐
            crossAxisAlignment: 'center',  // 居中对齐
            spacing: 10,                   // 间距10像素
            padding: 5                     // 内边距5像素
        });
        
        this.addChild(container);
        this._gridLayoutExample = container;
    };
    
    /**
     * 创建对齐布局示例
     * 在屏幕四个角落和中心创建对齐的元素
     */
    Scene_Title.prototype.createAlignmentExample = function() {
        // 创建四个角落的容器
        const createCornerContainer = (layoutType, x, y, color) => {
            const container = new LayoutContainer();
            container.x = x;
            container.y = y;
            container.width = 150;
            container.height = 100;
            
            // 创建一个元素
            const sprite = new Sprite();
            const bitmap = new Bitmap(60, 30);
            bitmap.fillRect(0, 0, 60, 30, color);
            bitmap.drawText(layoutType, 0, 0, 60, 30, 'center');
            sprite.bitmap = bitmap;
            container.addChild(sprite);
            
            // 应用布局
            container.applyLayout(layoutType, {
                padding: 10
            });
            
            this.addChild(container);
            return container;
        };
        
        // 创建四个角落和中心的容器
        this._topLeftExample = createCornerContainer('topLeft', 0, 0, 'rgba(255, 0, 0, 0.7)');
        this._topRightExample = createCornerContainer('topRight', Graphics.width - 150, 0, 'rgba(0, 255, 0, 0.7)');
        this._bottomLeftExample = createCornerContainer('bottomLeft', 0, Graphics.height - 100, 'rgba(0, 0, 255, 0.7)');
        this._bottomRightExample = createCornerContainer('bottomRight', Graphics.width - 150, Graphics.height - 100, 'rgba(255, 255, 0, 0.7)');
        
        // 创建中心容器
        const centerContainer = new LayoutContainer();
        centerContainer.x = Graphics.width / 2 - 75;
        centerContainer.y = Graphics.height / 2 - 50;
        centerContainer.width = 150;
        centerContainer.height = 100;
        
        const centerSprite = new Sprite();
        const centerBitmap = new Bitmap(60, 30);
        centerBitmap.fillRect(0, 0, 60, 30, 'rgba(255, 0, 255, 0.7)');
        centerBitmap.drawText('center', 0, 0, 60, 30, 'center');
        centerSprite.bitmap = centerBitmap;
        centerContainer.addChild(centerSprite);
        
        centerContainer.applyLayout('center', {
            padding: 10
        });
        
        this.addChild(centerContainer);
        this._centerExample = centerContainer;
    };
})();
