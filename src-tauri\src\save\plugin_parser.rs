use crate::save::types::{Modification, ModificationType, MODIFICATIONS_CACHE};
use std::collections::HashMap;

/// 解析现有修改
pub fn parse_existing_modifications(content: &str) -> Vec<Modification> {
    println!("解析现有修改...");

    // 检查缓存
    if let Some(cached) = MODIFICATIONS_CACHE.lock().unwrap().as_ref() {
        println!("使用缓存的修改，数量: {}", cached.len());
        return cached.clone();
    }

    // 简单的解析逻辑，提取修改信息
    let mut modifications = Vec::new();

    // 查找所有 scenePath_ 变量定义
    let lines: Vec<&str> = content.lines().collect();
    for (i, line) in lines.iter().enumerate() {
        if line.contains("const scenePath_") && line.contains(" = ") {
            // 提取场景路径
            if let Some(path_str) = extract_json_array(line) {
                if let Ok(scene_path) = serde_json::from_str::<Vec<String>>(path_str) {
                    // 查找类名
                    let mut class_name = String::from("Unknown");
                    if i + 3 < lines.len() {
                        if let Some(class_name_line) = lines.get(i + 3) {
                            if class_name_line.contains("constructor.name") {
                                if let Some(name) = extract_class_name(class_name_line) {
                                    class_name = name.to_string();
                                }
                            }
                        }
                    }

                    // 提取属性
                    let mut properties = HashMap::new();
                    let var_name = if let Some(name) = extract_var_name(line) {
                        name
                    } else {
                        continue;
                    };

                    // 查找属性设置
                    println!("查找变量 {} 的属性设置，从行 {} 开始", var_name, i + 4);
                    for j in i + 4..lines.len() {
                        let prop_line = lines[j];
                        println!("  检查行 {}: {}", j, prop_line.trim());

                        // 如果遇到下一个对象定义，结束当前对象的解析
                        if prop_line.contains("const scenePath_") && prop_line != *line {
                            println!("  遇到下一个对象定义，结束解析");
                            break;
                        }

                        // 如果遇到结束大括号，结束当前对象的解析
                        if prop_line.trim() == "}" || prop_line.trim() == "};" {
                            println!("  遇到结束大括号，结束解析");
                            break;
                        }

                        if prop_line.contains(&format!("targetObject_{}", var_name))
                            && prop_line.contains(" = ")
                        {
                            println!("  找到属性设置行: {}", prop_line.trim());
                            if let Some((prop_name, value)) = extract_property(prop_line, var_name)
                            {
                                println!("  提取到属性: {} = {}", prop_name, value);
                                if let Ok(json_value) = serde_json::from_str(&value) {
                                    println!("  成功添加属性: {} = {:?}", prop_name, json_value);
                                    properties.insert(prop_name, json_value);
                                } else {
                                    println!("  JSON解析失败: {}", value);
                                }
                            } else {
                                println!("  属性提取失败");
                            }
                        }
                    }

                    println!(
                        "变量 {} 解析完成，找到 {} 个属性",
                        var_name,
                        properties.len()
                    );

                    // 添加修改（默认为对象实例修改，保持向后兼容）
                    modifications.push(Modification {
                        class_name,
                        scene_path,
                        properties,
                        modification_type: ModificationType::ObjectInstance,
                    });
                }
            }
        }
    }

    println!("解析完成，找到 {} 个修改", modifications.len());

    // 更新缓存
    let mut cache = MODIFICATIONS_CACHE.lock().unwrap();
    *cache = Some(modifications.clone());

    modifications
}

/// 从字符串中提取 JSON 数组
fn extract_json_array(line: &str) -> Option<&str> {
    if let Some(start) = line.find('[') {
        if let Some(end) = line.rfind(']') {
            if end > start {
                return Some(&line[start..=end]);
            }
        }
    }
    None
}

/// 从字符串中提取类名
fn extract_class_name(line: &str) -> Option<&str> {
    if let Some(start) = line.find('\'') {
        if let Some(end) = line[start + 1..].find('\'') {
            return Some(&line[start + 1..start + 1 + end]);
        }
    }
    None
}

/// 从字符串中提取变量名
fn extract_var_name(line: &str) -> Option<&str> {
    if let Some(start) = line.find("scenePath_") {
        if let Some(end) = line[start..].find(" =") {
            return Some(&line[start..start + end]);
        }
    }
    None
}

/// 从字符串中提取属性名和值
fn extract_property(line: &str, var_name: &str) -> Option<(String, String)> {
    let prefix = format!("targetObject_{}", var_name);
    if let Some(start) = line.find(&prefix) {
        if let Some(dot) = line[start + prefix.len()..].find('.') {
            let prop_start = start + prefix.len() + dot + 1;
            if let Some(eq) = line[prop_start..].find(" = ") {
                let prop_name = line[prop_start..prop_start + eq].trim();
                let value_start = prop_start + eq + 3;
                if let Some(semi) = line[value_start..].find(';') {
                    let value = line[value_start..value_start + semi].trim();
                    return Some((prop_name.to_string(), value.to_string()));
                }
            }
        }
    }
    None
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_extract_json_array() {
        let line = r#"const scenePath_1 = ["Scene_Title", "0"];"#;
        let result = extract_json_array(line);
        assert_eq!(result, Some(r#"["Scene_Title", "0"]"#));
    }

    #[test]
    fn test_extract_class_name() {
        let line = r#"if (targetObject.constructor.name === 'Sprite') {"#;
        let result = extract_class_name(line);
        assert_eq!(result, Some("Sprite"));
    }

    #[test]
    fn test_extract_var_name() {
        let line = r#"const scenePath_sprite_Scene_Title_0_12345 = ["Scene_Title", "0"];"#;
        let result = extract_var_name(line);
        assert_eq!(result, Some("scenePath_sprite_Scene_Title_0_12345"));
    }
}
