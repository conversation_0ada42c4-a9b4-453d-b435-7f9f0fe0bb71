# 前后端集成测试文档

## 🎯 测试目标

验证新的统一API架构是否正确工作，特别是：
1. 类型操作和对象操作的正确区分
2. 类型路径标记的正确设置和识别
3. 前端操作上下文的正确判断

## 🔄 测试流程

### **测试场景1：对象操作模式**

1. **前置条件**：
   - 选择一个普通的场景对象（没有类型标记）
   - 确保 `selectedObjects.className` 为空

2. **操作步骤**：
   - 修改对象属性（如 x, y, width 等）
   - 创建子对象
   - 删除子对象

3. **预期结果**：
   - 前端判断为 `operationMode: 'OBJECT'`
   - 后端路由到 `object_handlers`
   - 生成的代码在场景的 `start` 方法中

### **测试场景2：类型操作模式（通过TypeTreePanel选择）**

1. **前置条件**：
   - 选择一个对象
   - 在 TypeTreePanel 中点击类型节点
   - 确保 `selectedObjects.className` 有值

2. **操作步骤**：
   - 修改对象属性
   - 创建子对象
   - 删除子对象

3. **预期结果**：
   - 前端判断为 `operationMode: 'TYPE'`
   - 后端路由到 `type_handlers`
   - 生成的代码在类型的 `initialize` 方法中
   - 创建的对象有类型标记：
     ```javascript
     object._rpgEditorTypeCreated = true;
     object._rpgEditorParentType = 'WindowLayer';
     object._rpgEditorPath = ['WindowLayer'];
     ```

### **测试场景3：类型操作模式（通过对象标记识别）**

1. **前置条件**：
   - 选择一个有类型标记的对象（`_rpgEditorTypeCreated = true`）
   - 确保对象有 `_rpgEditorPath` 属性

2. **操作步骤**：
   - 修改对象属性
   - 创建子对象

3. **预期结果**：
   - 前端自动判断为 `operationMode: 'TYPE'`
   - 即使 `selectedObjects.className` 为空，也应该识别为类型操作
   - 后端路由到 `type_handlers`

## 🔍 调试检查点

### **前端检查点**

1. **BackendService.ts - determineOperationContext 函数**：
   ```typescript
   console.log('检测到对象身上有类型创建标记，使用类型操作模式');
   console.log('类型路径:', object._rpgEditorPath);
   console.log('父类型:', object._rpgEditorParentType);
   ```

2. **操作信息构建**：
   ```typescript
   const operationInfo = {
     operationMode: 'TYPE' | 'OBJECT',
     operationType: 'CREATE' | 'DELETE' | 'MODIFY',
     targetPath: [...],
     fieldName: '...',
     fieldValue: ...,
     _operationContext: {
       topLevelType: '...'
     }
   };
   ```

### **后端检查点**

1. **entry.rs - 入口API**：
   ```rust
   logging::log_info("收到统一修改请求 - 模式: {}, 类型: {}, 路径: {:?}");
   ```

2. **router.rs - 操作路由**：
   ```rust
   logging::log_info("路由到类型操作处理器");
   // 或
   logging::log_info("路由到对象操作处理器");
   ```

3. **type_handlers.rs - 类型操作处理**：
   ```rust
   logging::log_info("处理类型创建操作 - 类型: {}, 字段: {}, 路径: {:?}");
   ```

4. **plugin_generator.rs - 代码生成**：
   ```rust
   // 类型创建标记
   code.push_str("_rpgEditorTypeCreated = true;");
   code.push_str("_rpgEditorParentType = '{}';");
   code.push_str("_rpgEditorPath = ['{}'];");
   ```

## 🚨 常见问题排查

### **问题1：前端无法识别类型操作**
- 检查对象是否有 `_rpgEditorTypeCreated` 标记
- 检查对象是否有 `_rpgEditorPath` 数组
- 检查 `selectedObjects.className` 是否正确设置

### **问题2：后端路由错误**
- 检查 `operationMode` 是否正确传递
- 检查 `topLevelType` 是否正确提取
- 检查路径验证逻辑

### **问题3：生成的代码位置错误**
- 类型操作应该在 `initialize` 方法中
- 对象操作应该在场景的 `start` 方法中

## 📋 测试清单

- [ ] 对象操作：属性修改
- [ ] 对象操作：对象创建
- [ ] 对象操作：对象删除
- [ ] 类型操作（TypeTreePanel）：属性修改
- [ ] 类型操作（TypeTreePanel）：对象创建
- [ ] 类型操作（TypeTreePanel）：对象删除
- [ ] 类型操作（对象标记）：属性修改
- [ ] 类型操作（对象标记）：对象创建
- [ ] 生成的代码正确性验证
- [ ] 类型标记正确设置验证

## 🎉 成功标准

1. **前端能够正确区分操作模式**
2. **后端能够正确路由到对应处理器**
3. **生成的代码在正确的位置**
4. **类型标记正确设置**
5. **整个流程无错误日志**
