//=============================================================================
// ObjectManager.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc 通过ID管理游戏对象的插件
 * <AUTHOR> Agent
 * @url https://github.com/yourusername/ObjectManager
 *
 * @help
 * ============================================================================
 * 介绍
 * ============================================================================
 * 
 * 这个插件允许您通过ID管理游戏对象，包括查找、修改、添加子对象和删除对象。
 * 它提供了一个全局对象管理器，可以在游戏的任何地方使用。
 * 
 * ============================================================================
 * 使用方法
 * ============================================================================
 * 
 * 1. 创建对象：
 *    ObjectManager.create(id, properties);
 * 
 * 2. 查找对象：
 *    const obj = ObjectManager.find(id);
 * 
 * 3. 修改对象属性：
 *    ObjectManager.modify(id, properties);
 * 
 * 4. 添加子对象：
 *    ObjectManager.addChild(parentId, childId, properties);
 * 
 * 5. 删除对象：
 *    ObjectManager.remove(id);
 * 
 * 6. 获取对象的所有子对象：
 *    const children = ObjectManager.getChildren(id);
 * 
 * 7. 获取所有对象：
 *    const allObjects = ObjectManager.getAllObjects();
 * 
 * ============================================================================
 * 示例
 * ============================================================================
 * 
 * // 创建一个角色对象
 * ObjectManager.create('player1', {
 *   name: '勇者',
 *   level: 1,
 *   hp: 100,
 *   mp: 50,
 *   position: { x: 0, y: 0 }
 * });
 * 
 * // 查找角色对象
 * const player = ObjectManager.find('player1');
 * console.log(player.name); // 输出: 勇者
 * 
 * // 修改角色属性
 * ObjectManager.modify('player1', {
 *   level: 2,
 *   hp: 120
 * });
 * 
 * // 添加装备作为子对象
 * ObjectManager.addChild('player1', 'sword1', {
 *   name: '铁剑',
 *   attack: 10
 * });
 * 
 * // 获取角色的所有装备
 * const equipment = ObjectManager.getChildren('player1');
 * 
 * // 删除装备
 * ObjectManager.remove('sword1');
 * 
 * ============================================================================
 * 注意事项
 * ============================================================================
 * 
 * - 对象ID必须是唯一的字符串
 * - 删除父对象会自动删除所有子对象
 * - 对象属性可以是任何JavaScript值，包括嵌套对象
 * - 这个插件不会自动保存对象数据，需要与其他存档插件配合使用
 */

var Imported = Imported || {};
Imported.ObjectManager = true;

//-----------------------------------------------------------------------------
// ObjectManager
//
// 游戏对象管理器

function ObjectManager() {
    throw new Error('This is a static class');
}

// 初始化对象存储
ObjectManager._objects = {};
ObjectManager._children = {};

/**
 * 创建一个新对象
 * @param {string} id - 对象的唯一ID
 * @param {Object} properties - 对象的属性
 * @returns {Object} 创建的对象
 */
ObjectManager.create = function(id, properties) {
    if (this._objects[id]) {
        console.warn(`对象ID '${id}' 已存在，将被覆盖`);
    }
    
    this._objects[id] = properties || {};
    this._children[id] = [];
    
    return this._objects[id];
};

/**
 * 查找对象
 * @param {string} id - 对象的唯一ID
 * @returns {Object|null} 找到的对象，如果不存在则返回null
 */
ObjectManager.find = function(id) {
    return this._objects[id] || null;
};

/**
 * 修改对象属性
 * @param {string} id - 对象的唯一ID
 * @param {Object} properties - 要修改的属性
 * @returns {Object|null} 修改后的对象，如果对象不存在则返回null
 */
ObjectManager.modify = function(id, properties) {
    const obj = this.find(id);
    if (!obj) {
        console.warn(`对象ID '${id}' 不存在，无法修改`);
        return null;
    }
    
    // 递归合并属性
    this._mergeProperties(obj, properties);
    
    return obj;
};

/**
 * 递归合并属性
 * @private
 * @param {Object} target - 目标对象
 * @param {Object} source - 源对象
 */
ObjectManager._mergeProperties = function(target, source) {
    for (const key in source) {
        if (source.hasOwnProperty(key)) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                // 如果是对象，递归合并
                target[key] = target[key] || {};
                this._mergeProperties(target[key], source[key]);
            } else {
                // 否则直接赋值
                target[key] = source[key];
            }
        }
    }
};

/**
 * 添加子对象
 * @param {string} parentId - 父对象的ID
 * @param {string} childId - 子对象的ID
 * @param {Object} properties - 子对象的属性
 * @returns {Object|null} 创建的子对象，如果父对象不存在则返回null
 */
ObjectManager.addChild = function(parentId, childId, properties) {
    if (!this.find(parentId)) {
        console.warn(`父对象ID '${parentId}' 不存在，无法添加子对象`);
        return null;
    }
    
    const child = this.create(childId, properties);
    
    // 将子对象ID添加到父对象的子对象列表中
    if (!this._children[parentId].includes(childId)) {
        this._children[parentId].push(childId);
    }
    
    return child;
};

/**
 * 获取对象的所有子对象
 * @param {string} id - 对象的唯一ID
 * @returns {Array} 子对象数组
 */
ObjectManager.getChildren = function(id) {
    if (!this.find(id)) {
        console.warn(`对象ID '${id}' 不存在，无法获取子对象`);
        return [];
    }
    
    return this._children[id].map(childId => this.find(childId));
};

/**
 * 删除对象
 * @param {string} id - 对象的唯一ID
 * @returns {boolean} 是否成功删除
 */
ObjectManager.remove = function(id) {
    if (!this.find(id)) {
        console.warn(`对象ID '${id}' 不存在，无法删除`);
        return false;
    }
    
    // 递归删除所有子对象
    for (const childId of this._children[id]) {
        this.remove(childId);
    }
    
    // 从所有父对象的子对象列表中移除
    for (const parentId in this._children) {
        const index = this._children[parentId].indexOf(id);
        if (index !== -1) {
            this._children[parentId].splice(index, 1);
        }
    }
    
    // 删除对象和子对象列表
    delete this._objects[id];
    delete this._children[id];
    
    return true;
};

/**
 * 获取所有对象
 * @returns {Object} 所有对象的映射
 */
ObjectManager.getAllObjects = function() {
    return this._objects;
};

/**
 * 清除所有对象
 */
ObjectManager.clear = function() {
    this._objects = {};
    this._children = {};
};

/**
 * 保存对象数据到游戏变量
 * @param {number} variableId - 游戏变量ID
 */
ObjectManager.saveToVariable = function(variableId) {
    const data = {
        objects: this._objects,
        children: this._children
    };
    $gameVariables.setValue(variableId, JSON.stringify(data));
};

/**
 * 从游戏变量加载对象数据
 * @param {number} variableId - 游戏变量ID
 * @returns {boolean} 是否成功加载
 */
ObjectManager.loadFromVariable = function(variableId) {
    const json = $gameVariables.value(variableId);
    if (!json) {
        console.warn(`变量ID ${variableId} 中没有对象数据`);
        return false;
    }
    
    try {
        const data = JSON.parse(json);
        this._objects = data.objects || {};
        this._children = data.children || {};
        return true;
    } catch (e) {
        console.error('加载对象数据失败:', e);
        return false;
    }
};

// 将ObjectManager添加到全局作用域
window.ObjectManager = ObjectManager;
