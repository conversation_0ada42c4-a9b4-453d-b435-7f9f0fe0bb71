//=============================================================================
// UIEditor.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc UI编辑器 - 可视化编辑游戏界面
 * <AUTHOR> Name
 *
 * @command openEditor
 * @text 打开编辑器
 * @desc 打开UI编辑器窗口
 *
 * @help UIEditor.js
 *
 * 这个插件提供了一个可视化的UI编辑器：
 * 1. 场景树视图
 * 2. 属性编辑面板
 * 3. 实时预览
 */

(() => {
  "use strict";

  // 创建独立窗口的UI编辑器
  class UIEditorWindow {
    static async open() {
      try {
        // 使用基础的window.open方法创建新窗口
        const editorPath = "editor.html";
        const windowFeatures = [
          "width=800",
          "height=600",
          "menubar=no",
          "toolbar=no",
          "location=no",
          "status=no",
          "resizable=yes",
          "scrollbars=yes",
        ].join(",");

        const editorWindow = window.open(
          editorPath,
          "UIEditor",
          windowFeatures
        );

        if (!editorWindow) {
          throw new Error("无法创建编辑器窗口，可能被浏览器阻止了弹出窗口");
        }

        // 等待窗口加载完成
        await new Promise((resolve) => {
          editorWindow.onload = () => {
            console.log("编辑器窗口加载完成");
            resolve();
          };
        });

        // 初始化编辑器
        try {
          this.initializeEditor(editorWindow);
          console.log("编辑器初始化完成");
        } catch (error) {
          console.error("编辑器初始化失败:", error);
          editorWindow.close();
        }

        // 监听窗口关闭
        editorWindow.onbeforeunload = () => {
          console.log("编辑器窗口即将关闭");
        };
      } catch (error) {
        console.error("打开编辑器窗口时发生错误:", error);
      }
    }

    static initializeEditor(editorWindow) {
      // 初始化编辑器的各个组件
      const editorDoc = editorWindow.window.document;

      // 获取场景树容器和预览容器
      const sceneChildren = editorDoc.getElementById("sceneChildren");
      const previewContainer = editorDoc.getElementById("preview");

      // 创建预览渲染器
      const app = new PIXI.Application({
        width: previewContainer.clientWidth,
        height: previewContainer.clientHeight,
        backgroundColor: 0x000000,
      });
      previewContainer.appendChild(app.view);

      // 获取当前场景的渲染内容
      const gameStage = SceneManager._scene;
      if (gameStage && gameStage.children) {
        // 创建预览场景容器
        const previewStage = new PIXI.Container();
        app.stage.addChild(previewStage);

        // 复制场景内容到预览窗口
        function cloneDisplayObject(source) {
          if (!source) return null;

          let clone;

          // 根据源对象类型创建相应的克隆对象
          if (source instanceof PIXI.Sprite) {
            // 如果是精灵对象，复制其纹理
            clone = new PIXI.Sprite(source.texture);
            // 复制精灵特有属性
            clone.anchor.copyFrom(source.anchor);
            clone.tint = source.tint;
            clone.blendMode = source.blendMode;
          } else if (source instanceof PIXI.Text) {
            // 如果是文本对象
            clone = new PIXI.Text(source.text, source.style);
          } else if (source instanceof PIXI.Graphics) {
            // 如果是图形对象
            clone = new PIXI.Graphics();
            // 复制图形数据
            clone.geometry = source.geometry.clone();
          } else {
            // 默认创建容器
            clone = new PIXI.Container();
          }

          // 复制基本变换属性
          clone.position.set(source.x, source.y);
          clone.scale.set(source.scale.x, source.scale.y);
          clone.rotation = source.rotation;
          clone.alpha = source.alpha;
          clone.visible = source.visible;
          clone.zIndex = source.zIndex;

          // 递归复制子元素
          if (source.children && source.children.length > 0) {
            source.children.forEach((child) => {
              const childClone = cloneDisplayObject(child);
              if (childClone) clone.addChild(childClone);
            });
          }

          return clone;
        }

        // 复制场景显示对象
        const sceneClone = cloneDisplayObject(gameStage);
        if (sceneClone) previewStage.addChild(sceneClone);

        // 定期更新预览内容
        const updatePreview = () => {
          if (sceneClone && gameStage) {
            // 更新预览场景的状态
            sceneClone.position.set(gameStage.x, gameStage.y);
            sceneClone.scale.set(gameStage.scale.x, gameStage.scale.y);
            sceneClone.rotation = gameStage.rotation;
            sceneClone.alpha = gameStage.alpha;
            sceneClone.visible = gameStage.visible;
          }
          requestAnimationFrame(updatePreview);
        };
        updatePreview();
      }

      // 获取当前场景中的所有UI元素
      const scene = SceneManager._scene;
      if (!scene) {
        console.error("无法获取当前场景");
        return;
      }
      console.log("当前场景:", scene.constructor.name);

      // 递归创建场景树节点
      function createTreeNode(element, parentNode) {
        if (!element) {
          console.warn("尝试创建空元素节点");
          return;
        }

        console.log("创建节点:", element.constructor.name);
        const node = editorDoc.createElement("div");
        node.className = "tree-node";

        // 创建图标和名称
        const icon = editorDoc.createElement("span");
        icon.className = "tree-icon";

        // 检查PIXI显示对象的子元素
        const displayObject = element;
        const hasChildren =
          displayObject.children && displayObject.children.length > 0;

        console.log("节点属性:", {
          name: element.constructor.name,
          type: displayObject.type || "Unknown",
          hasChildren,
          visible: displayObject.visible,
          x: displayObject.x,
          y: displayObject.y,
        });

        icon.textContent = hasChildren ? "▼" : "■";

        const name = editorDoc.createElement("span");
        name.textContent = `${element.constructor.name} (${
          displayObject.type || "Unknown"
        })`;

        node.appendChild(icon);
        node.appendChild(name);

        // 创建子节点容器
        const childrenContainer = editorDoc.createElement("div");
        childrenContainer.className = "tree-children";

        // 递归添加所有PIXI显示对象的子元素
        if (hasChildren) {
          console.log(
            `添加${element.constructor.name}的子元素:`,
            displayObject.children.length
          );
          displayObject.children.forEach((child, index) => {
            if (child) {
              console.log(`处理子元素 ${index}:`, child.constructor.name);
              createTreeNode(child, childrenContainer);
            }
          });
        }

        // 只有当有子元素时才添加子节点容器
        if (childrenContainer.children.length > 0) {
          node.appendChild(childrenContainer);
        }

        // 添加点击事件
        node.addEventListener("click", (e) => {
          e.stopPropagation();
          // 移除其他节点的选中状态
          editorDoc
            .querySelectorAll(".tree-node")
            .forEach((n) => n.classList.remove("selected"));
          // 添加当前节点的选中状态
          node.classList.add("selected");
          // TODO: 更新属性面板
        });

        parentNode.appendChild(node);
      }

      // 创建场景树
      createTreeNode(scene, sceneChildren);
    }
  }

  // 注册插件命令
  PluginManager.registerCommand("UIEditor", "openEditor", () => {
    UIEditorWindow.open();
  });

  // 添加按键监听
  document.addEventListener("keydown", (event) => {
    // 当按下F12键时打开编辑器
    if (event.key === "F8") {
      console.log("按下F8键，正在打开编辑器...");
      UIEditorWindow.open();
    }
  });
})();
