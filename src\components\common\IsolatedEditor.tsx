import React, { useRef, useEffect, useState, useCallback } from 'react';
import './IsolatedEditor.css';

interface IsolatedEditorProps {
  code: string;
  language?: string;
  theme?: 'vs-dark' | 'light';
  onChange?: (value: string) => void;
  onEscape?: () => void;
  onReady?: () => void;
}

/**
 * 完全隔离的编辑器组件，使用iframe来确保事件不会传播到外部
 */
const IsolatedEditor: React.FC<IsolatedEditorProps> = ({
  code: initialCode,
  language = 'javascript',
  theme = 'vs-dark',
  onChange,
  onEscape,
  onReady
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const editorRef = useRef<any>(null);
  const [isEditorReady, setIsEditorReady] = useState(false);
  const lastCodeRef = useRef(initialCode);
  const lastThemeRef = useRef(theme);

  // 初始化iframe - 只在组件挂载时执行一次
  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe) return;

    // 创建一个blob URL，包含编辑器的HTML内容
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          html, body, #editor-container {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
          }
          body {
            background-color: ${theme === 'vs-dark' ? '#1e1e1e' : '#ffffff'};
          }
        </style>
      </head>
      <body>
        <div id="editor-container"></div>

        <!-- 加载Monaco编辑器 -->
        <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.36.1/min/vs/loader.js"></script>
        <script>
          // 初始化Monaco编辑器
          require.config({
            paths: { 'vs': 'https://cdn.jsdelivr.net/npm/monaco-editor@0.36.1/min/vs' }
          });

          require(['vs/editor/editor.main'], function() {
            // 创建编辑器实例
            window.editor = monaco.editor.create(document.getElementById('editor-container'), {
              value: ${JSON.stringify(initialCode)},
              language: ${JSON.stringify(language)},
              theme: ${JSON.stringify(theme)},
              automaticLayout: true,
              fontSize: 14,
              fontFamily: "'Fira Code', 'Consolas', 'Monaco', monospace",
              lineNumbers: 'on',
              folding: true,
              wordWrap: 'on',
              scrollBeyondLastLine: false,
              minimap: { enabled: false }
            });

            // 监听编辑器内容变化
            window.editor.onDidChangeModelContent(function() {
              const value = window.editor.getValue();
              window.parent.postMessage({ type: 'editor-change', value }, '*');
            });

            // 监听Escape键
            window.editor.onKeyDown(function(e) {
              if (e.keyCode === 27) { // Escape键
                window.parent.postMessage({ type: 'editor-escape' }, '*');
              }
            });

            // 通知父窗口编辑器已加载完成
            window.parent.postMessage({ type: 'editor-ready' }, '*');
          });

          // 监听来自父窗口的消息
          window.addEventListener('message', function(event) {
            if (!event.data || typeof event.data !== 'object') return;

            if (event.data.type === 'update-editor' && window.editor) {
              // 获取当前编辑器内容
              const currentValue = window.editor.getValue();

              // 如果内容没有变化，不做任何处理
              if (currentValue === event.data.value) {
                return;
              }

              // 保存当前光标位置和滚动位置
              const selection = window.editor.getSelection();
              const scrollTop = window.editor.getScrollTop();
              const scrollLeft = window.editor.getScrollLeft();

              // 更新编辑器内容
              window.editor.setValue(event.data.value);

              // 恢复光标位置和滚动位置
              if (selection) {
                window.editor.setSelection(selection);
                window.editor.setScrollTop(scrollTop);
                window.editor.setScrollLeft(scrollLeft);
              }
            }

            if (event.data.type === 'update-theme' && window.editor) {
              window.editor.updateOptions({ theme: event.data.theme });
              document.body.style.backgroundColor =
                event.data.theme === 'vs-dark' ? '#1e1e1e' : '#ffffff';
            }
          });
        </script>
      </body>
      </html>
    `;

    // 创建Blob和URL
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);

    // 设置iframe的src
    iframe.src = url;

    // 清理函数
    return () => {
      URL.revokeObjectURL(url);
    };
  }, []); // 空依赖数组，确保只在组件挂载时执行一次

  // 处理来自iframe的消息
  const handleMessage = useCallback((event: MessageEvent) => {
    if (!event.data || typeof event.data !== 'object') return;

    switch (event.data.type) {
      case 'editor-change':
        // 更新内部引用，但不触发状态更新
        lastCodeRef.current = event.data.value;
        // 只调用外部onChange回调
        onChange?.(event.data.value);
        break;
      case 'editor-escape':
        onEscape?.();
        break;
      case 'editor-ready':
        console.log('编辑器已加载完成');
        setIsEditorReady(true);
        // 调用外部onReady回调
        onReady?.();
        break;
    }
  }, [onChange, onEscape, onReady]);

  // 监听来自iframe的消息
  useEffect(() => {
    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [handleMessage]);

  // 处理代码和主题更新
  useEffect(() => {
    if (!isEditorReady || !iframeRef.current) return;

    const iframe = iframeRef.current;
    const iframeWindow = iframe.contentWindow;
    if (!iframeWindow) return;

    // 检查代码是否变化了
    if (initialCode !== lastCodeRef.current) {
      console.log('更新编辑器代码:', initialCode);
      iframeWindow.postMessage({ type: 'update-editor', value: initialCode }, '*');
      lastCodeRef.current = initialCode;
    }

    // 检查主题是否变化了
    if (theme !== lastThemeRef.current) {
      console.log('更新编辑器主题:', theme);
      iframeWindow.postMessage({ type: 'update-theme', theme }, '*');
      lastThemeRef.current = theme;
    }
  }, [initialCode, theme, isEditorReady]);

  return (
    <div className="isolated-editor-container">
      <iframe
        ref={iframeRef}
        className="isolated-editor-iframe"
        title="Isolated Code Editor"
        sandbox="allow-scripts allow-same-origin"
      />
    </div>
  );
};

export default IsolatedEditor;
