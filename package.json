{"name": "rpgeditor", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@monaco-editor/react": "^4.7.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@reduxjs/toolkit": "^2.8.0", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-dialog": "^2.2.1", "@tauri-apps/plugin-fs": "^2.2.1", "@tauri-apps/plugin-opener": "^2", "@types/pixi.js": "^4.8.9", "@types/prismjs": "^1.26.5", "@types/uuid": "^10.0.0", "codemirror": "^5.65.2", "jshint": "^2.13.6", "pixi.js": "^8.9.2", "prismjs": "^1.30.0", "re-resizable": "^6.11.2", "react": "^18.3.1", "react-codemirror2": "^8.0.1", "react-dom": "^18.3.1", "react-redux": "^9.2.0", "react-scan": "^0.3.3", "react-simple-code-editor": "^0.14.1", "uuid": "^11.1.0", "zustand": "^5.0.4"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "typescript": "~5.6.2", "vite": "^6.0.3"}}