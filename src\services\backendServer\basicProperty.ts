import { recordPropertyModification } from './BackendService';

// ==================== 基础属性相关方法 ====================

/**
 * 基础属性类型定义
 */
export type BasicPropertyType =
  | 'position'     // x, y
  | 'size'         // width, height
  | 'transform'    // rotation, skew.x, skew.y, anchor.x, anchor.y
  | 'appearance'   // alpha, tint, color
  | 'border'       // border.width, border.color
  | 'visibility';  // visible

/**
 * 基础属性映射
 */
export const BASIC_PROPERTY_MAP: Record<string, BasicPropertyType> = {
  'x': 'position',
  'y': 'position',
  'width': 'size',
  'height': 'size',
  'rotation': 'transform',
  'skew.x': 'transform',
  'skew.y': 'transform',
  'anchor.x': 'transform',
  'anchor.y': 'transform',
  'alpha': 'appearance',
  'tint': 'appearance',
  'color': 'appearance',
  'border.width': 'border',
  'border.color': 'border',
  'visible': 'visibility'
};

/**
 * 检查是否为基础属性
 */
export const isBasicProperty = (propertyName: string): boolean => {
  return propertyName in BASIC_PROPERTY_MAP;
};

/**
 * 获取基础属性类型
 */
export const getBasicPropertyType = (propertyName: string): BasicPropertyType | null => {
  return BASIC_PROPERTY_MAP[propertyName] || null;
};

/**
 * 处理基础属性修改（前端对象更新）
 * @param objects 要修改的对象数组
 * @param propertyName 属性名称
 * @param value 新值
 * @returns 是否成功修改
 */
export const updateBasicProperty = (objects: any[], propertyName: string, value: any): boolean => {
  if (!isBasicProperty(propertyName)) {
    console.warn(`${propertyName} 不是基础属性`);
    return false;
  }

  try {
    const propertyType = getBasicPropertyType(propertyName);
    console.log(`更新基础属性: ${propertyName} (类型: ${propertyType}) = ${value}`);

    // 获取第一个对象作为参考计算增量
    const firstObject = objects[0];
    if (!firstObject) return false;

    let currentValue = getNestedPropertyValue(firstObject, propertyName);
    let deltaValue = value;

    // 对数值类型计算增量
    if (typeof currentValue === 'number' && typeof value === 'number') {
      deltaValue = value - currentValue;
      console.log(`计算增量值: ${deltaValue} (${value} - ${currentValue})`);
    }

    // 对每个对象应用修改
    for (const object of objects) {
      if (!object) continue;

      if (propertyName.includes('.')) {
        // 处理嵌套属性
        setNestedPropertyValue(object, propertyName, deltaValue, typeof currentValue === 'number');
      } else {
        // 处理直接属性
        if (typeof object[propertyName] === 'number' && typeof deltaValue === 'number') {
          object[propertyName] += deltaValue;
        } else {
          object[propertyName] = deltaValue;
        }
      }

      console.log(`基础属性 ${propertyName} 已更新为: ${getNestedPropertyValue(object, propertyName)}`);
    }

    return true;
  } catch (error) {
    console.error(`更新基础属性 ${propertyName} 失败:`, error);
    return false;
  }
};

/**
 * 记录基础属性修改到后端
 * @param object 要修改的对象
 * @param propertyName 属性名称
 * @param value 新值
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export const recordBasicPropertyModification = async (
  object: any,
  propertyName: string,
  value: any
): Promise<string> => {
  if (!isBasicProperty(propertyName)) {
    throw new Error(`${propertyName} 不是基础属性`);
  }

  const propertyType = getBasicPropertyType(propertyName);
  console.log(`记录基础属性修改: ${propertyName} (类型: ${propertyType}) = ${value}`);

  // 使用统一的后端记录方法
  return await recordPropertyModification(object, propertyName, value);
};

/**
 * 获取嵌套属性值
 */
const getNestedPropertyValue = (obj: any, propertyPath: string): any => {
  const keys = propertyPath.split('.');
  let current = obj;

  for (const key of keys) {
    if (current === null || current === undefined) {
      return undefined;
    }
    current = current[key];
  }

  return current;
};

/**
 * 设置嵌套属性值
 */
const setNestedPropertyValue = (obj: any, propertyPath: string, value: any, isIncrement: boolean = false): void => {
  const keys = propertyPath.split('.');
  let current = obj;

  // 遍历到倒数第二个键
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!current[key]) {
      current[key] = {};
    }
    current = current[key];
  }

  // 设置最后一个键的值
  const lastKey = keys[keys.length - 1];
  if (isIncrement && typeof current[lastKey] === 'number') {
    current[lastKey] += value;
  } else {
    current[lastKey] = value;
  }
};
