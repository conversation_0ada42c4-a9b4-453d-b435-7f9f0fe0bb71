import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Collapse,
  IconButton,
  Chip
} from '@mui/material';
import {
  ExpandMore,
  ChevronRight,
  Class as ClassIcon
} from '@mui/icons-material';
import useProjectStore from '../../../store/Store';
import { getObjectTypeTree, TypeTreeNode, TypeTreeInfo } from '../../../utils/object/objectTypeTree';

interface TypeTreePanelProps {
  // 可以添加需要的属性
}

const TypeTreePanel: React.FC<TypeTreePanelProps> = () => {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);

  // 获取全局状态
  const selectedObjects = useProjectStore(state => state.selectedObjects);
  const setSelectedObjects = useProjectStore(state => state.setSelectedObjects);

  // 类型树信息状态
  const [typeTreeInfo, setTypeTreeInfo] = useState<TypeTreeInfo | null>(null);

  // 将TypeTreeNode转换为渲染用的节点，并生成唯一ID，同时添加当前对象节点
  const convertToRenderNodes = (nodes: TypeTreeNode[], parentId: string = '', currentObject?: any): Array<TypeTreeNode & { id: string }> => {
    const convertedNodes = nodes.map((node, index) => {
      const id = parentId ? `${parentId}-${index}` : `root-${index}`;
      return {
        ...node,
        id,
        children: convertToRenderNodes(node.children, id)
      };
    });

    // 如果是根级别且有当前对象，添加当前对象节点到最后
    if (!parentId && currentObject && convertedNodes.length > 0) {
      // 找到最深层的当前类型节点
      const findDeepestCurrentType = (nodeList: Array<TypeTreeNode & { id: string }>): TypeTreeNode & { id: string } | null => {
        for (const node of nodeList) {
          if (node.isCurrentType && node.children.length === 0) {
            return node;
          }
          if (node.children.length > 0) {
            const found = findDeepestCurrentType(node.children as Array<TypeTreeNode & { id: string }>);
            if (found) return found;
          }
        }
        return null;
      };

      const deepestCurrentType = findDeepestCurrentType(convertedNodes);
      if (deepestCurrentType) {
        // 在最深层的当前类型节点下添加对象实例节点
        const objectNode: TypeTreeNode & { id: string } = {
          name: `${currentObject.constructor?.name || 'Object'} 实例`,
          isCurrentType: false,
          description: '当前选中的对象',
          children: [],
          parent: deepestCurrentType,
          depth: deepestCurrentType.depth + 1,
          isEditable: false,
          id: `${deepestCurrentType.id}-object`
        };

        (deepestCurrentType.children as Array<TypeTreeNode & { id: string }>).push(objectNode);
      }
    }

    return convertedNodes;
  };

  const [renderNodes, setRenderNodes] = useState<Array<TypeTreeNode & { id: string }>>([]);

  useEffect(() => {
    if (!selectedObjects.objects.length) {
      setTypeTreeInfo(null);
      setRenderNodes([]);
      setExpandedNodes(new Set());
      setSelectedNodeId(null);
      return;
    }

    const currentObject = selectedObjects.objects[0];

    // 使用objectTypeTree获取类型树信息
    const treeInfo = getObjectTypeTree(currentObject);
    setTypeTreeInfo(treeInfo);

    // 转换为渲染节点，传入当前对象以添加对象实例节点
    const nodes = convertToRenderNodes(treeInfo.typeTree, '', currentObject);
    setRenderNodes(nodes);

    // 默认展开所有节点
    const allNodeIds = new Set<string>();
    const collectNodeIds = (nodeList: Array<TypeTreeNode & { id: string }>) => {
      nodeList.forEach(node => {
        allNodeIds.add(node.id);
        if (node.children.length > 0) {
          collectNodeIds(node.children as Array<TypeTreeNode & { id: string }>);
        }
      });
    };
    collectNodeIds(nodes);
    setExpandedNodes(allNodeIds);

    // 默认选中当前对象类型，但不触发setSelectedObjects避免循环
    const currentTypeNode = nodes.find(node =>
      findCurrentTypeInTree([node], treeInfo.targetObject?.constructor?.name || '')
    );
    if (currentTypeNode) {
      const currentNode = findCurrentTypeInTree([currentTypeNode], treeInfo.targetObject?.constructor?.name || '');
      if (currentNode) {
        setSelectedNodeId(currentNode.id);
        // 注意：这里不调用setSelectedObjects，避免无限循环
        // 只在用户手动点击时才设置className
      }
    }
  }, [selectedObjects.objects]); // 只依赖objects数组，不依赖整个selectedObjects

  // 递归查找当前类型节点
  const findCurrentTypeInTree = (nodes: Array<TypeTreeNode & { id: string }>, typeName: string): (TypeTreeNode & { id: string }) | null => {
    for (const node of nodes) {
      if (node.isCurrentType && node.name === typeName) {
        return node;
      }
      if (node.children.length > 0) {
        const found = findCurrentTypeInTree(node.children as Array<TypeTreeNode & { id: string }>, typeName);
        if (found) return found;
      }
    }
    return null;
  };

  const handleNodeToggle = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const handleNodeClick = (node: TypeTreeNode & { id: string }) => {
    setSelectedNodeId(node.id);
    if (selectedObjects.objects.length > 0) {
      // 如果点击的是对象实例节点，设置为object类型，清除className
      if (node.description === '当前选中的对象') {
        setSelectedObjects(selectedObjects.objects, "object", undefined);
      } else {
        // 点击类型节点时，设置className到selectedObjects
        setSelectedObjects(selectedObjects.objects, "class", node.name);
      }

    }
  };

  const renderTreeItem = (node: TypeTreeNode & { id: string }, depth: number = 0): React.ReactNode => {
    const isExpanded = expandedNodes.has(node.id);
    const hasChildren = node.children.length > 0;
    const isSelected = selectedNodeId === node.id;

    return (
      <React.Fragment key={node.id}>
        <ListItem
          onClick={() => handleNodeClick(node)}
          sx={{
            pl: depth * 2 + 1,
            cursor: 'pointer',
            backgroundColor: isSelected ? 'primary.light' : 'transparent',
            '&:hover': {
              backgroundColor: isSelected ? 'primary.light' : 'action.hover'
            }
          }}
        >
          {/* 展开/收起图标 */}
          <ListItemIcon sx={{ minWidth: 24 }}>
            {hasChildren ? (
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  handleNodeToggle(node.id);
                }}
              >
                {isExpanded ? <ExpandMore fontSize="small" /> : <ChevronRight fontSize="small" />}
              </IconButton>
            ) : (
              <Box sx={{ width: 24 }} />
            )}
          </ListItemIcon>

          {/* 类型图标 */}
          <ListItemIcon sx={{ minWidth: 32 }}>
            <ClassIcon sx={{ fontSize: 16, color: 'primary.main' }} />
          </ListItemIcon>

          {/* 类型名称 */}
          <ListItemText
            primary={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography
                  variant="body2"
                  sx={{
                    fontWeight: node.isCurrentType ? 'bold' : 'normal',
                    color: node.isCurrentType ? 'primary.main' : 'text.primary'
                  }}
                >
                  {node.name}
                </Typography>

                {node.description && (
                  <Chip
                    label={node.description}
                    size="small"
                    variant={node.isCurrentType ? "filled" : "outlined"}
                    color={node.isCurrentType ? "primary" : "default"}
                    sx={{ fontSize: '0.7rem', height: 20 }}
                  />
                )}
              </Box>
            }
          />
        </ListItem>

        {/* 子节点 */}
        <Collapse in={isExpanded} timeout="auto" unmountOnExit>
          {(node.children as Array<TypeTreeNode & { id: string }>).map(child => renderTreeItem(child, depth + 1))}
        </Collapse>
      </React.Fragment>
    );
  };

  if (!selectedObjects.objects.length || !typeTreeInfo) {
    return (
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          请先选择一个对象查看类型树
        </Typography>
      </Box>
    );
  }

  const displayTypeName = typeTreeInfo.displayObjectType || 'Unknown';
  const targetTypeName = typeTreeInfo.targetObject?.constructor?.name || 'Unknown';
  const inheritanceDepth = typeTreeInfo.typeTree.length > 0 ?
    Math.max(...typeTreeInfo.typeTree.map(node => node.depth)) : 0;

  return (
    <Box
      className="type-tree-panel"
      sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}
    >
      {/* 头部信息 */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" gutterBottom>
          类型树
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {typeTreeInfo.objectType === 'logic-primary' ? (
            <>
              逻辑对象: <strong>{targetTypeName}</strong> | 显示对象: <strong>{displayTypeName}</strong>
            </>
          ) : (
            <>
              显示对象: <strong>{displayTypeName}</strong>
            </>
          )}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          继承链深度: {inheritanceDepth}
        </Typography>
        {selectedObjects.className && (
          <Typography variant="caption" color="primary">
            已选择类型: <strong>{selectedObjects.className}</strong>
          </Typography>
        )}
        <Typography variant="caption" color={selectedObjects.className ? "secondary" : "primary"}>
          当前模式: <strong>{selectedObjects.className ? "类型修改模式" : "对象修改模式"}</strong>
        </Typography>
      </Box>

      {/* 类型树 */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        <List dense>
          {renderNodes.map(node => renderTreeItem(node))}
        </List>
      </Box>

      {/* 底部说明 */}
      <Box sx={{ p: 1, borderTop: 1, borderColor: 'divider' }}>
        <Typography variant="caption" color="text.secondary">
          点击类型名称可选择该类型
        </Typography>
      </Box>
    </Box>
  );
};

export default TypeTreePanel;