# Anime.js动画库插件

## 简介

Anime.js动画库插件为RPG Maker MZ提供了强大而灵活的动画功能。通过集成流行的Anime.js库，这个插件允许开发者对游戏中的任何对象应用平滑、高性能的动画效果，包括对象自身和其子元素。

## 特性

- **对象自身动画**：直接对游戏对象应用动画效果
- **子元素动画**：同时对容器的所有子元素应用动画
- **丰富的动画属性**：支持位置、缩放、旋转、透明度等多种属性的动画
- **多种缓动函数**：提供弹性、弹跳、线性等多种缓动效果
- **动画序列**：支持创建连续的动画序列和时间轴
- **动画控制**：支持暂停、恢复、重启和停止动画
- **事件回调**：提供动画开始、更新、完成等事件的回调函数

## 安装

1. 将`AnimeJS.js`文件放入你的游戏项目的`plugins`文件夹中
2. 在RPG Maker MZ的插件管理器中启用AnimeJS插件
3. 确保你的游戏有互联网连接，因为插件会从CDN加载Anime.js库
   (或者你可以下载Anime.js库并修改插件中的路径以使用本地文件)

## 基本用法

### 对象自身动画

```javascript
// 获取对象
const obj = findObjectByScenePath(SceneManager._scene, ["Scene_Title", "2"]);

// 基本动画 - 移动到指定位置
obj.animate({ x: 100, y: 200 }, 1000, "easeOutQuad");

// 多属性动画 - 同时改变多个属性
obj.animate({
    x: 100,
    y: 200,
    alpha: 0.5,
    scaleX: 1.5,
    scaleY: 1.5,
    rotation: Math.PI / 4  // 旋转45度
}, 1500, "easeInOutExpo");

// 相对值动画 - 使用字符串表示相对变化
obj.animate({
    x: "+=50",  // 向右移动50像素
    y: "-=30"   // 向上移动30像素
}, 800, "linear");

// 值数组动画 - 指定起始值和结束值
obj.animate({
    alpha: [0, 1],  // 从透明到不透明
    scaleX: [0.5, 1.5, 1]  // 从0.5缩放到1.5，再回到1
}, 2000, "easeOutElastic");

// 循环动画
obj.animate({
    y: "+=20"
}, 1000, "easeInOutSine", {
    loop: true,
    direction: "alternate"  // 交替方向
});
```

### 子元素动画

```javascript
// 获取容器对象
const container = findObjectByScenePath(SceneManager._scene, ["Scene_Menu", "1"]);

// 对所有子元素应用相同的动画
container.animateChildren({
    alpha: [0, 1],  // 所有子元素从透明到不透明
    y: "+=10"       // 所有子元素向下移动10像素
}, 800, "easeOutQuad");

// 使用错开延迟 - 子元素依次开始动画
container.animateChildren({
    scale: [0.8, 1],
    alpha: [0, 1]
}, 600, "easeOutBack", {
    delay: anime.stagger(100)  // 每个子元素延迟100毫秒
});

// 使用网格错开延迟 - 适用于网格布局的子元素
container.animateChildren({
    scale: [0.8, 1],
    alpha: [0, 1]
}, 600, "easeOutBack", {
    delay: anime.stagger(100, {grid: [3, 5], from: 'center'})  // 3x5网格，从中心开始
});
```

### 动画序列

```javascript
// 创建动画序列 - 一个接一个地执行动画
const obj = findObjectByScenePath(SceneManager._scene, ["Scene_Title", "2"]);
obj.animateSequence([
    { x: 100 },           // 第一步：移动到x=100
    { y: 200 },           // 第二步：移动到y=200
    { alpha: 0.5 },       // 第三步：设置透明度为0.5
    { scaleX: 1.5, scaleY: 1.5 }  // 第四步：放大1.5倍
], 800, "easeOutQuad");

// 使用时间轴创建更复杂的序列
const timeline = anime.timeline({
    targets: obj,
    easing: 'easeOutExpo'
});

timeline
    .add({
        x: 100,
        duration: 500
    })
    .add({
        y: 200,
        duration: 800,
        offset: '+=200'  // 在前一个动画开始后200毫秒开始
    })
    .add({
        alpha: 0.5,
        duration: 300
    });
```

### 动画控制

```javascript
// 创建动画并保存引用
const animation = obj.animate({ x: 100 }, 1000, "linear");

// 暂停动画
animation.pause();

// 播放动画
animation.play();

// 重启动画
animation.restart();

// 寻找特定进度
animation.seek(500);  // 跳到动画的中间点

// 反向播放
animation.reverse();

// 停止所有动画
obj.stopAnimations();

// 停止所有子元素的动画
container.stopChildrenAnimations();
```

### 动画回调

```javascript
obj.animate({ x: 100 }, 1000, "linear", {
    // 动画开始时调用
    begin: function(anim) {
        console.log('动画开始');
    },
    
    // 动画更新时调用
    update: function(anim) {
        console.log('动画进度:', anim.progress + '%');
    },
    
    // 动画完成时调用
    complete: function(anim) {
        console.log('动画完成');
    }
});
```

## 高级用法

### 自定义缓动函数

```javascript
// 使用自定义缓动函数
obj.animate({ x: 100 }, 1000, function(el, i, total) {
    return function(t) {
        return Math.pow(Math.sin(t * Math.PI), 3);
    }
});
```

### 路径动画

```javascript
// 沿SVG路径移动
obj.animate({
    translateX: {
        value: [0, 250],
        duration: 1000
    },
    translateY: {
        value: [0, 50],
        duration: 1000
    },
    rotate: {
        value: '1turn',
        duration: 1200,
        easing: 'easeInOutSine'
    }
}, 0, 'linear');
```

### 关键帧动画

```javascript
// 使用关键帧
obj.animate({
    translateX: [
        { value: 250, duration: 1000, delay: 500 },
        { value: 0, duration: 1000, delay: 500 }
    ],
    translateY: [
        { value: -100, duration: 500 },
        { value: 100, duration: 500, delay: 1000 },
        { value: 0, duration: 500, delay: 1000 }
    ],
    scaleX: [
        { value: 2, duration: 500, delay: 1000, easing: 'easeOutExpo' },
        { value: 1, duration: 500 }
    ],
    scaleY: [
        { value: 2, duration: 500, delay: 1000, easing: 'easeOutExpo' },
        { value: 1, duration: 500 }
    ]
}, 0, 'linear');
```

## 注意事项

1. 动画属性名称应与PIXI.js对象的属性名称一致
2. 对于复杂的动画，建议使用时间轴而不是嵌套的setTimeout
3. 过多的同时动画可能会影响游戏性能，请适当控制
4. 某些特殊对象(如Window_Base)可能需要特殊处理才能正确动画

## 示例插件

查看`AnimeJS_Examples.js`插件，了解更多实际使用示例。

## 兼容性

- 兼容RPG Maker MZ 1.0.0及以上版本
- 依赖Anime.js 3.2.1或更高版本

## 许可证

MIT许可证
