/*:
 * @plugindesc 布局容器插件 - 提供灵活的UI布局功能
 * <AUTHOR>
 * @target MZ
 * @help
 * ============================================================================
 * 布局容器插件 v1.0.0
 * ============================================================================
 *
 * 这个插件提供了一个LayoutContainer类，继承自PIXI.Container，
 * 实现了多种布局方法，使UI元素的排列更加灵活和简单。
 *
 * 使用方法:
 * 1. 创建LayoutContainer实例: const container = new LayoutContainer();
 * 2. 添加子元素: container.addChild(sprite1, sprite2, ...);
 * 3. 应用布局: container.applyLayout('column', { options });
 *
 * 布局类型:
 * - column: 垂直排列子元素
 * - row: 水平排列子元素
 * - grid: 网格排列子元素
 * - topLeft, topRight, bottomLeft, bottomRight: 对齐到容器的四个角
 * - topCenter, bottomCenter, leftCenter, rightCenter: 对齐到容器的四个边的中心
 * - center: 居中对齐
 *
 * 布局选项:
 * - mainAxisAlignment: 主轴对齐方式 ('start', 'center', 'end', 'space-between', 'space-around')
 * - crossAxisAlignment: 交叉轴对齐方式 ('start', 'center', 'end', 'stretch')
 * - spacing: 元素间距 (数字或百分比字符串)
 * - padding: 内边距 (数字、百分比字符串或{top, right, bottom, left}对象)
 * - columns: 网格布局的列数 (仅用于grid布局)
 *
 * 示例:
 * const container = new LayoutContainer();
 * container.addChild(sprite1, sprite2, sprite3);
 * container.applyLayout('row', {
 *   mainAxisAlignment: 'space-between',
 *   crossAxisAlignment: 'center',
 *   padding: 10
 * });
 *
 * ============================================================================
 */

(function () {
    'use strict';

    //-----------------------------------------------------------------------------
    // LayoutContainer
    //
    // 布局容器类，继承自PIXI.Container，提供多种布局方法
    //-----------------------------------------------------------------------------

    /**
     * 布局容器类
     * @class LayoutContainer
     * @extends PIXI.Container
     */
    function LayoutContainer() {
        this.initialize.apply(this, arguments);
    }

    LayoutContainer.prototype = Object.create(PIXI.Container.prototype);
    LayoutContainer.prototype.constructor = LayoutContainer;

    /**
     * 初始化
     */
    LayoutContainer.prototype.initialize = function () {
        PIXI.Container.call(this);
        this._layoutType = null;
        this._layoutOptions = null;
        this._margin = { top: 0, left: 0 };
        this._enableChildrenLayout = false; // 是否启用子集布局
        this._autoUpdateLayout = false; // 是否自动更新布局
    };

    /**
     * 应用布局
     * @param {string} type - 布局类型 ('column', 'row', 'grid', 'topLeft', 等)
     * @param {Object} options - 布局选项
     * @returns {LayoutContainer} 返回this以支持链式调用
     */
    LayoutContainer.prototype.applyLayout = function (type, options) {
        options = options || {};
        this._layoutType = type;
        this._layoutOptions = options;

        // 默认选项
        var defaultOptions = {
            mainAxisAlignment: 'start',
            crossAxisAlignment: 'start',
            spacing: 0,
            padding: 0,
            columns: 2,
            autoUpdate: false // 默认禁用自动更新
        };

        // 合并选项
        var mergedOptions = {};
        for (var key in defaultOptions) {
            mergedOptions[key] = options.hasOwnProperty(key) ? options[key] : defaultOptions[key];
        }

        // 设置是否启用子集布局
        var isChildrenLayout = ['column', 'row', 'grid'].includes(type);
        this._enableChildrenLayout = isChildrenLayout && mergedOptions.autoUpdate;

        // 当启用子集布局时，自动启用自动布局更新
        this._autoUpdateLayout = this._enableChildrenLayout;

        // 如果是子元素布局且启用了子集布局，覆盖原生的addChild和removeChild方法
        if (this._enableChildrenLayout && !this._originalAddChild) {
            this._setupAutoLayoutUpdates();
        }

        // 应用布局
        switch (type) {
            case 'column':
                this._applyColumnLayout(mergedOptions);
                break;
            case 'row':
                this._applyRowLayout(mergedOptions);
                break;
            case 'grid':
                this._applyGridLayout(mergedOptions);
                break;
            case 'topLeft':
                this._applyTopLeftLayout(mergedOptions);
                break;
            case 'topRight':
                this._applyTopRightLayout(mergedOptions);
                break;
            case 'bottomLeft':
                this._applyBottomLeftLayout(mergedOptions);
                break;
            case 'bottomRight':
                this._applyBottomRightLayout(mergedOptions);
                break;
            case 'topCenter':
                this._applyTopCenterLayout(mergedOptions);
                break;
            case 'bottomCenter':
                this._applyBottomCenterLayout(mergedOptions);
                break;
            case 'leftCenter':
                this._applyLeftCenterLayout(mergedOptions);
                break;
            case 'rightCenter':
                this._applyRightCenterLayout(mergedOptions);
                break;
            case 'center':
                this._applyCenterLayout(mergedOptions);
                break;
            default:
                console.warn("未知的布局类型: " + type);
        }

        return this;
    };

    /**
     * 更新布局
     * 当容器尺寸或子元素变化时调用此方法重新应用布局
     * @returns {LayoutContainer} 返回this以支持链式调用
     */
    LayoutContainer.prototype.updateLayout = function () {
        if (this._layoutType && this._layoutOptions) {
            this.applyLayout(this._layoutType, this._layoutOptions);
        }
        return this;
    };

    /**
     * 添加子元素并更新布局
     * @param {...PIXI.DisplayObject} children - 要添加的子元素
     * @returns {PIXI.DisplayObject} 最后添加的子元素
     */
    LayoutContainer.prototype.addChildAndUpdateLayout = function () {
        var lastChild = PIXI.Container.prototype.addChild.apply(this, arguments);
        this.updateLayout();
        return lastChild;
    };

    /**
     * 移除子元素并更新布局
     * @param {...PIXI.DisplayObject} children - 要移除的子元素
     * @returns {PIXI.DisplayObject} 最后移除的子元素
     */
    LayoutContainer.prototype.removeChildAndUpdateLayout = function () {
        var lastChild = PIXI.Container.prototype.removeChild.apply(this, arguments);
        this.updateLayout();
        return lastChild;
    };

    /**
     * 设置是否启用子集布局
     * @param {boolean} enabled - 是否启用子集布局
     * @returns {LayoutContainer} 返回this以支持链式调用
     */
    LayoutContainer.prototype.setEnableChildrenLayout = function (enabled) {
        if (enabled === this._enableChildrenLayout) return this; // 状态未变，直接返回

        this._enableChildrenLayout = !!enabled;

        // 当启用子集布局时，自动启用自动布局更新
        this._autoUpdateLayout = this._enableChildrenLayout;

        // 如果启用子集布局且尚未设置，则设置自动更新
        if (this._enableChildrenLayout && !this._originalAddChild) {
            this._setupAutoLayoutUpdates();
        }

        console.log(`${this._enableChildrenLayout ? '启用' : '禁用'}子集布局`);
        return this;
    };

    /**
     * 设置是否启用自动布局更新
     * @param {boolean} enabled - 是否启用自动布局更新
     * @returns {LayoutContainer} 返回this以支持链式调用
     */
    LayoutContainer.prototype.setAutoLayoutUpdate = function (enabled) {
        if (enabled === this._autoUpdateLayout) return this; // 状态未变，直接返回

        this._autoUpdateLayout = !!enabled;

        // 如果禁用自动布局更新，也禁用子集布局
        if (!this._autoUpdateLayout) {
            this._enableChildrenLayout = false;
        }

        // 如果启用自动更新且尚未设置，则设置自动更新
        if (this._autoUpdateLayout && !this._originalAddChild) {
            this._setupAutoLayoutUpdates();
        }

        console.log(`${this._autoUpdateLayout ? '启用' : '禁用'}自动布局更新`);
        return this;
    };

    /**
     * 设置自动布局更新
     * 覆盖原生的addChild和removeChild方法，使其在添加或删除子元素时自动更新布局
     * @private
     */
    LayoutContainer.prototype._setupAutoLayoutUpdates = function () {
        // 保存原始方法引用
        this._originalAddChild = this.addChild;
        this._originalRemoveChild = this.removeChild;
        this._originalAddChildAt = this.addChildAt;
        this._originalRemoveChildAt = this.removeChildAt;

        // 覆盖addChild方法
        this.addChild = function () {
            // 保存当前的子元素数量
            var prevChildCount = this.children ? this.children.length : 0;

            // 调用原始方法
            var result = this._originalAddChild.apply(this, arguments);

            // 获取当前的子元素数量
            var currentChildCount = this.children ? this.children.length : 0;

            console.log("添加子元素:", prevChildCount, "->", currentChildCount, "自动更新:", this._autoUpdateLayout);

            // 检查子元素数量是否变化
            if (this._autoUpdateLayout && currentChildCount > prevChildCount) {
                // 如果没有设置布局类型，使用默认的column布局
                if (!this._layoutType || !['column', 'row', 'grid'].includes(this._layoutType)) {
                    console.log("未找到有效的布局类型，应用默认column布局");
                    this._layoutType = 'column';
                    this._layoutOptions = this._layoutOptions || {
                        mainAxisAlignment: 'start',
                        crossAxisAlignment: 'start',
                        spacing: 0,
                        padding: 0
                    };
                }

                console.log("触发自动布局更新:", this._layoutType);

                // 延迟执行，确保在当前执行栈完成后再更新布局
                setTimeout(() => {
                    this.updateLayout();
                    console.log("自动更新布局完成: 添加子元素");
                }, 0);
            }

            return result;
        };

        // 覆盖removeChild方法
        this.removeChild = function () {
            // 保存当前的子元素数量
            var prevChildCount = this.children ? this.children.length : 0;

            // 调用原始方法
            var result = this._originalRemoveChild.apply(this, arguments);

            // 获取当前的子元素数量
            var currentChildCount = this.children ? this.children.length : 0;

            console.log("移除子元素:", prevChildCount, "->", currentChildCount, "自动更新:", this._autoUpdateLayout);

            // 检查子元素数量是否变化
            if (this._autoUpdateLayout && currentChildCount < prevChildCount) {
                // 如果没有设置布局类型，使用默认的column布局
                if (!this._layoutType || !['column', 'row', 'grid'].includes(this._layoutType)) {
                    console.log("未找到有效的布局类型，应用默认column布局");
                    this._layoutType = 'column';
                    this._layoutOptions = this._layoutOptions || {
                        mainAxisAlignment: 'start',
                        crossAxisAlignment: 'start',
                        spacing: 0,
                        padding: 0
                    };
                }

                console.log("触发自动布局更新:", this._layoutType);

                // 延迟执行，确保在当前执行栈完成后再更新布局
                setTimeout(() => {
                    this.updateLayout();
                    console.log("自动更新布局完成: 移除子元素");
                }, 0);
            }

            return result;
        };
    };

    /**
     * 解析间距值
     * @private
     * @param {number|string} spacing - 间距值，可以是数字或百分比字符串
     * @param {number} parentSize - 父容器尺寸
     * @returns {number} 解析后的间距值
     */
    LayoutContainer.prototype._parseSpacing = function (spacing, parentSize) {
        if (typeof spacing === 'string' && spacing.endsWith('%')) {
            var percentage = parseFloat(spacing) / 100;
            return parentSize * percentage;
        }
        return Number(spacing) || 0;
    };

    /**
     * 解析百分比值
     * @private
     * @param {number|string} value - 要解析的值
     * @param {number} base - 基准值
     * @param {boolean} isHeight - 是否是高度值
     * @returns {number} 解析后的值
     */
    LayoutContainer.prototype._parsePercentageValue = function (value, base, isHeight) {
        isHeight = isHeight || false;
        if (value === undefined) {
            return 0;
        }
        if (typeof value === 'string' && value.endsWith('%')) {
            var percentage = parseFloat(value.slice(0, -1));
            return (percentage / 100) * base;
        }
        return Number(value) || 0;
    };

    /**
     * 获取标准化的内边距
     * @private
     * @param {number|string|Object} padding - 内边距值
     * @param {number} parentWidth - 父容器宽度
     * @param {number} parentHeight - 父容器高度
     * @returns {Object} 标准化的内边距对象 {top, right, bottom, left}
     */
    LayoutContainer.prototype._getNormalizedPadding = function (padding, parentWidth, parentHeight) {
        parentWidth = parentWidth || 0;
        parentHeight = parentHeight || 0;

        if (typeof padding === 'number' || padding === undefined) {
            var value = padding || 0;
            return {
                top: value,
                right: value,
                bottom: value,
                left: value
            };
        }

        if (typeof padding === 'string') {
            var value = this._parsePercentageValue(padding, parentHeight, true);
            return {
                top: value,
                right: value,
                bottom: value,
                left: value
            };
        }

        return {
            top: this._parsePercentageValue(padding.top, parentHeight, true),
            right: this._parsePercentageValue(padding.right, parentWidth),
            bottom: this._parsePercentageValue(padding.bottom, parentHeight, true),
            left: this._parsePercentageValue(padding.left, parentWidth)
        };
    };

    /**
     * 应用列布局（垂直排列）
     * @private
     * @param {Object} options - 布局选项
     */
    LayoutContainer.prototype._applyColumnLayout = function (options) {
        // 获取子元素数组
        var children = this.children;
        if (children.length === 0) return;

        // 获取内边距
        var padding = this._getNormalizedPadding(options.padding, this.width, this.height);

        // 解析间距
        var spacing = this._parseSpacing(options.spacing, this.height);

        // 计算子元素总高度和最大宽度
        var totalHeight = 0;
        var maxChildWidth = 0;

        for (var i = 0; i < children.length; i++) {
            totalHeight += children[i].height;
            maxChildWidth = Math.max(maxChildWidth, children[i].width);
        }

        // 计算可用空间
        var availableHeight = this.height === 0
            ? totalHeight + (children.length - 1) * spacing
            : this.height - padding.top - padding.bottom;

        var parentWidth = this.width === 0
            ? maxChildWidth
            : this.width - padding.left - padding.right;

        var currentY = padding.top;

        // 主轴对齐计算
        switch (options.mainAxisAlignment) {
            case 'center':
                var totalSpaceCenter = totalHeight + (children.length - 1) * spacing;
                currentY = padding.top + (availableHeight - totalSpaceCenter) / 2;
                break;
            case 'end':
                var totalSpaceEnd = totalHeight + (children.length - 1) * spacing;
                currentY = padding.top + availableHeight - totalSpaceEnd;
                break;
            case 'space-between':
                if (children.length > 1) {
                    spacing = (availableHeight - totalHeight) / (children.length - 1);
                    currentY = padding.top;
                } else {
                    // 只有一个子元素时居中显示
                    currentY = padding.top + (availableHeight - totalHeight) / 2;
                }
                break;
            case 'space-around':
                if (children.length > 0) {
                    var totalSpacing = availableHeight - totalHeight;
                    spacing = totalSpacing / (children.length + 1);
                    currentY = padding.top + spacing;
                }
                break;
            default: // 'start'
                currentY = padding.top;
        }

        // 布局子元素
        for (var i = 0; i < children.length; i++) {
            var child = children[i];

            // 保存原始尺寸
            var originalWidth = child.width;
            var x = padding.left;

            // 交叉轴对齐计算
            switch (options.crossAxisAlignment) {
                case 'center':
                    x = padding.left + (parentWidth - child.width) / 2;
                    break;
                case 'end':
                    x = padding.left + parentWidth - child.width;
                    break;
                case 'stretch':
                    // 保存原始比例
                    var widthRatio = child.scale.x;
                    child.width = parentWidth;
                    // 保持比例
                    child.scale.x = child.scale.y = widthRatio;
                    x = padding.left;
                    break;
                default: // 'start'
                    x = padding.left;
            }

            // 设置位置
            child.x = x;
            child.y = currentY;

            // 更新下一个元素的Y位置
            currentY += child.height + spacing;

            // 如果是stretch模式且修改了宽度，恢复原始宽度
            if (options.crossAxisAlignment === 'stretch') {
                child.width = originalWidth;
            }
        }
    };

    /**
     * 应用顶部左对齐布局
     * @private
     * @param {Object} options - 布局选项
     */
    LayoutContainer.prototype._applyTopLeftLayout = function (options) {
        var children = this.children;
        if (children.length === 0) return;

        var padding = this._getNormalizedPadding(options.padding, this.width, this.height);

        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            child.x = padding.left;
            child.y = padding.top;
        }
    };

    /**
     * 应用顶部右对齐布局
     * @private
     * @param {Object} options - 布局选项
     */
    LayoutContainer.prototype._applyTopRightLayout = function (options) {
        var children = this.children;
        if (children.length === 0) return;

        var padding = this._getNormalizedPadding(options.padding, this.width, this.height);

        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            child.x = this.width - padding.right - child.width;
            child.y = padding.top;
        }
    };

    /**
     * 应用底部左对齐布局
     * @private
     * @param {Object} options - 布局选项
     */
    LayoutContainer.prototype._applyBottomLeftLayout = function (options) {
        var children = this.children;
        if (children.length === 0) return;

        var padding = this._getNormalizedPadding(options.padding, this.width, this.height);

        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            child.x = padding.left;
            child.y = this.height - padding.bottom - child.height;
        }
    };

    /**
     * 应用底部右对齐布局
     * @private
     * @param {Object} options - 布局选项
     */
    LayoutContainer.prototype._applyBottomRightLayout = function (options) {
        var children = this.children;
        if (children.length === 0) return;

        var padding = this._getNormalizedPadding(options.padding, this.width, this.height);

        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            child.x = this.width - padding.right - child.width;
            child.y = this.height - padding.bottom - child.height;
        }
    };

    /**
     * 应用顶部居中布局
     * @private
     * @param {Object} options - 布局选项
     */
    LayoutContainer.prototype._applyTopCenterLayout = function (options) {
        var children = this.children;
        if (children.length === 0) return;

        var padding = this._getNormalizedPadding(options.padding, this.width, this.height);

        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            child.x = padding.left + (this.width - padding.left - padding.right - child.width) / 2;
            child.y = padding.top;
        }
    };

    /**
     * 应用底部居中布局
     * @private
     * @param {Object} options - 布局选项
     */
    LayoutContainer.prototype._applyBottomCenterLayout = function (options) {
        var children = this.children;
        if (children.length === 0) return;

        var padding = this._getNormalizedPadding(options.padding, this.width, this.height);

        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            child.x = padding.left + (this.width - padding.left - padding.right - child.width) / 2;
            child.y = this.height - padding.bottom - child.height;
        }
    };

    /**
     * 应用左侧居中布局
     * @private
     * @param {Object} options - 布局选项
     */
    LayoutContainer.prototype._applyLeftCenterLayout = function (options) {
        var children = this.children;
        if (children.length === 0) return;

        var padding = this._getNormalizedPadding(options.padding, this.width, this.height);

        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            child.x = padding.left;
            child.y = padding.top + (this.height - padding.top - padding.bottom - child.height) / 2;
        }
    };

    /**
     * 应用右侧居中布局
     * @private
     * @param {Object} options - 布局选项
     */
    LayoutContainer.prototype._applyRightCenterLayout = function (options) {
        var children = this.children;
        if (children.length === 0) return;

        var padding = this._getNormalizedPadding(options.padding, this.width, this.height);

        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            child.x = this.width - padding.right - child.width;
            child.y = padding.top + (this.height - padding.top - padding.bottom - child.height) / 2;
        }
    };

    /**
     * 应用居中布局
     * @private
     * @param {Object} options - 布局选项
     */
    LayoutContainer.prototype._applyCenterLayout = function (options) {
        var children = this.children;
        if (children.length === 0) return;

        var padding = this._getNormalizedPadding(options.padding, this.width, this.height);

        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            child.x = padding.left + (this.width - padding.left - padding.right - child.width) / 2;
            child.y = padding.top + (this.height - padding.top - padding.bottom - child.height) / 2;
        }
    };

    /**
     * 应用行布局（水平排列）
     * @private
     * @param {Object} options - 布局选项
     */
    LayoutContainer.prototype._applyRowLayout = function (options) {
        // 获取子元素数组
        var children = this.children;
        if (children.length === 0) return;

        // 获取内边距
        var padding = this._getNormalizedPadding(options.padding, this.width, this.height);

        // 解析间距
        var spacing = this._parseSpacing(options.spacing, this.width);

        // 计算子元素总宽度和最大高度
        var totalWidth = 0;
        var maxChildHeight = 0;

        for (var i = 0; i < children.length; i++) {
            totalWidth += children[i].width;
            maxChildHeight = Math.max(maxChildHeight, children[i].height);
        }

        // 计算可用空间
        var availableWidth = this.width === 0
            ? totalWidth + (children.length - 1) * spacing
            : this.width - padding.left - padding.right;

        var parentHeight = this.height === 0
            ? maxChildHeight
            : this.height - padding.top - padding.bottom;

        var currentX = padding.left;

        // 主轴对齐计算
        switch (options.mainAxisAlignment) {
            case 'center':
                currentX = padding.left + (availableWidth - totalWidth - (children.length - 1) * spacing) / 2;
                break;
            case 'end':
                currentX = padding.left + availableWidth - totalWidth - (children.length - 1) * spacing;
                break;
            case 'space-between':
                if (children.length > 1) {
                    spacing = (availableWidth - totalWidth) / (children.length - 1);
                    currentX = padding.left;
                } else {
                    // 只有一个子元素时居中显示
                    currentX = padding.left + (availableWidth - totalWidth) / 2;
                }
                break;
            case 'space-around':
                if (children.length > 0) {
                    var totalSpacing = availableWidth - totalWidth;
                    spacing = totalSpacing / (children.length + 1);
                    currentX = padding.left + spacing;
                }
                break;
            default: // 'start'
                currentX = padding.left;
        }

        // 布局子元素
        for (var i = 0; i < children.length; i++) {
            var child = children[i];

            // 保存原始尺寸
            var originalHeight = child.height;
            var y = padding.top;

            // 交叉轴对齐计算
            switch (options.crossAxisAlignment) {
                case 'center':
                    y = padding.top + (parentHeight - child.height) / 2;
                    break;
                case 'end':
                    y = padding.top + parentHeight - child.height;
                    break;
                case 'stretch':
                    // 保存原始比例
                    var heightRatio = child.scale.y;
                    child.height = parentHeight;
                    // 保持比例
                    child.scale.y = child.scale.x = heightRatio;
                    y = padding.top;
                    break;
                default: // 'start'
                    y = padding.top;
            }

            // 设置位置
            child.x = currentX;
            child.y = y;

            // 更新下一个元素的X位置
            currentX += child.width + spacing;

            // 如果是stretch模式且修改了高度，恢复原始高度
            if (options.crossAxisAlignment === 'stretch') {
                child.height = originalHeight;
            }
        }
    };

    /**
     * 应用网格布局
     * @private
     * @param {Object} options - 布局选项
     */
    LayoutContainer.prototype._applyGridLayout = function (options) {
        // 获取子元素数组
        var children = this.children;
        if (children.length === 0) return;

        var columns = options.columns || 2;
        var spacing = this._parseSpacing(options.spacing, this.width);
        var padding = this._getNormalizedPadding(options.padding, this.width, this.height);

        // 计算子元素的最大宽度和高度
        var maxChildWidth = 0;
        for (var i = 0; i < children.length; i++) {
            maxChildWidth = Math.max(maxChildWidth, children[i].width);
        }

        // 计算可用宽度和单元格宽度
        var availableWidth = this.width === 0
            ? Math.max(maxChildWidth * columns, maxChildWidth) + (columns - 1) * spacing
            : this.width - padding.left - padding.right;

        var cellWidth = (availableWidth - (columns - 1) * spacing) / columns;

        // 计算每行的高度和总高度
        var rowHeights = [];
        var currentRow = 0;
        var maxHeightInRow = 0;
        var totalHeight = 0;
        var rowSpacing = spacing;

        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            var row = Math.floor(i / columns);
            if (row !== currentRow) {
                rowHeights.push(maxHeightInRow);
                totalHeight += maxHeightInRow + rowSpacing;
                currentRow = row;
                maxHeightInRow = child.height;
            } else {
                maxHeightInRow = Math.max(maxHeightInRow, child.height);
            }
        }

        if (children.length > 0) {
            rowHeights.push(maxHeightInRow);
            totalHeight += maxHeightInRow;
        }

        // 计算父容器实际高度和可用高度
        var parentHeight = this.height === 0
            ? totalHeight + padding.top + padding.bottom
            : this.height;
        var availableHeight = parentHeight - padding.top - padding.bottom;

        // 计算垂直间距和起始位置
        var startY = padding.top;
        var verticalGap = spacing;

        switch (options.mainAxisAlignment) {
            case 'center':
                startY = padding.top + (availableHeight - totalHeight) / 2;
                break;
            case 'end':
                startY = padding.top + availableHeight - totalHeight;
                break;
            case 'space-between':
                if (rowHeights.length > 1) {
                    verticalGap = (availableHeight - totalHeight + spacing) / (rowHeights.length - 1);
                    startY = padding.top;
                } else {
                    startY = padding.top + (availableHeight - totalHeight) / 2;
                }
                break;
            case 'space-around':
                if (rowHeights.length > 0) {
                    verticalGap = (availableHeight - totalHeight + spacing) / (rowHeights.length + 1);
                    startY = padding.top + verticalGap;
                }
                break;
            default: // 'start'
                startY = padding.top;
        }

        // 计算水平起始位置
        var startX = padding.left;
        var totalColumnsWidth = cellWidth * columns + spacing * (columns - 1);

        switch (options.mainAxisAlignment) {
            case 'center':
                startX = padding.left + (availableWidth - totalColumnsWidth) / 2;
                break;
            case 'end':
                startX = padding.left + availableWidth - totalColumnsWidth;
                break;
            default: // 'start'
                startX = padding.left;
        }

        // 布局子元素
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            var row = Math.floor(i / columns);
            var col = i % columns;

            // 计算垂直位置
            var y = startY;
            for (var j = 0; j < row; j++) {
                y += rowHeights[j] + verticalGap;
            }

            // 计算水平位置
            var x = startX + col * (cellWidth + spacing);

            // 保存原始尺寸和比例
            var originalWidth = child.width;
            var originalHeight = child.height;
            var originalScaleX = child.scale.x;
            var originalScaleY = child.scale.y;

            if (options.crossAxisAlignment === 'stretch') {
                // 保持原始比例进行缩放
                var scaleX = cellWidth / originalWidth;
                var scaleY = rowHeights[row] / originalHeight;
                var scale = Math.min(scaleX, scaleY);
                child.scale.set(scale);

                // 设置新尺寸
                child.width = cellWidth;
                child.height = rowHeights[row];
            } else {
                // 垂直对齐
                switch (options.crossAxisAlignment) {
                    case 'center':
                        y += (rowHeights[row] - child.height) / 2;
                        break;
                    case 'end':
                        y += rowHeights[row] - child.height;
                        break;
                    default: // 'start'
                    // 不需要调整
                }

                // 水平对齐
                switch (options.crossAxisAlignment) {
                    case 'center':
                        x += (cellWidth - child.width) / 2;
                        break;
                    case 'end':
                        x += cellWidth - child.width;
                        break;
                    default: // 'start'
                    // 不需要调整
                }
            }

            // 设置位置
            child.x = x;
            child.y = y;

            // 如果是stretch模式，恢复原始尺寸和比例
            if (options.crossAxisAlignment === 'stretch') {
                child.width = originalWidth;
                child.height = originalHeight;
                child.scale.set(originalScaleX, originalScaleY);
            }
        }
    };

    // 将LayoutContainer类添加到全局作用域
    window.LayoutContainer = LayoutContainer;
})();
