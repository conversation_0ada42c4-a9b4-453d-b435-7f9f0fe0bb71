// 测试路径处理功能
console.log("测试路径处理功能");

// 模拟Rust中的process_image_path函数
function processImagePath(originalPath) {
    // 如果是完整路径（以 ../projects/ 开头），直接使用
    if (originalPath.startsWith("../projects/")) {
        // 提取相对路径部分（从 img/ 开始）
        const parts = originalPath.split('/');
        let relativePath;
        
        if (parts.length >= 4) {
            // 重新组合从 img 开始的路径
            const imgPath = parts.slice(3).join("/");
            relativePath = `/${imgPath}`;
        } else {
            // 如果路径格式不正确，使用原路径
            relativePath = originalPath;
        }

        // 生成条件路径代码：完整路径 vs 相对路径
        return `window.mzl ? '${originalPath}' : '${relativePath}'`;
    } else {
        // 如果不是完整路径，直接使用原路径
        return `'${originalPath}'`;
    }
}

// 测试用例
const testCases = [
    "../projects/Project4/img/titles1/Ruins.png",
    "../projects/Project4/img/characters/!Door2.png", 
    "../projects/MyProject/img/faces/Actor1.png",
    "../projects/TestProject/img/pictures/Background.jpg",
    "img/titles1/Ruins.png",
    "/img/characters/Actor1.png",
    "characters/Actor1.png"
];

console.log('\n=== 测试路径处理结果 ===');
testCases.forEach((testPath, index) => {
    const result = processImagePath(testPath);
    console.log(`\n测试 ${index + 1}:`);
    console.log(`输入: ${testPath}`);
    console.log(`输出: ${result}`);
    
    // 模拟生成的代码
    console.log(`生成的代码: const fullPath_${index} = ${result};`);
});

// 测试实际的条件判断
console.log('\n=== 测试条件判断 ===');

// 模拟window.mzl为true的情况
window.mzl = true;
console.log('当 window.mzl = true 时:');
testCases.slice(0, 4).forEach((testPath, index) => {
    const result = processImagePath(testPath);
    // 解析并执行条件表达式
    if (testPath.startsWith("../projects/")) {
        const parts = testPath.split('/');
        const relativePath = parts.length >= 4 ? `/${parts.slice(3).join("/")}` : testPath;
        const actualPath = window.mzl ? testPath : relativePath;
        console.log(`  ${testPath} -> ${actualPath}`);
    }
});

// 模拟window.mzl为false的情况
window.mzl = false;
console.log('\n当 window.mzl = false 时:');
testCases.slice(0, 4).forEach((testPath, index) => {
    const result = processImagePath(testPath);
    // 解析并执行条件表达式
    if (testPath.startsWith("../projects/")) {
        const parts = testPath.split('/');
        const relativePath = parts.length >= 4 ? `/${parts.slice(3).join("/")}` : testPath;
        const actualPath = window.mzl ? testPath : relativePath;
        console.log(`  ${testPath} -> ${actualPath}`);
    }
});

// 测试边界情况
console.log('\n=== 测试边界情况 ===');
const edgeCases = [
    "../projects/",
    "../projects/Project4",
    "../projects/Project4/",
    "../projects/Project4/img",
    "../projects/Project4/img/",
    "",
    "invalid/path"
];

edgeCases.forEach((testPath, index) => {
    const result = processImagePath(testPath);
    console.log(`边界测试 ${index + 1}: "${testPath}" -> ${result}`);
});

console.log('\n=== 测试完成 ===');

// 验证最终生成的代码格式
console.log('\n=== 验证生成的代码格式 ===');
const samplePath = "../projects/Project4/img/characters/!Door2.png";
const generatedCode = `
// 加载图片 1
const fullPath_0 = ${processImagePath(samplePath)};
const newBitmap_0 = ImageManager.loadBitmapFromUrl(fullPath_0);
newBitmap_0.addLoadListener(function (bitmap) {
    // 设置bitmap（这会自动触发必要的更新）
    targetObject_sprite_Scene_Title_0_56135.bitmap = bitmap;
    if (DEBUG) log('图片加载完成，已设置bitmap:', fullPath_0);
});
`;

console.log('生成的完整代码:');
console.log(generatedCode);
