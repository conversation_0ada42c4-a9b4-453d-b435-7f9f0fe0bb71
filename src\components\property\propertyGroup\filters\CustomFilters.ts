/**
 * 自定义滤镜类定义
 * 包含所有自定义的PIXI滤镜效果
 */

// 烟雾滤镜
export class SmokeFilter extends (window as any).PIXI.Filter {
  constructor() {
    const vertexShader = `
      attribute vec2 aVertexPosition;
      attribute vec2 aTextureCoord;
      uniform mat3 projectionMatrix;
      varying vec2 vTextureCoord;
      void main(void) {
        gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
        vTextureCoord = aTextureCoord;
      }
    `;

    const fragmentShader = `
      varying vec2 vTextureCoord;
      uniform sampler2D uSampler;
      uniform float time;
      uniform float intensity;

      void main(void) {
        vec2 uv = vTextureCoord;

        // 烟雾效果
        float noise = fract(sin(dot(uv + time * 0.05, vec2(12.9898, 78.233))) * 43758.5453);
        noise = noise * 0.5 - 0.25;

        vec2 offset = vec2(noise * intensity * 0.02);
        vec4 color = texture2D(uSampler, uv + offset);

        // 添加一些灰色调
        float gray = (color.r + color.g + color.b) / 3.0;
        vec4 smokeColor = mix(color, vec4(gray, gray, gray, color.a), intensity * 0.3);

        gl_FragColor = smokeColor;
      }
    `;

    super(vertexShader, fragmentShader);

    this.uniforms.time = 0.0;
    this.uniforms.intensity = 0.5;

    // 自动更新时间
    this.autoUpdate = true;
    this._lastTime = Date.now();
  }

  // 应用滤镜前更新时间
  apply(filterManager: any, input: any, output: any, clear: any) {
    if (this.autoUpdate) {
      const now = Date.now();
      this.uniforms.time += (now - this._lastTime) * 0.001;
      this._lastTime = now;
    }

    super.apply(filterManager, input, output, clear);
  }
}

// 火焰滤镜
export class FireFilter extends (window as any).PIXI.Filter {
  constructor() {
    const vertexShader = `
      attribute vec2 aVertexPosition;
      attribute vec2 aTextureCoord;
      uniform mat3 projectionMatrix;
      varying vec2 vTextureCoord;
      void main(void) {
        gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
        vTextureCoord = aTextureCoord;
      }
    `;

    const fragmentShader = `
      varying vec2 vTextureCoord;
      uniform sampler2D uSampler;
      uniform float time;
      uniform float intensity;
      uniform vec3 fireColor;

      float random(vec2 st) {
        return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
      }

      float noise(vec2 st) {
        vec2 i = floor(st);
        vec2 f = fract(st);

        float a = random(i);
        float b = random(i + vec2(1.0, 0.0));
        float c = random(i + vec2(0.0, 1.0));
        float d = random(i + vec2(1.0, 1.0));

        vec2 u = f * f * (3.0 - 2.0 * f);

        return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
      }

      void main(void) {
        vec2 uv = vTextureCoord;
        vec4 color = texture2D(uSampler, uv);

        // 火焰效果
        float n = noise(vec2(uv.x * 10.0, (uv.y * 10.0 - time * 2.0)));
        float mask = 1.0 - uv.y;
        mask = pow(mask, 1.0 + intensity * 2.0) * 2.0;

        float fire = n * mask;
        fire = smoothstep(0.1, 0.9, fire);

        // 混合原始颜色和火焰颜色
        vec3 fireColorMix = mix(color.rgb, fireColor, fire * intensity);

        gl_FragColor = vec4(fireColorMix, color.a);
      }
    `;

    super(vertexShader, fragmentShader);

    this.uniforms.time = 0.0;
    this.uniforms.intensity = 0.5;
    this.uniforms.fireColor = [1.0, 0.5, 0.0]; // 橙色火焰

    // 自动更新时间
    this.autoUpdate = true;
    this._lastTime = Date.now();
  }

  // 应用滤镜前更新时间
  apply(filterManager: any, input: any, output: any, clear: any) {
    if (this.autoUpdate) {
      const now = Date.now();
      this.uniforms.time += (now - this._lastTime) * 0.001;
      this._lastTime = now;
    }

    super.apply(filterManager, input, output, clear);
  }
}

// 水波滤镜
export class WaterFilter extends (window as any).PIXI.Filter {
  constructor() {
    const vertexShader = `
      attribute vec2 aVertexPosition;
      attribute vec2 aTextureCoord;
      uniform mat3 projectionMatrix;
      varying vec2 vTextureCoord;
      void main(void) {
        gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
        vTextureCoord = aTextureCoord;
      }
    `;

    const fragmentShader = `
      varying vec2 vTextureCoord;
      uniform sampler2D uSampler;
      uniform float time;
      uniform float amplitude;
      uniform float frequency;

      void main(void) {
        vec2 uv = vTextureCoord;

        // 水波效果
        float x = uv.x * frequency + time;
        float y = uv.y * frequency + time;

        float sinX = sin(x) * amplitude * 0.01;
        float sinY = sin(y) * amplitude * 0.01;

        vec2 offset = vec2(sinX, sinY);

        gl_FragColor = texture2D(uSampler, uv + offset);
      }
    `;

    super(vertexShader, fragmentShader);

    this.uniforms.time = 0.0;
    this.uniforms.amplitude = 5.0;
    this.uniforms.frequency = 10.0;

    // 自动更新时间
    this.autoUpdate = true;
    this._lastTime = Date.now();
  }

  // 应用滤镜前更新时间
  apply(filterManager: any, input: any, output: any, clear: any) {
    if (this.autoUpdate) {
      const now = Date.now();
      this.uniforms.time += (now - this._lastTime) * 0.001;
      this._lastTime = now;
    }

    super.apply(filterManager, input, output, clear);
  }
}

// 布料滤镜
export class ClothFilter extends (window as any).PIXI.Filter {
  constructor() {
    const vertexShader = `
      attribute vec2 aVertexPosition;
      attribute vec2 aTextureCoord;
      uniform mat3 projectionMatrix;
      varying vec2 vTextureCoord;
      void main(void) {
        gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
        vTextureCoord = aTextureCoord;
      }
    `;

    const fragmentShader = `
      varying vec2 vTextureCoord;
      uniform sampler2D uSampler;
      uniform float time;
      uniform float intensity;
      uniform float waveFrequency;

      void main(void) {
        vec2 uv = vTextureCoord;

        // 布料波动效果
        float wave = sin(uv.y * waveFrequency + time) * intensity * 0.01;
        vec2 distortion = vec2(wave, 0.0);

        // 添加一些细微的纹理
        float noise = fract(sin(dot(uv, vec2(12.9898, 78.233))) * 43758.5453);
        distortion += vec2(noise * intensity * 0.002);

        gl_FragColor = texture2D(uSampler, uv + distortion);
      }
    `;

    super(vertexShader, fragmentShader);

    this.uniforms.time = 0.0;
    this.uniforms.intensity = 5.0;
    this.uniforms.waveFrequency = 20.0;

    // 自动更新时间
    this.autoUpdate = true;
    this._lastTime = Date.now();
  }

  // 应用滤镜前更新时间
  apply(filterManager: any, input: any, output: any, clear: any) {
    if (this.autoUpdate) {
      const now = Date.now();
      this.uniforms.time += (now - this._lastTime) * 0.001;
      this._lastTime = now;
    }

    super.apply(filterManager, input, output, clear);
  }
}

// 光晕滤镜
export class GlowFilter extends (window as any).PIXI.Filter {
  constructor() {
    const vertexShader = `
      attribute vec2 aVertexPosition;
      attribute vec2 aTextureCoord;
      uniform mat3 projectionMatrix;
      varying vec2 vTextureCoord;
      void main(void) {
        gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
        vTextureCoord = aTextureCoord;
      }
    `;

    const fragmentShader = `
      varying vec2 vTextureCoord;
      uniform sampler2D uSampler;
      uniform float intensity;
      uniform vec3 glowColor;
      uniform float time;

      void main(void) {
        vec4 color = texture2D(uSampler, vTextureCoord);

        // 计算发光效果
        float glow = 0.0;
        float blurSize = 0.01 * intensity;

        // 简单的模糊采样
        for(float x = -4.0; x <= 4.0; x += 1.0) {
            for(float y = -4.0; y <= 4.0; y += 1.0) {
                vec2 offset = vec2(x, y) * blurSize;
                glow += texture2D(uSampler, vTextureCoord + offset).a;
            }
        }

        glow /= 81.0; // 9x9 采样
        glow *= intensity;

        // 添加一些脉动效果
        float pulse = (sin(time) * 0.1 + 0.9) * intensity;

        // 混合原始颜色和发光颜色
        vec3 finalColor = mix(color.rgb, glowColor, glow * pulse);

        gl_FragColor = vec4(finalColor, color.a);
      }
    `;

    super(vertexShader, fragmentShader);

    this.uniforms.intensity = 0.5;
    this.uniforms.glowColor = [1.0, 1.0, 0.5]; // 黄色光晕
    this.uniforms.time = 0.0;

    // 自动更新时间
    this.autoUpdate = true;
    this._lastTime = Date.now();
  }

  // 应用滤镜前更新时间
  apply(filterManager: any, input: any, output: any, clear: any) {
    if (this.autoUpdate) {
      const now = Date.now();
      this.uniforms.time += (now - this._lastTime) * 0.001;
      this._lastTime = now;
    }

    super.apply(filterManager, input, output, clear);
  }
}

// 冰霜滤镜
export class FrostFilter extends (window as any).PIXI.Filter {
  constructor() {
    const vertexShader = `
      attribute vec2 aVertexPosition;
      attribute vec2 aTextureCoord;
      uniform mat3 projectionMatrix;
      varying vec2 vTextureCoord;
      void main(void) {
        gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
        vTextureCoord = aTextureCoord;
      }
    `;

    const fragmentShader = `
      varying vec2 vTextureCoord;
      uniform sampler2D uSampler;
      uniform float intensity;
      uniform float time;

      float random(vec2 st) {
        return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
      }

      float noise(vec2 st) {
        vec2 i = floor(st);
        vec2 f = fract(st);

        float a = random(i);
        float b = random(i + vec2(1.0, 0.0));
        float c = random(i + vec2(0.0, 1.0));
        float d = random(i + vec2(1.0, 1.0));

        vec2 u = f * f * (3.0 - 2.0 * f);

        return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
      }

      void main(void) {
        vec2 uv = vTextureCoord;

        // 冰霜效果
        float n = noise(uv * 10.0 + time * 0.1);
        float frost = smoothstep(0.3, 0.7, n) * intensity;

        // 扭曲原始图像
        vec2 offset = vec2(n - 0.5) * intensity * 0.02;
        vec4 color = texture2D(uSampler, uv + offset);

        // 添加冰霜颜色
        vec3 frostColor = vec3(0.8, 0.9, 1.0); // 淡蓝色
        vec3 finalColor = mix(color.rgb, frostColor, frost * 0.7);

        gl_FragColor = vec4(finalColor, color.a);
      }
    `;

    super(vertexShader, fragmentShader);

    this.uniforms.intensity = 0.5;
    this.uniforms.time = 0.0;

    // 自动更新时间
    this.autoUpdate = true;
    this._lastTime = Date.now();
  }

  // 应用滤镜前更新时间
  apply(filterManager: any, input: any, output: any, clear: any) {
    if (this.autoUpdate) {
      const now = Date.now();
      this.uniforms.time += (now - this._lastTime) * 0.001;
      this._lastTime = now;
    }

    super.apply(filterManager, input, output, clear);
  }
}

// 高级色彩调整滤镜
export class AdvancedColorFilter extends (window as any).PIXI.Filter {
  constructor() {
    const vertexShader = `
      attribute vec2 aVertexPosition;
      attribute vec2 aTextureCoord;
      uniform mat3 projectionMatrix;
      varying vec2 vTextureCoord;
      void main(void) {
        gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
        vTextureCoord = aTextureCoord;
      }
    `;

    const fragmentShader = `
      varying vec2 vTextureCoord;
      uniform sampler2D uSampler;

      // 色彩调整参数
      uniform float contrast;      // 对比度
      uniform float brightness;    // 亮度
      uniform float shadows;       // 暗部调整
      uniform float highlights;    // 亮部调整
      uniform float saturation;    // 饱和度
      uniform float vibrance;      // 自然饱和度

      // 将RGB转换为亮度值
      float getLuminance(vec3 color) {
        return dot(color, vec3(0.299, 0.587, 0.114));
      }

      void main(void) {
        vec4 color = texture2D(uSampler, vTextureCoord);
        vec3 rgb = color.rgb;

        // 亮度调整
        rgb = rgb * brightness;

        // 对比度调整
        rgb = (rgb - 0.5) * contrast + 0.5;

        // 计算亮度
        float luminance = getLuminance(rgb);

        // 暗部调整 (影响暗部区域)
        if (luminance < 0.5) {
          // 暗部区域的调整强度随亮度降低而增加
          float shadowsEffect = (0.5 - luminance) * 2.0;
          rgb = mix(rgb, vec3(0.0), -shadows * shadowsEffect);
        }

        // 亮部调整 (影响亮部区域)
        if (luminance > 0.5) {
          // 亮部区域的调整强度随亮度增加而增加
          float highlightsEffect = (luminance - 0.5) * 2.0;
          rgb = mix(rgb, vec3(1.0), highlights * highlightsEffect);
        }

        // 饱和度调整
        float luminance2 = getLuminance(rgb);
        vec3 satColor = mix(vec3(luminance2), rgb, saturation);

        // 自然饱和度调整 (低饱和度区域受影响更大)
        float satLevel = length(rgb - vec3(luminance2)) / sqrt(3.0);
        vec3 vibColor = mix(vec3(luminance2), rgb, 1.0 + vibrance * (1.0 - satLevel));

        // 混合饱和度和自然饱和度效果
        rgb = mix(satColor, vibColor, 0.5);

        // 确保颜色在有效范围内
        rgb = clamp(rgb, 0.0, 1.0);

        gl_FragColor = vec4(rgb, color.a);
      }
    `;

    super(vertexShader, fragmentShader);

    // 初始化参数
    this.uniforms.contrast = 1.0;     // 1.0 = 正常, >1.0 = 增加对比度, <1.0 = 降低对比度
    this.uniforms.brightness = 1.0;   // 1.0 = 正常, >1.0 = 增加亮度, <1.0 = 降低亮度
    this.uniforms.shadows = 0.0;      // 0.0 = 正常, <0.0 = 提亮暗部, >0.0 = 压暗暗部
    this.uniforms.highlights = 0.0;   // 0.0 = 正常, <0.0 = 压暗亮部, >0.0 = 提亮亮部
    this.uniforms.saturation = 1.0;   // 1.0 = 正常, >1.0 = 增加饱和度, <1.0 = 降低饱和度
    this.uniforms.vibrance = 0.0;     // 0.0 = 正常, >0.0 = 增加自然饱和度
  }
}

// 注册自定义滤镜到全局
export const registerCustomFilters = () => {
  (window as any).SmokeFilter = SmokeFilter;
  (window as any).FireFilter = FireFilter;
  (window as any).WaterFilter = WaterFilter;
  (window as any).ClothFilter = ClothFilter;
  (window as any).GlowFilter = GlowFilter;
  (window as any).FrostFilter = FrostFilter;
  (window as any).AdvancedColorFilter = AdvancedColorFilter;
};