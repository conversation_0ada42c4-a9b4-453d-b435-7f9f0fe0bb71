import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Divider,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  Tooltip,
  Checkbox,
  Switch
} from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

// 布局图标
import ColumnIcon from '@mui/icons-material/ViewColumn';
import RowIcon from '@mui/icons-material/ViewStream';
import GridOnIcon from '@mui/icons-material/GridOn';
import AlignHorizontalLeftIcon from '@mui/icons-material/AlignHorizontalLeft';
import AlignHorizontalRightIcon from '@mui/icons-material/AlignHorizontalRight';
import AlignHorizontalCenterIcon from '@mui/icons-material/AlignHorizontalCenter';
import AlignVerticalCenterIcon from '@mui/icons-material/AlignVerticalCenter';
import VerticalAlignTopIcon from '@mui/icons-material/VerticalAlignTop';
import VerticalAlignBottomIcon from '@mui/icons-material/VerticalAlignBottom';
import CenterFocusStrongIcon from '@mui/icons-material/CenterFocusStrong';

import useProjectStore from '../../../store/Store';
import SelectInput, { SelectOption } from '../../ui/SelectInput';
import SliderInput from '../../ui/SliderInput';

// 调试模式开关
const DEBUG_MODE = false;

// 调试日志函数
const debugLog = (...args: any[]) => {
  if (DEBUG_MODE) {
    console.log('[LayoutPanel]', ...args);
  }
};

// 自身布局类型
const selfLayoutTypes = [
  { value: 'topLeft', label: 'topLeft', icon: <AlignHorizontalLeftIcon fontSize="small" color="primary" /> },
  { value: 'topCenter', label: 'topCenter', icon: <AlignHorizontalCenterIcon fontSize="small" color="primary" /> },
  { value: 'topRight', label: 'topRight', icon: <AlignHorizontalRightIcon fontSize="small" color="primary" /> },
  { value: 'leftCenter', label: 'leftCenter', icon: <AlignVerticalCenterIcon fontSize="small" color="primary" /> },
  { value: 'center', label: 'center', icon: <CenterFocusStrongIcon fontSize="small" color="primary" /> },
  { value: 'rightCenter', label: 'rightCenter', icon: <AlignVerticalCenterIcon fontSize="small" color="primary" /> },
  { value: 'bottomLeft', label: 'bottomLeft', icon: <AlignHorizontalLeftIcon fontSize="small" color="primary" /> },
  { value: 'bottomCenter', label: 'bottomCenter', icon: <AlignHorizontalCenterIcon fontSize="small" color="primary" /> },
  { value: 'bottomRight', label: 'bottomRight', icon: <AlignHorizontalRightIcon fontSize="small" color="primary" /> },
];

// 子元素布局类型
const childrenLayoutTypes = [
  { value: 'column', label: 'column', icon: <ColumnIcon fontSize="small" color="primary" /> },
  { value: 'row', label: 'row', icon: <RowIcon fontSize="small" color="primary" /> },
  { value: 'grid', label: 'grid', icon: <GridOnIcon fontSize="small" color="primary" /> }
];

// 主轴对齐方式选项
const mainAxisAlignments: SelectOption[] = [
  { value: 'start', label: 'start', icon: <AlignHorizontalLeftIcon fontSize="small" color="primary" /> },
  { value: 'center', label: 'center', icon: <AlignHorizontalCenterIcon fontSize="small" color="primary" /> },
  { value: 'end', label: 'end', icon: <AlignHorizontalRightIcon fontSize="small" color="primary" /> },
  { value: 'space-between', label: 'space-between', icon: <AlignHorizontalLeftIcon fontSize="small" color="primary" /> },
  { value: 'space-around', label: 'space-around', icon: <AlignHorizontalCenterIcon fontSize="small" color="primary" /> },
  { value: 'space-evenly', label: 'space-evenly', icon: <AlignHorizontalCenterIcon fontSize="small" color="primary" /> }
];

// 交叉轴对齐方式选项
const crossAxisAlignments: SelectOption[] = [
  { value: 'start', label: 'start', icon: <VerticalAlignTopIcon fontSize="small" color="primary" /> },
  { value: 'center', label: 'center', icon: <AlignVerticalCenterIcon fontSize="small" color="primary" /> },
  { value: 'end', label: 'end', icon: <VerticalAlignBottomIcon fontSize="small" color="primary" /> },
  { value: 'stretch', label: 'stretch', icon: <AlignVerticalCenterIcon fontSize="small" color="primary" /> }
];

// 类型定义
interface LayoutObject {
  children?: any[];
  parent?: any;
  _layoutType?: string;
  _layoutOptions?: any;
  _margin?: { top: number; left: number };
  _enableChildrenLayout?: boolean;
  _autoUpdateLayout?: boolean;
  _fitStageWidth?: boolean;
  _fitStageHeight?: boolean;
  _fitParentWidth?: boolean;
  _fitParentHeight?: boolean;
  _originalWidth?: number;
  _originalHeight?: number;
  width?: number;
  height?: number;
  applyLayout?: (type: string, options: any) => void;
  setAutoLayoutUpdate?: (enabled: boolean) => void;
  setEnableChildrenLayout?: (enabled: boolean) => void;
  getLayoutState?: () => any;
  clearLayout?: () => void;
}

const LayoutPanel: React.FC = () => {
  const selectedObject = useProjectStore(state => state.selectedObjects.objects[0]) as LayoutObject | null;

  // 布局状态
  const [selfLayoutType, setSelfLayoutType] = useState<string>('topLeft');
  const [childrenLayoutType, setChildrenLayoutType] = useState<string>('column');
  const [mainAxisAlignment, setMainAxisAlignment] = useState<string>('start');
  const [crossAxisAlignment, setCrossAxisAlignment] = useState<string>('start');
  const [horizontalSpacing, setHorizontalSpacing] = useState<number>(0);
  const [verticalSpacing, setVerticalSpacing] = useState<number>(0);
  const [padding, setPadding] = useState<number>(0);
  const [columns, setColumns] = useState<number>(2);
  const [hasChildren, setHasChildren] = useState<boolean>(false);
  const [hasParent, setHasParent] = useState<boolean>(false);
  const [enableChildrenLayout, setEnableChildrenLayout] = useState<boolean>(false);

  // 外边距状态
  const [marginTop, setMarginTop] = useState<number>(0);
  const [marginLeft, setMarginLeft] = useState<number>(0);

  // 尺寸适配状态
  const [fitStageWidth, setFitStageWidth] = useState<boolean>(false);
  const [fitStageHeight, setFitStageHeight] = useState<boolean>(false);
  const [fitParentWidth, setFitParentWidth] = useState<boolean>(false);
  const [fitParentHeight, setFitParentHeight] = useState<boolean>(false);

  // 工具函数：检查是否为子元素布局类型
  const isChildrenLayoutType = useCallback((type: string) => {
    return ['column', 'row', 'grid'].includes(type);
  }, []);

  // 工具函数：检查是否为自身布局类型
  const isSelfLayoutType = useCallback((type: string) => {
    return selfLayoutTypes.some(layout => layout.value === type);
  }, []);

  // 检查选中对象类型和属性
  useEffect(() => {
    if (!selectedObject) return;

    debugLog('检查选中对象:', selectedObject.constructor?.name);

    // 检查对象是否有children属性
    const objectHasChildren = !!(selectedObject.children && Array.isArray(selectedObject.children));
    setHasChildren(objectHasChildren);

    // 检查对象是否有父容器
    const objectHasParent = selectedObject.parent !== null && selectedObject.parent !== undefined;
    setHasParent(objectHasParent);

    debugLog(`对象状态: 有子元素=${objectHasChildren}, 有父容器=${objectHasParent}`);

    // 获取子集布局启用状态
    setEnableChildrenLayout(selectedObject._enableChildrenLayout || false);

    // 如果对象已有布局属性，使用它们
    if (selectedObject._layoutType) {
      if (isSelfLayoutType(selectedObject._layoutType)) {
        setSelfLayoutType(selectedObject._layoutType);
      } else if (isChildrenLayoutType(selectedObject._layoutType)) {
        setChildrenLayoutType(selectedObject._layoutType);
      }

      // 获取布局选项
      const options = selectedObject._layoutOptions || {};
      setMainAxisAlignment(options.mainAxisAlignment || 'start');
      setCrossAxisAlignment(options.crossAxisAlignment || 'start');
      setHorizontalSpacing(options.horizontalSpacing || 0);
      setVerticalSpacing(options.verticalSpacing || 0);
      setPadding(options.padding || 0);
      setColumns(options.columns || 2);
    } else {
      // 默认设置
      setSelfLayoutType('topLeft');
      setChildrenLayoutType('column');
    }

    // 初始化margin属性
    if (!selectedObject._margin) {
      selectedObject._margin = { top: 0, left: 0 };
    }

    // 更新外边距状态
    setMarginTop(selectedObject._margin.top || 0);
    setMarginLeft(selectedObject._margin.left || 0);

    // 检查对象是否设置了尺寸适配
    setFitStageWidth(!!selectedObject._fitStageWidth);
    setFitStageHeight(!!selectedObject._fitStageHeight);
    setFitParentWidth(!!selectedObject._fitParentWidth);
    setFitParentHeight(!!selectedObject._fitParentHeight);
  }, [selectedObject, isSelfLayoutType, isChildrenLayoutType]);

  // 应用自身布局
  const applySelfLayout = useCallback((layoutType: string) => {
    if (!selectedObject || !selectedObject.applyLayout) return;

    debugLog('应用自身布局:', layoutType);

    // 确保_margin存在
    if (!selectedObject._margin) {
      selectedObject._margin = { top: 0, left: 0 };
    }

    // 更新对象的布局属性
    selectedObject._layoutType = layoutType;

    try {
      // 直接调用applyLayout方法，传递margin选项
      const options = {
        margin: selectedObject._margin
      };

      // 检查对象是否有父容器
      if (!selectedObject.parent) {
        console.warn('[LayoutPanel] 对象没有父容器，自身布局无效');
        return;
      }

      // 应用布局
      selectedObject.applyLayout(layoutType, options);
      debugLog(`自身布局已应用: ${layoutType}`, options);
    } catch (error) {
      console.error('[LayoutPanel] 应用自身布局时出错:', error);
    }
  }, [selectedObject]);

  // 应用子元素布局
  const applyChildrenLayout = useCallback(() => {
    if (!selectedObject || !selectedObject.applyLayout) return;

    debugLog('应用子元素布局:', childrenLayoutType);

    // 创建布局选项对象
    const layoutOptions = {
      mainAxisAlignment,
      crossAxisAlignment,
      horizontalSpacing,
      verticalSpacing,
      padding,
      columns,
      autoUpdate: enableChildrenLayout // 子集布局启用时自动启用自动更新
    };

    debugLog('布局选项:', layoutOptions);

    // 更新对象的布局属性
    selectedObject._layoutType = childrenLayoutType;
    selectedObject._layoutOptions = layoutOptions;
    selectedObject._enableChildrenLayout = enableChildrenLayout;

    try {
      // 确保容器有有效尺寸
      if (!selectedObject.width || selectedObject.width <= 0) {
        selectedObject.width = 400;
        debugLog('设置默认容器宽度: 400px');
      }

      if (!selectedObject.height || selectedObject.height <= 0) {
        selectedObject.height = 400;
        debugLog('设置默认容器高度: 400px');
      }

      // 确保网格布局的间距值是数字
      if (childrenLayoutType === 'grid') {
        layoutOptions.horizontalSpacing = Number(horizontalSpacing);
        layoutOptions.verticalSpacing = Number(verticalSpacing);
        debugLog('网格布局间距:', layoutOptions.horizontalSpacing, layoutOptions.verticalSpacing);
      }

      // 先设置自动布局更新 - 根据优化后的layout.js插件
      // 注意：applyChildrenLayout只在启用子集布局时调用，所以这里应该始终为true
      if (typeof selectedObject.setAutoLayoutUpdate === 'function') {
        selectedObject.setAutoLayoutUpdate(true);
        debugLog(`设置自动布局更新: true`);
      } else {
        selectedObject._autoUpdateLayout = true;
        debugLog(`直接设置_autoUpdateLayout: true`);
      }

      // 然后应用布局
      selectedObject.applyLayout(childrenLayoutType, layoutOptions);
      debugLog(`子元素布局已应用: ${childrenLayoutType}`);
    } catch (error) {
      console.error('[LayoutPanel] 应用子元素布局时出错:', error);
    }
  }, [selectedObject, childrenLayoutType, mainAxisAlignment, crossAxisAlignment,
    horizontalSpacing, verticalSpacing, padding, columns, enableChildrenLayout]);

  // 处理自身布局类型变化
  const handleSelfLayoutChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newLayoutType = event.target.value;
    setSelfLayoutType(newLayoutType);
    applySelfLayout(newLayoutType);
  }, [applySelfLayout]);

  // 处理子元素布局类型变化 - 自动应用布局
  const handleChildrenLayoutChange = useCallback((value: string) => {
    if (!selectedObject || !selectedObject.applyLayout) return;

    debugLog('更新子元素布局类型:', value);

    // 更新状态
    setChildrenLayoutType(value);

    // 直接更新对象的布局类型
    selectedObject._layoutType = value;

    // 确保布局选项存在
    if (!selectedObject._layoutOptions) {
      selectedObject._layoutOptions = {
        mainAxisAlignment,
        crossAxisAlignment,
        horizontalSpacing,
        verticalSpacing,
        padding,
        columns,
        autoUpdate: enableChildrenLayout
      };
    }

    // 使用requestAnimationFrame确保在状态更新后应用布局
    requestAnimationFrame(() => {
      if (selectedObject && selectedObject.applyLayout && selectedObject._layoutOptions) {
        selectedObject.applyLayout(value, selectedObject._layoutOptions);
        debugLog(`布局类型已更新: ${value}`);
      }
    });
  }, [selectedObject, mainAxisAlignment, crossAxisAlignment, horizontalSpacing,
    verticalSpacing, padding, columns, enableChildrenLayout]);

  // 处理布局选项变化 - 自动应用布局
  const handleLayoutOptionChange = useCallback((optionName: string, value: any) => {
    if (!selectedObject || !selectedObject.applyLayout) return;

    debugLog(`更新布局选项: ${optionName} = ${value}`);

    // 确保布局选项存在
    if (!selectedObject._layoutOptions) {
      selectedObject._layoutOptions = {
        mainAxisAlignment,
        crossAxisAlignment,
        horizontalSpacing,
        verticalSpacing,
        padding,
        columns,
        autoUpdate: enableChildrenLayout
      };
    }

    // 根据选项名称更新状态和对象属性
    switch (optionName) {
      case 'mainAxisAlignment':
        setMainAxisAlignment(value);
        selectedObject._layoutOptions.mainAxisAlignment = value;
        break;
      case 'crossAxisAlignment':
        setCrossAxisAlignment(value);
        selectedObject._layoutOptions.crossAxisAlignment = value;
        break;
      case 'horizontalSpacing':
        setHorizontalSpacing(value);
        selectedObject._layoutOptions.horizontalSpacing = value;
        break;
      case 'verticalSpacing':
        setVerticalSpacing(value);
        selectedObject._layoutOptions.verticalSpacing = value;
        break;
      case 'padding':
        setPadding(value);
        selectedObject._layoutOptions.padding = value;
        break;
      case 'columns':
        setColumns(value);
        selectedObject._layoutOptions.columns = value;
        break;
    }

    // 使用requestAnimationFrame确保在状态更新后应用布局
    requestAnimationFrame(() => {
      if (selectedObject && selectedObject.applyLayout && selectedObject._layoutType && selectedObject._layoutOptions) {
        selectedObject.applyLayout(selectedObject._layoutType, selectedObject._layoutOptions);
        debugLog(`布局已更新: ${selectedObject._layoutType}`);
      }
    });
  }, [selectedObject, mainAxisAlignment, crossAxisAlignment, horizontalSpacing,
    verticalSpacing, padding, columns, enableChildrenLayout]);

  // 处理外边距变化
  const handleMarginChange = (side: 'top' | 'left', value: number) => {
    if (!selectedObject) return;

    // 更新状态
    if (side === 'top') {
      setMarginTop(value);
    } else if (side === 'left') {
      setMarginLeft(value);
    }

    // 确保_margin存在
    selectedObject._margin = selectedObject._margin || { top: 0, left: 0 };
    selectedObject._margin[side] = value;

    console.log(`外边距变化: ${side} = ${value}px`);

    // 只应用当前布局类型
    if (selectedObject._layoutType) {
      // 根据当前布局类型应用相应的布局
      if (selfLayoutTypes.some(layout => layout.value === selectedObject._layoutType)) {
        applySelfLayout(selectedObject._layoutType);
      } else if (childrenLayoutTypes.some(layout => layout.value === selectedObject._layoutType) && enableChildrenLayout) {
        // 只有在启用子集布局时才应用子元素布局
        applyChildrenLayout();
      }
    } else if (selectedObject.parent) {
      // 如果当前没有布局类型但有父容器，强制应用 topLeft 布局
      selectedObject._layoutType = 'topLeft';
      setSelfLayoutType('topLeft');
      applySelfLayout('topLeft');
    }

  };

  // 处理舞台尺寸适配变化
  const handleFitStageWidthChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!selectedObject) return;

    const checked = event.target.checked;
    setFitStageWidth(checked);

    // 如果启用舞台宽度适配，则禁用父级宽度适配
    if (checked) {
      setFitParentWidth(false);
      selectedObject._fitParentWidth = false;
    }

    // 保存设置到对象属性
    selectedObject._fitStageWidth = checked;

    if (checked) {
      // 获取真正舞台的宽度（通过 Graphics 对象）
      const stageWidth = (window as any).Graphics ? (window as any).Graphics.width : 816; // RPG Maker MZ 默认宽度为 816

      // 保存原始宽度（如果尚未保存）
      if (selectedObject._originalWidth === undefined) {
        selectedObject._originalWidth = selectedObject.width;
      }

      // 设置对象宽度为舞台宽度
      selectedObject.width = stageWidth;
      console.log(`适配舞台宽度: ${stageWidth}px (Graphics.width)`);
    } else if (selectedObject._originalWidth !== undefined) {
      // 恢复原始宽度
      selectedObject.width = selectedObject._originalWidth;
      console.log(`恢复原始宽度: ${selectedObject._originalWidth}px`);
    }

    // 如果有布局，重新应用
    if (selectedObject._layoutType) {
      if (selfLayoutTypes.some(layout => layout.value === selectedObject._layoutType)) {
        applySelfLayout(selectedObject._layoutType);
      } else if (childrenLayoutTypes.some(layout => layout.value === selectedObject._layoutType) && enableChildrenLayout) {
        // 只有在启用子集布局时才应用子元素布局
        applyChildrenLayout();
      }
    }
  };

  // 处理舞台高度适配变化
  const handleFitStageHeightChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!selectedObject) return;

    const checked = event.target.checked;
    setFitStageHeight(checked);

    // 如果启用舞台高度适配，则禁用父级高度适配
    if (checked) {
      setFitParentHeight(false);
      selectedObject._fitParentHeight = false;
    }

    // 保存设置到对象属性
    selectedObject._fitStageHeight = checked;

    if (checked) {
      // 获取真正舞台的高度（通过 Graphics 对象）
      const stageHeight = (window as any).Graphics ? (window as any).Graphics.height : 624; // RPG Maker MZ 默认高度为 624

      // 保存原始高度（如果尚未保存）
      if (selectedObject._originalHeight === undefined) {
        selectedObject._originalHeight = selectedObject.height;
      }

      // 设置对象高度为舞台高度
      selectedObject.height = stageHeight;
      console.log(`适配舞台高度: ${stageHeight}px (Graphics.height)`);
    } else if (selectedObject._originalHeight !== undefined) {
      // 恢复原始高度
      selectedObject.height = selectedObject._originalHeight;
      console.log(`恢复原始高度: ${selectedObject._originalHeight}px`);
    }

    // 如果有布局，重新应用
    if (selectedObject._layoutType) {
      if (selfLayoutTypes.some(layout => layout.value === selectedObject._layoutType)) {
        applySelfLayout(selectedObject._layoutType);
      } else if (childrenLayoutTypes.some(layout => layout.value === selectedObject._layoutType) && enableChildrenLayout) {
        // 只有在启用子集布局时才应用子元素布局
        applyChildrenLayout();
      }
    }
  };

  // 处理父级宽度适配变化
  const handleFitParentWidthChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!selectedObject || !selectedObject.parent) return;

    const checked = event.target.checked;
    setFitParentWidth(checked);

    // 如果启用父级宽度适配，则禁用舞台宽度适配
    if (checked) {
      setFitStageWidth(false);
      selectedObject._fitStageWidth = false;
    }

    // 保存设置到对象属性
    selectedObject._fitParentWidth = checked;

    if (checked) {
      // 获取父容器的宽度
      const parentWidth = selectedObject.parent.width;

      // 保存原始宽度（如果尚未保存）
      if (selectedObject._originalWidth === undefined) {
        selectedObject._originalWidth = selectedObject.width;
      }

      // 设置对象宽度为父容器宽度
      selectedObject.width = parentWidth;
      console.log(`适配父级宽度: ${parentWidth}px`);
    } else if (selectedObject._originalWidth !== undefined) {
      // 恢复原始宽度
      selectedObject.width = selectedObject._originalWidth;
      console.log(`恢复原始宽度: ${selectedObject._originalWidth}px`);
    }

    // 如果有布局，重新应用
    if (selectedObject._layoutType) {
      if (selfLayoutTypes.some(layout => layout.value === selectedObject._layoutType)) {
        applySelfLayout(selectedObject._layoutType);
      } else if (childrenLayoutTypes.some(layout => layout.value === selectedObject._layoutType) && enableChildrenLayout) {
        // 只有在启用子集布局时才应用子元素布局
        applyChildrenLayout();
      }
    }
  };

  // 处理父级高度适配变化
  const handleFitParentHeightChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!selectedObject || !selectedObject.parent) return;

    const checked = event.target.checked;
    setFitParentHeight(checked);

    // 如果启用父级高度适配，则禁用舞台高度适配
    if (checked) {
      setFitStageHeight(false);
      selectedObject._fitStageHeight = false;
    }

    // 保存设置到对象属性
    selectedObject._fitParentHeight = checked;

    if (checked) {
      // 获取父容器的高度
      const parentHeight = selectedObject.parent.height;

      // 保存原始高度（如果尚未保存）
      if (selectedObject._originalHeight === undefined) {
        selectedObject._originalHeight = selectedObject.height;
      }

      // 设置对象高度为父容器高度
      selectedObject.height = parentHeight;
      console.log(`适配父级高度: ${parentHeight}px`);
    } else if (selectedObject._originalHeight !== undefined) {
      // 恢复原始高度
      selectedObject.height = selectedObject._originalHeight;
      console.log(`恢复原始高度: ${selectedObject._originalHeight}px`);
    }

    // 如果有布局，重新应用
    if (selectedObject._layoutType) {
      if (selfLayoutTypes.some(layout => layout.value === selectedObject._layoutType)) {
        applySelfLayout(selectedObject._layoutType);
      } else if (childrenLayoutTypes.some(layout => layout.value === selectedObject._layoutType) && enableChildrenLayout) {
        // 只有在启用子集布局时才应用子元素布局
        applyChildrenLayout();
      }
    }
  };

  // 处理子集布局启用状态变化 - 核心功能：子组件布局必须手动开启，开启时自动启用自动布局
  const handleEnableChildrenLayoutChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    if (!selectedObject) return;

    const checked = event.target.checked;
    setEnableChildrenLayout(checked);

    debugLog(`${checked ? '启用' : '禁用'}子集布局`);

    // 保存子集布局启用状态
    selectedObject._enableChildrenLayout = checked;

    // 关键逻辑：开启子组件布局时自动启用自动布局更新
    if (checked) {
      // 确保布局选项存在并设置autoUpdate为true
      if (!selectedObject._layoutOptions) {
        selectedObject._layoutOptions = {
          mainAxisAlignment,
          crossAxisAlignment,
          horizontalSpacing,
          verticalSpacing,
          padding,
          columns,
          autoUpdate: true // 强制启用自动更新
        };
      } else {
        selectedObject._layoutOptions.autoUpdate = true; // 强制启用自动更新
      }

      // 设置自动布局更新 - 根据优化后的layout.js插件
      if (typeof selectedObject.setAutoLayoutUpdate === 'function') {
        selectedObject.setAutoLayoutUpdate(true); // 强制启用自动更新
        debugLog('通过setAutoLayoutUpdate方法启用自动布局更新');
      } else {
        selectedObject._autoUpdateLayout = true; // 强制启用自动更新
        debugLog('直接设置_autoUpdateLayout属性启用自动布局更新');
      }

      // 如果有子元素布局类型，立即应用布局
      if (isChildrenLayoutType(childrenLayoutType)) {
        applyChildrenLayout();
        debugLog(`子集布局已启用并应用: ${childrenLayoutType}`);
      }
    } else {
      // 禁用子集布局时，也禁用自动更新
      if (typeof selectedObject.setAutoLayoutUpdate === 'function') {
        selectedObject.setAutoLayoutUpdate(false);
        debugLog('通过setAutoLayoutUpdate方法禁用自动布局更新');
      } else {
        selectedObject._autoUpdateLayout = false;
        debugLog('直接设置_autoUpdateLayout属性禁用自动布局更新');
      }

      // 更新布局选项中的autoUpdate
      if (selectedObject._layoutOptions) {
        selectedObject._layoutOptions.autoUpdate = false;
      }

      debugLog('子集布局已禁用');
    }
  }, [selectedObject, mainAxisAlignment, crossAxisAlignment, horizontalSpacing,
    verticalSpacing, padding, columns, childrenLayoutType, isChildrenLayoutType, applyChildrenLayout]);

  // 如果没有选中对象，显示提示信息
  if (!selectedObject) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography variant="body2" color="text.secondary">
          未选中任何对象
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{ height: "100%", display: "flex", flexDirection: "column", p: 2 }}
      className="layout-panel-container"
    >
      <Paper
        elevation={0}
        sx={{ p: 2, mb: 2 }}
        className="layout-panel-content"
      >
        {selectedObject && (
          <Box>
            {/* 尺寸适配模块 */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                尺寸适配
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                舞台和父级尺寸适配只能选择其中一个
              </Typography>

              {/* 舞台尺寸适配 */}
              <Box sx={{ mb: 1, border: '1px solid #e0e0e0', p: 1, borderRadius: 1 }}>
                <Typography variant="caption" sx={{ display: 'block', fontWeight: 'bold', mb: 1 }}>
                  舞台尺寸适配
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'row', gap: 2 }}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={fitStageWidth}
                        onChange={handleFitStageWidthChange}
                        size="small"
                        disabled={!selectedObject}
                      />
                    }
                    label={
                      <Typography variant="body2">
                        适配舞台宽度
                      </Typography>
                    }
                  />

                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={fitStageHeight}
                        onChange={handleFitStageHeightChange}
                        size="small"
                        disabled={!selectedObject}
                      />
                    }
                    label={
                      <Typography variant="body2">
                        适配舞台高度
                      </Typography>
                    }
                  />
                </Box>
              </Box>

              {/* 父级尺寸适配 - 只有当对象有父容器时才显示 */}
              {hasParent && (
                <Box sx={{ mb: 1, border: '1px solid #e0e0e0', p: 1, borderRadius: 1 }}>
                  <Typography variant="caption" sx={{ display: 'block', fontWeight: 'bold', mb: 1 }}>
                    父级尺寸适配
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'row', gap: 2 }}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={fitParentWidth}
                          onChange={handleFitParentWidthChange}
                          size="small"
                          disabled={!selectedObject || !selectedObject.parent}
                        />
                      }
                      label={
                        <Typography variant="body2">
                          适配父级宽度
                        </Typography>
                      }
                    />

                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={fitParentHeight}
                          onChange={handleFitParentHeightChange}
                          size="small"
                          disabled={!selectedObject || !selectedObject.parent}
                        />
                      }
                      label={
                        <Typography variant="body2">
                          适配父级高度
                        </Typography>
                      }
                    />
                  </Box>
                </Box>
              )}
            </Box>
            {/* 自身布局模块 */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                自身布局
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                设置对象相对于父容器的位置
              </Typography>

              {!hasParent && (
                <Box sx={{ p: 1, mb: 2, bgcolor: 'error.light', borderRadius: 1 }}>
                  <Typography variant="caption" color="error.dark" fontWeight="bold">
                    警告：此对象没有父容器，自身布局无效。
                  </Typography>
                  <Typography variant="caption" color="error.dark" display="block">
                    请先将此对象添加到一个容器中，然后再应用自身布局。
                  </Typography>
                </Box>
              )}

              <FormControl component="fieldset" sx={{ width: '100%' }}>
                <RadioGroup
                  value={selfLayoutType}
                  onChange={handleSelfLayoutChange}
                >
                  <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 1 }}>
                    {selfLayoutTypes.map((layout) => (
                      <Tooltip key={layout.value} title={layout.label}>
                        <FormControlLabel
                          value={layout.value}
                          control={<Radio size="small" />}
                          label={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              {layout.icon}
                            </Box>
                          }
                          sx={{
                            m: 0,
                            '& .MuiFormControlLabel-label': {
                              fontSize: '0.75rem',
                              display: 'flex',
                              alignItems: 'center'
                            }
                          }}
                        />
                      </Tooltip>
                    ))}
                  </Box>
                </RadioGroup>
              </FormControl>

              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 2, mb: 1 }}>
                外边距
              </Typography>

              <SliderInput
                label="上边距"
                value={marginTop}
                min={0}
                max={(window as any).Graphics ? (window as any).Graphics.height : 624}
                step={1}
                onChange={(value) => handleMarginChange('top', value)}
                object={selectedObject}
                propertyName="_margin.top"
                unit="px"
                valueLabelDisplay="on"
                compact
              />

              <SliderInput
                label="左边距"
                value={marginLeft}
                min={0}
                max={(window as any).Graphics ? (window as any).Graphics.width : 816}
                step={1}
                onChange={(value) => handleMarginChange('left', value)}
                object={selectedObject}
                propertyName="_margin.left"
                unit="px"
                valueLabelDisplay="on"
                compact
              />
            </Box>

            <Divider sx={{ my: 2 }} />

            {/* 子元素布局模块 - 只有当对象有子元素时才显示 */}
            {hasChildren && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  子元素布局
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                  设置子元素在容器内的排列方式
                </Typography>

                {/* 子集布局启用开关 - 必须手动开启 */}
                <Box sx={{ mb: 2, p: 1.5, border: '2px solid #e3f2fd', borderRadius: 1, bgcolor: '#f8f9fa' }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={enableChildrenLayout}
                        onChange={handleEnableChildrenLayoutChange}
                        name="enableChildrenLayout"
                        color="primary"
                        size="medium"
                      />
                    }
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="body2" sx={{ mr: 0.5, fontWeight: 'bold' }}>
                          启用子集布局
                        </Typography>
                        <Tooltip title="子组件布局必须手动开启。开启后将自动启用自动布局更新功能，子元素会按照布局规则排列，并在添加或删除子元素时自动重新布局。">
                          <HelpOutlineIcon fontSize="small" color="primary" />
                        </Tooltip>
                      </Box>
                    }
                    sx={{ mb: 1 }}
                  />

                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                    ⚠️ 重要：子组件布局功能默认关闭，需要手动开启
                  </Typography>
                  <Typography variant="caption" color="primary.main" sx={{ display: 'block', mt: 0.5 }}>
                    ✓ 开启时会自动启用自动布局更新功能
                  </Typography>
                </Box>



                {/* 只有在启用子集布局时才显示布局设置 */}
                {enableChildrenLayout && (

                  <Box sx={{ mt: 2 }}>
                    <SelectInput
                      label="布局类型"
                      value={childrenLayoutType}
                      options={childrenLayoutTypes}
                      onChange={handleChildrenLayoutChange}
                      object={selectedObject}
                      propertyName="_layoutType"
                    />
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                      布局设置
                    </Typography>

                    <SelectInput
                      label="主轴对齐"
                      value={mainAxisAlignment}
                      options={mainAxisAlignments}
                      onChange={(value) => handleLayoutOptionChange('mainAxisAlignment', value)}
                      object={selectedObject}
                      propertyName="_layoutOptions.mainAxisAlignment"
                      compact
                    />

                    <SelectInput
                      label="交叉轴对齐"
                      value={crossAxisAlignment}
                      options={crossAxisAlignments}
                      onChange={(value) => handleLayoutOptionChange('crossAxisAlignment', value)}
                      object={selectedObject}
                      propertyName="_layoutOptions.crossAxisAlignment"
                      compact
                    />

                    {childrenLayoutType === 'grid' && (
                      <Box sx={{ mt: 1, border: '1px solid #e0e0e0', p: 1, borderRadius: 1, mb: 1 }}>
                        <Typography variant="caption" sx={{ display: 'block', fontWeight: 'bold', mb: 1 }}>
                          网格布局设置
                        </Typography>
                        <SliderInput
                          label="列数"
                          value={columns}
                          onChange={(value) => {
                            const numValue = Math.max(1, Math.round(Number(value)));
                            setColumns(numValue);
                            if (selectedObject) {
                              selectedObject._layoutOptions = selectedObject._layoutOptions || {};
                              selectedObject._layoutOptions.columns = numValue;
                              applyChildrenLayout();
                            }
                          }}
                          min={1}
                          max={10}
                          step={1}
                          object={selectedObject}
                          propertyName="_layoutOptions.columns"
                          marks
                          compact
                        />
                      </Box>
                    )}

                    {/* 根据布局类型显示不同的间距控制 */}
                    {childrenLayoutType === 'column' && (
                      <>
                        <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
                          垂直布局间距
                        </Typography>
                        <SliderInput
                          label="上下间距"
                          value={verticalSpacing}
                          onChange={(value) => handleLayoutOptionChange('verticalSpacing', value)}
                          min={-100}
                          max={100}
                          step={1}
                          object={selectedObject}
                          propertyName="_layoutOptions.verticalSpacing"
                          unit="px"
                          compact
                        />
                      </>
                    )}

                    {childrenLayoutType === 'row' && (
                      <>
                        <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
                          水平布局间距
                        </Typography>
                        <SliderInput
                          label="左右间距"
                          value={horizontalSpacing}
                          onChange={(value) => handleLayoutOptionChange('horizontalSpacing', value)}
                          min={-100}
                          max={100}
                          step={1}
                          object={selectedObject}
                          propertyName="_layoutOptions.horizontalSpacing"
                          unit="px"
                          compact
                        />
                      </>
                    )}

                    {childrenLayoutType === 'grid' && (
                      <>
                        <Typography variant="caption" sx={{ mt: 1, display: 'block', fontWeight: 'bold' }}>
                          网格布局间距
                        </Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mt: 1, border: '1px solid #e0e0e0', p: 1, borderRadius: 1 }}>
                          <Box sx={{ mb: 1 }}>
                            <Typography variant="caption" sx={{ display: 'block', color: 'text.secondary' }}>
                              列间距 (水平方向)
                            </Typography>
                            <SliderInput
                              label="列间距"
                              value={horizontalSpacing}
                              onChange={(value) => {
                                const numValue = Number(value);
                                setHorizontalSpacing(numValue);
                                if (selectedObject) {
                                  selectedObject._layoutOptions = selectedObject._layoutOptions || {};
                                  selectedObject._layoutOptions.horizontalSpacing = numValue;
                                  applyChildrenLayout();
                                }
                              }}
                              min={0}
                              max={50}
                              step={1}
                              object={selectedObject}
                              propertyName="_layoutOptions.horizontalSpacing"
                              unit="px"
                              compact
                            />
                          </Box>

                          <Box>
                            <Typography variant="caption" sx={{ display: 'block', color: 'text.secondary' }}>
                              行间距 (垂直方向)
                            </Typography>
                            <SliderInput
                              label="行间距"
                              value={verticalSpacing}
                              onChange={(value) => {
                                const numValue = Number(value);
                                setVerticalSpacing(numValue);
                                if (selectedObject) {
                                  selectedObject._layoutOptions = selectedObject._layoutOptions || {};
                                  selectedObject._layoutOptions.verticalSpacing = numValue;
                                  applyChildrenLayout();
                                }
                              }}
                              min={0}
                              max={50}
                              step={1}
                              object={selectedObject}
                              propertyName="_layoutOptions.verticalSpacing"
                              unit="px"
                              compact
                            />
                          </Box>
                        </Box>
                      </>
                    )}

                    <SliderInput
                      label="内边距"
                      value={padding}
                      onChange={(value) => handleLayoutOptionChange('padding', value)}
                      min={0}
                      max={50}
                      step={1}
                      object={selectedObject}
                      propertyName="_layoutOptions.padding"
                      unit="px"
                      compact
                    />
                  </Box>
                )}
              </Box>
            )}

          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default LayoutPanel;
