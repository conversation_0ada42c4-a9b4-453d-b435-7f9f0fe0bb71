// 测试新的elements数组结构
console.log("测试bitmap elements数组结构");

// 模拟一个bitmap对象
const testBitmap = {
    elements: [
        {
            type: 'image',
            bounds: { x: 5, y: 3, width: 144, height: 129 },
            dh: 129,
            dw: 144,
            dx: 5,
            dy: 3,
            sh: 129,
            source: { _url: '../projects/Project4/img/faces/Actor1.png' },
            sw: 144,
            sx: 0,
            sy: 7
        },
        {
            type: 'text',
            align: 'left',
            bounds: { x: 184, y: 13, width: 168, height: 36 },
            lineHeight: 36,
            maxWidth: 168,
            text: '里德',
            x: 184,
            y: 13
        }
    ],
    
    // 模拟redrawing方法
    redrawing: function() {
        console.log('调用bitmap.redrawing()方法');
        console.log('重绘所有elements:', this.elements.length);
        
        // 先绘制图像元素
        const imageElements = this.elements.filter(e => e.type === 'image');
        console.log('图像元素数量:', imageElements.length);
        
        // 再绘制文本元素
        const textElements = this.elements.filter(e => e.type === 'text');
        console.log('文本元素数量:', textElements.length);
        
        textElements.forEach((textItem, index) => {
            console.log(`绘制文本 ${index + 1}:`, textItem.text);
        });
    },
    
    // 向后兼容的getter
    get texts() {
        return this.elements.filter(e => e.type === 'text');
    },
    
    get imgs() {
        return this.elements.filter(e => e.type === 'image');
    }
};

// 测试新的数据结构
console.log('=== 测试elements数组 ===');
console.log('总元素数量:', testBitmap.elements.length);
console.log('文本元素:', testBitmap.texts);
console.log('图像元素:', testBitmap.imgs);

// 测试修改文本元素
console.log('\n=== 测试修改文本元素 ===');
const textElement = testBitmap.elements.find(e => e.type === 'text' && e.text === '里德');
if (textElement) {
    console.log('找到文本元素:', textElement.text);
    textElement.text = '新的文本内容';
    console.log('修改后的文本:', textElement.text);
    
    // 调用重绘方法
    testBitmap.redrawing();
}

// 测试生成的代码结构
console.log('\n=== 测试生成的代码结构 ===');
const generatedCode = `
// 检查是否有 elements 数组
if (targetObject._bitmap.elements && targetObject._bitmap.elements.length > 0) {
    // 查找要修改的文本元素
    const textToModify = targetObject._bitmap.elements.find(e => e.type === 'text' && e.text === targetObject._bitmap.text);
    // 如果找到了文本元素，更新其属性
    if (textToModify) {
        // 更新文本元素的 text 属性
        textToModify.text = "新文本";
    }
    // 调用统一的重绘方法
    targetObject._bitmap.redrawing();
}
`;

console.log('生成的代码结构:');
console.log(generatedCode);

console.log('\n=== 测试完成 ===');
