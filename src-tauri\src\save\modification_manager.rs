use crate::save::plugin_parser::parse_existing_modifications;
use crate::save::types::{Modification, ModificationType};
use crate::utils::{cache, logging, path, project_state, validation};
use std::collections::HashMap;
use std::fs;
use tauri::AppHandle;

/// 记录单个属性修改
#[tauri::command]
pub fn record_property_modification(
    app_handle: AppHandle,
    object_path: Vec<String>,
    class_name: String,
    field_name: String,
    value: serde_json::Value,
) -> Result<String, String> {
    logging::log_function_call!(
        "ModificationManager",
        "record_property_modification",
        &object_path,
        &class_name,
        &field_name,
        &value
    );

    // 验证输入参数
    validation::validate_scene_path(&object_path)?;

    // 获取当前修改列表
    let mut modifications = get_current_modifications(&app_handle)?;

    // 查找是否已存在相同路径的修改
    let mut found = false;
    for modification in &mut modifications {
        if modification.scene_path == object_path {
            // 更新现有修改的属性
            modification
                .properties
                .insert(field_name.clone(), value.clone());
            found = true;
            break;
        }
    }

    // 如果没有找到相同路径的修改，创建新的修改
    if !found {
        let mut properties = HashMap::new();
        properties.insert(field_name.clone(), value.clone());

        modifications.push(Modification {
            class_name,
            scene_path: object_path,
            properties,
            modification_type: ModificationType::ObjectInstance,
        });
    }

    // 更新缓存
    cache::update_cached_modifications(modifications)?;

    Ok(format!("属性 {} 已记录", field_name))
}

/// 记录类型原型修改
#[tauri::command]
pub fn record_class_prototype_modification(
    app_handle: AppHandle,
    class_name: String,
    field_name: String,
    value: serde_json::Value,
) -> Result<String, String> {
    logging::log_function_call!(
        "ModificationManager",
        "record_class_prototype_modification",
        &class_name,
        &field_name,
        &value
    );

    // 验证类名
    if class_name.is_empty() {
        return Err("类名不能为空".to_string());
    }

    // 获取当前修改列表
    let mut modifications = get_current_modifications(&app_handle)?;

    // 查找是否已存在相同类型的原型修改
    let mut found = false;
    for modification in &mut modifications {
        if modification.class_name == class_name
            && modification.modification_type == ModificationType::ClassPrototype
        {
            // 更新现有修改的属性
            modification
                .properties
                .insert(field_name.clone(), value.clone());
            found = true;
            break;
        }
    }

    // 如果没有找到相同类型的修改，创建新的修改
    if !found {
        let mut properties = HashMap::new();
        properties.insert(field_name.clone(), value.clone());

        modifications.push(Modification {
            class_name: class_name.clone(),
            scene_path: vec![], // 类型修改不需要场景路径
            properties,
            modification_type: ModificationType::ClassPrototype,
        });
    }

    // 更新缓存
    cache::update_cached_modifications(modifications)?;

    Ok(format!("类型 {} 的属性 {} 已记录", class_name, field_name))
}

/// 获取当前修改列表
pub fn get_current_modifications(app_handle: &AppHandle) -> Result<Vec<Modification>, String> {
    // 首先检查缓存
    if let Some(cached) = cache::get_cached_modifications() {
        return Ok(cached);
    }

    logging::log_debug("ModificationManager", "缓存为空，从文件读取修改列表");

    // 如果缓存为空，尝试从文件中读取
    let project_name = match project_state::get_current_project_name(app_handle) {
        Ok(name) => name,
        Err(_) => {
            logging::log_debug("ModificationManager", "项目名称为空，返回空修改列表");
            return Ok(Vec::new());
        }
    };

    // 构建插件文件路径
    let plugins_dir = path::get_project_plugins_dir(&project_name)?;
    let plugin_file_path = plugins_dir.join("RPGEditor_PrototypeModifications.js");

    // 如果文件存在，解析现有修改
    if plugin_file_path.exists() {
        let content = fs::read_to_string(&plugin_file_path)
            .map_err(|e| format!("Failed to read plugin file: {}", e))?;
        let modifications = parse_existing_modifications(&content);
        logging::log_info(
            "ModificationManager",
            &format!("从文件读取到 {} 个修改", modifications.len()),
        );
        return Ok(modifications);
    }

    // 如果文件不存在，返回空列表
    logging::log_debug("ModificationManager", "插件文件不存在，返回空修改列表");
    Ok(Vec::new())
}

/// 合并修改
pub fn merge_modifications(
    existing: Vec<Modification>,
    new_mods: Vec<Modification>,
) -> Vec<Modification> {
    println!(
        "合并修改，现有: {}，新增: {}",
        existing.len(),
        new_mods.len()
    );

    // 打印现有修改的详细信息
    for (i, existing_mod) in existing.iter().enumerate() {
        println!(
            "  现有修改 {}: 路径={:?}, 类名={}, 属性数量={}",
            i,
            existing_mod.scene_path,
            existing_mod.class_name,
            existing_mod.properties.len()
        );
        for (key, value) in &existing_mod.properties {
            println!("    现有属性: {} = {}", key, value);
        }
    }

    // 打印新修改的详细信息
    for (i, new_mod) in new_mods.iter().enumerate() {
        println!(
            "  新修改 {}: 路径={:?}, 类名={}, 属性数量={}",
            i,
            new_mod.scene_path,
            new_mod.class_name,
            new_mod.properties.len()
        );
        for (key, value) in &new_mod.properties {
            println!("    新属性: {} = {}", key, value);
        }
    }

    let mut result = existing;

    // 处理每个新修改
    for new_mod in new_mods {
        println!(
            "处理新修改: 路径={:?}, 属性数量={}",
            new_mod.scene_path,
            new_mod.properties.len()
        );

        // 查找是否存在相同路径的修改
        let mut found = false;
        for existing_mod in &mut result {
            if existing_mod.scene_path == new_mod.scene_path {
                println!("  找到相同路径的现有修改，合并属性");
                println!("  合并前现有属性数量: {}", existing_mod.properties.len());

                // 合并属性
                for (key, value) in new_mod.properties.iter() {
                    println!("    添加属性: {} = {}", key, value);
                    existing_mod.properties.insert(key.clone(), value.clone());
                }

                println!("  合并后现有属性数量: {}", existing_mod.properties.len());
                found = true;
                break;
            }
        }

        // 如果没有找到相同路径的修改，添加新修改
        if !found {
            println!("  没有找到相同路径的修改，添加新修改");
            result.push(new_mod);
        }
    }

    println!("合并后的修改数量: {}", result.len());

    // 打印合并后的结果
    for (i, result_mod) in result.iter().enumerate() {
        println!(
            "  合并结果 {}: 路径={:?}, 类名={}, 属性数量={}",
            i,
            result_mod.scene_path,
            result_mod.class_name,
            result_mod.properties.len()
        );
        for (key, value) in &result_mod.properties {
            println!("    结果属性: {} = {}", key, value);
        }
    }

    result
}

// ==================== 滤镜相关方法 ====================

/// 记录滤镜参数修改
#[tauri::command]
pub fn record_filter_param_modification(
    app_handle: AppHandle,
    object_path: Vec<String>,
    class_name: String,
    filter_index: usize,
    param_name: String,
    value: serde_json::Value,
) -> Result<String, String> {
    println!("接收到 record_filter_param_modification 调用");
    println!("对象路径: {:?}", object_path);
    println!("类名: {}", class_name);
    println!("滤镜索引: {}", filter_index);
    println!("参数名: {}", param_name);
    println!("值: {}", value);

    // 检查对象路径是否有效
    if object_path.is_empty() {
        return Err("对象路径为空，无法记录滤镜参数修改".to_string());
    }

    // 检查场景名称是否有效（以 Scene_ 开头）
    let scene_name = &object_path[0];
    if !scene_name.starts_with("Scene_") {
        return Err(format!(
            "无效的场景名称: {}，无法记录滤镜参数修改",
            scene_name
        ));
    }

    // 获取当前修改列表
    let mut modifications = get_current_modifications(&app_handle)?;

    // 构建滤镜参数的属性名
    let filter_property_name = format!("filters[{}].{}", filter_index, param_name);

    // 查找是否已存在相同路径的修改
    let mut found = false;
    for modification in &mut modifications {
        if modification.scene_path == object_path {
            // 更新现有修改的滤镜参数
            modification
                .properties
                .insert(filter_property_name.clone(), value.clone());
            found = true;
            break;
        }
    }

    // 如果没有找到相同路径的修改，创建新的修改
    if !found {
        let mut properties = HashMap::new();
        properties.insert(filter_property_name.clone(), value.clone());

        modifications.push(Modification {
            class_name,
            scene_path: object_path,
            properties,
            modification_type: ModificationType::ObjectInstance,
        });
    }

    // 更新缓存
    cache::update_cached_modifications(modifications)?;

    Ok(format!("滤镜参数 {} 已记录", filter_property_name))
}

/// 记录添加滤镜操作
#[tauri::command]
pub fn record_add_filter(
    app_handle: AppHandle,
    object_path: Vec<String>,
    class_name: String,
    filter_type: String,
    filter_params: serde_json::Value,
    insert_index: Option<usize>,
) -> Result<String, String> {
    println!("接收到 record_add_filter 调用");
    println!("对象路径: {:?}", object_path);
    println!("类名: {}", class_name);
    println!("滤镜类型: {}", filter_type);
    println!("滤镜参数: {}", filter_params);
    println!("插入索引: {:?}", insert_index);

    // 检查对象路径是否有效
    if object_path.is_empty() {
        return Err("对象路径为空，无法记录添加滤镜操作".to_string());
    }

    // 检查场景名称是否有效（以 Scene_ 开头）
    let scene_name = &object_path[0];
    if !scene_name.starts_with("Scene_") {
        return Err(format!(
            "无效的场景名称: {}，无法记录添加滤镜操作",
            scene_name
        ));
    }

    // 获取当前修改列表
    let mut modifications = get_current_modifications(&app_handle)?;

    // 构建添加滤镜的操作信息
    let add_filter_operation = serde_json::json!({
        "operation": "add",
        "filterType": filter_type,
        "params": filter_params,
        "insertIndex": insert_index
    });

    // 构建滤镜操作的属性名
    let filter_operation_name = format!(
        "filters_operation_{}",
        chrono::Utc::now().timestamp_millis()
    );

    // 查找是否已存在相同路径的修改
    let mut found = false;
    for modification in &mut modifications {
        if modification.scene_path == object_path {
            // 添加滤镜操作到现有修改
            modification
                .properties
                .insert(filter_operation_name.clone(), add_filter_operation.clone());
            found = true;
            break;
        }
    }

    // 如果没有找到相同路径的修改，创建新的修改
    if !found {
        let mut properties = HashMap::new();
        properties.insert(filter_operation_name.clone(), add_filter_operation.clone());

        modifications.push(Modification {
            class_name,
            scene_path: object_path,
            properties,
            modification_type: ModificationType::ObjectInstance,
        });
    }

    // 更新缓存
    cache::update_cached_modifications(modifications)?;

    Ok(format!("添加滤镜操作 {} 已记录", filter_type))
}

/// 记录删除滤镜操作
#[tauri::command]
pub fn record_remove_filter(
    app_handle: AppHandle,
    object_path: Vec<String>,
    class_name: String,
    filter_index: usize,
) -> Result<String, String> {
    println!("接收到 record_remove_filter 调用");
    println!("对象路径: {:?}", object_path);
    println!("类名: {}", class_name);
    println!("滤镜索引: {}", filter_index);

    // 检查对象路径是否有效
    if object_path.is_empty() {
        return Err("对象路径为空，无法记录删除滤镜操作".to_string());
    }

    // 检查场景名称是否有效（以 Scene_ 开头）
    let scene_name = &object_path[0];
    if !scene_name.starts_with("Scene_") {
        return Err(format!(
            "无效的场景名称: {}，无法记录删除滤镜操作",
            scene_name
        ));
    }

    // 获取当前修改列表
    let mut modifications = get_current_modifications(&app_handle)?;

    // 构建删除滤镜的操作信息
    let remove_filter_operation = serde_json::json!({
        "operation": "remove",
        "filterIndex": filter_index
    });

    // 构建滤镜操作的属性名
    let filter_operation_name = format!(
        "filters_operation_{}",
        chrono::Utc::now().timestamp_millis()
    );

    // 查找是否已存在相同路径的修改
    let mut found = false;
    for modification in &mut modifications {
        if modification.scene_path == object_path {
            // 添加删除滤镜操作到现有修改
            modification.properties.insert(
                filter_operation_name.clone(),
                remove_filter_operation.clone(),
            );
            found = true;
            break;
        }
    }

    // 如果没有找到相同路径的修改，创建新的修改
    if !found {
        let mut properties = HashMap::new();
        properties.insert(
            filter_operation_name.clone(),
            remove_filter_operation.clone(),
        );

        modifications.push(Modification {
            class_name,
            scene_path: object_path,
            properties,
            modification_type: ModificationType::ObjectInstance,
        });
    }

    // 更新缓存
    cache::update_cached_modifications(modifications)?;

    Ok(format!("删除滤镜操作 (索引: {}) 已记录", filter_index))
}

/// 记录滤镜重新排序操作
#[tauri::command]
pub fn record_reorder_filters(
    app_handle: AppHandle,
    object_path: Vec<String>,
    class_name: String,
    new_order: Vec<usize>,
) -> Result<String, String> {
    println!("接收到 record_reorder_filters 调用");
    println!("对象路径: {:?}", object_path);
    println!("类名: {}", class_name);
    println!("新顺序: {:?}", new_order);

    // 检查对象路径是否有效
    if object_path.is_empty() {
        return Err("对象路径为空，无法记录滤镜重排操作".to_string());
    }

    // 检查场景名称是否有效（以 Scene_ 开头）
    let scene_name = &object_path[0];
    if !scene_name.starts_with("Scene_") {
        return Err(format!(
            "无效的场景名称: {}，无法记录滤镜重排操作",
            scene_name
        ));
    }

    // 获取当前修改列表
    let mut modifications = get_current_modifications(&app_handle)?;

    // 构建重排滤镜的操作信息
    let reorder_filter_operation = serde_json::json!({
        "operation": "reorder",
        "newOrder": new_order
    });

    // 构建滤镜操作的属性名
    let filter_operation_name = format!(
        "filters_operation_{}",
        chrono::Utc::now().timestamp_millis()
    );

    // 查找是否已存在相同路径的修改
    let mut found = false;
    for modification in &mut modifications {
        if modification.scene_path == object_path {
            // 添加重排滤镜操作到现有修改
            modification.properties.insert(
                filter_operation_name.clone(),
                reorder_filter_operation.clone(),
            );
            found = true;
            break;
        }
    }

    // 如果没有找到相同路径的修改，创建新的修改
    if !found {
        let mut properties = HashMap::new();
        properties.insert(
            filter_operation_name.clone(),
            reorder_filter_operation.clone(),
        );

        modifications.push(Modification {
            class_name,
            scene_path: object_path,
            properties,
            modification_type: ModificationType::ObjectInstance,
        });
    }

    // 更新缓存
    cache::update_cached_modifications(modifications)?;

    Ok("滤镜重排操作已记录".to_string())
}

/// 统一的修改记录接口（支持类型操作和对象操作）
#[tauri::command]
pub fn record_unified_modification(
    app_handle: AppHandle,
    operation_info: serde_json::Value,
) -> Result<String, String> {
    logging::log_function_call!(
        "ModificationManager",
        "record_unified_modification",
        &operation_info
    );

    // 解析操作模式
    let operation_mode = operation_info
        .get("operationMode")
        .and_then(|v| v.as_str())
        .unwrap_or("OBJECT");

    let property_name = operation_info
        .get("propertyName")
        .and_then(|v| v.as_str())
        .ok_or("缺少属性名称")?;

    let value = operation_info.get("value").ok_or("缺少属性值")?.clone();

    let class_name = operation_info
        .get("className")
        .and_then(|v| v.as_str())
        .unwrap_or("Unknown");

    logging::log_debug(
        "ModificationManager",
        &format!(
            "操作模式: {}, 属性: {}, 类名: {}",
            operation_mode, property_name, class_name
        ),
    );

    match operation_mode {
        "TYPE" => {
            // 类型操作 - 使用类型修改API
            let parent_class_name = operation_info
                .get("_operationContext")
                .and_then(|ctx| ctx.get("parentClassName"))
                .and_then(|v| v.as_str())
                .ok_or("类型操作缺少父类名称")?;

            logging::log_info(
                "ModificationManager",
                &format!(
                    "处理类型操作: {} 类型的 {} 属性",
                    parent_class_name, property_name
                ),
            );

            record_class_prototype_modification(
                app_handle,
                parent_class_name.to_string(),
                property_name.to_string(),
                value,
            )
        }
        "OBJECT" => {
            // 对象操作 - 使用对象修改API
            let target_path = operation_info
                .get("targetPath")
                .and_then(|v| v.as_array())
                .ok_or("对象操作缺少目标路径")?;

            let path_strings: Vec<String> = target_path
                .iter()
                .filter_map(|v| v.as_str())
                .map(|s| s.to_string())
                .collect();

            logging::log_info(
                "ModificationManager",
                &format!(
                    "处理对象操作: 路径 {:?} 的 {} 属性",
                    path_strings, property_name
                ),
            );

            record_property_modification(
                app_handle,
                path_strings,
                class_name.to_string(),
                property_name.to_string(),
                value,
            )
        }
        _ => {
            let error_msg = format!("未知的操作模式: {}", operation_mode);
            logging::log_error("ModificationManager", &error_msg);
            Err(error_msg)
        }
    }
}
