import React, { useState, memo } from "react";
import {
  Box, Typography, IconButton, Paper, Collapse, Divider
} from "@mui/material";
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import DeleteIcon from '@mui/icons-material/Delete';
import NumberInput from "../../../ui/NumberInput";
import ColorPicker from "../../../ui/ColorPicker";
import SelectInput from "../../../ui/SelectInput";
import SliderInput from "../../../ui/SliderInput";
import { Switch } from "@mui/material";

// 滤镜参数定义
interface FilterParam {
  name: string;
  type: 'number' | 'color' | 'boolean' | 'select';
  min?: number;
  max?: number;
  step?: number;
  options?: { value: string; label: string }[];
  defaultValue: any;
}

// 当前应用的滤镜定义
interface AppliedFilter {
  id: string;
  type: string;
  params: Record<string, any>;
  instance?: any;
}

interface FilterItemProps {
  filter: AppliedFilter;
  filterOption: {
    id: string;
    name: string;
    icon: React.ReactNode;
    filterClass: string;
    defaultParams: Record<string, any>;
  };
  paramDefs: FilterParam[];
  isSelected: boolean;
  onSelect: () => void;
  onDelete: () => void;
  onUpdateParam: (paramName: string, value: any) => void;
}

const FilterItem: React.FC<FilterItemProps> = memo(({
  filter,
  filterOption,
  paramDefs,
  isSelected,
  onSelect,
  onDelete,
  onUpdateParam
}) => {
  const [expanded, setExpanded] = useState(false);

  // 渲染参数控制器
  const renderParamControl = (param: FilterParam, value: any) => {
    switch (param.type) {
      case 'number':
        // 对于范围较小的数值使用 SliderInput，否则使用 NumberInput
        const range = (param.max || 1) - (param.min || 0);
        if (range <= 10) {
          return (
            <SliderInput
              value={value !== undefined ? value : param.defaultValue}
              onChange={(newValue: number) => onUpdateParam(param.name, newValue)}
              min={param.min || 0}
              max={param.max || 1}
              step={param.step || 0.01}
              showInput={true}
            />
          );
        } else {
          return (
            <NumberInput
              label=""
              value={value !== undefined ? value : param.defaultValue}
              onChange={(newValue: number) => onUpdateParam(param.name, newValue)}
              min={param.min || 0}
              max={param.max || 1}
              step={param.step || 0.01}
            />
          );
        }
      case 'color':
        return (
          <ColorPicker
            value={value || param.defaultValue}
            onChange={(newValue: string) => onUpdateParam(param.name, newValue)}
            compact={true}
          />
        );
      case 'boolean':
        return (
          <Switch
            checked={value !== undefined ? value : param.defaultValue}
            onChange={(e: any) => onUpdateParam(param.name, e.target.checked)}
            size="small"
          />
        );
      case 'select':
        return (
          <SelectInput
            value={value !== undefined ? value : param.defaultValue}
            options={param.options || []}
            onChange={(newValue: any) => onUpdateParam(param.name, newValue)}
            width="100%"
          />
        );
      default:
        return null;
    }
  };

  return (
    <Paper
      elevation={0}
      sx={{
        border: '1px solid',
        borderColor: isSelected ? 'primary.main' : 'divider',
        borderRadius: 1,
        mb: 0.75,
        overflow: 'hidden',
        backgroundColor: 'background.paper'
      }}
    >
      {/* 滤镜头部 */}
      <Box
        sx={{
          px: 1,
          py: 0.5,
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          backgroundColor: isSelected ? 'action.selected' : 'transparent',
          '&:hover': {
            backgroundColor: 'action.hover'
          },
          minHeight: '32px'
        }}
        onClick={onSelect}
      >
        {/* 滤镜图标和名称 */}
        <Box sx={{ display: 'flex', alignItems: 'center', flex: 1, minWidth: 0 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mr: 0.5, fontSize: '16px' }}>
            {filterOption.icon}
          </Box>
          <Typography
            variant="body2"
            sx={{
              fontWeight: 'medium',
              fontSize: '13px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
          >
            {filterOption.name}
          </Typography>
        </Box>

        {/* 操作按钮 */}
        <Box sx={{ display: 'flex', alignItems: 'center', flexShrink: 0 }}>
          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              setExpanded(!expanded);
            }}
            sx={{ p: 0.25, minWidth: '24px', minHeight: '24px' }}
          >
            {expanded ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
          </IconButton>

          <IconButton
            size="small"
            color="error"
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            sx={{ ml: 0.25, p: 0.25, minWidth: '24px', minHeight: '24px' }}
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Box>
      </Box>

      {/* 滤镜参数面板 */}
      <Collapse in={expanded}>
        <Divider />
        <Box sx={{ px: 1, py: 0.75 }}>
          {paramDefs.length > 0 ? (
            paramDefs.map((param, index) => (
              <Box key={index} sx={{ mb: 1 }}>
                <Typography
                  variant="caption"
                  sx={{
                    display: 'block',
                    fontWeight: 'medium',
                    fontSize: '11px',
                    color: 'text.secondary',
                    mb: 0.25
                  }}
                >
                  {param.name}
                </Typography>
                <Box sx={{ '& > *': { mb: 0 } }}>
                  {renderParamControl(param, filter.params[param.name])}
                </Box>
              </Box>
            ))
          ) : (
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                textAlign: 'center',
                py: 0.75,
                fontSize: '12px'
              }}
            >
              此滤镜无可调参数
            </Typography>
          )}
        </Box>
      </Collapse>
    </Paper>
  );
}, (prevProps, nextProps) => {
  // 只有当filter、isSelected或其他关键props发生变化时才重新渲染
  return prevProps.filter === nextProps.filter &&
    prevProps.isSelected === nextProps.isSelected &&
    prevProps.filterOption === nextProps.filterOption &&
    prevProps.paramDefs === nextProps.paramDefs;
});

export default FilterItem;
