import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Alert,
  Snackbar,
  IconButton,
  Tooltip
} from '@mui/material';
import './LightCodeEditor.css'; // 自定义样式
import SaveIcon from '@mui/icons-material/Save';
import CloseIcon from '@mui/icons-material/Close';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import LightModeIcon from '@mui/icons-material/LightMode';
import DarkModeIcon from '@mui/icons-material/DarkMode';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import IsolatedEditor from './IsolatedEditor'; // 导入隔离编辑器组件

// 代码编辑器对话框属性
interface LightCodeEditorProps {
  open: boolean;
  title?: string;
  code: string;
  language?: string;
  onClose: () => void;
  onSave: (code: string) => void;
}

const LightCodeEditor: React.FC<LightCodeEditorProps> = ({
  open,
  title = '代码编辑器',
  code: initialCode,
  language = 'javascript',
  onClose,
  onSave
}) => {
  // 使用state存储当前编辑器代码，这样可以在编辑时更新
  const [currentCode, setCurrentCode] = useState(initialCode);
  // 使用ref存储当前编辑器代码，避免不必要的重新渲染
  const codeRef = useRef(initialCode);

  // 主题设置
  const [theme, setTheme] = useState<'vs-dark' | 'light'>('vs-dark');

  // 编辑器加载状态
  const [isEditorReady, setIsEditorReady] = useState(false);

  // 消息提示
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info'>('success');

  // 自定义关闭处理函数
  const handleClose = useCallback(() => {
    // 移除所有可能的焦点元素
    if (document.activeElement instanceof HTMLElement) {
      document.activeElement.blur();
    }

    // 确保没有元素保持焦点
    document.body.focus();

    // 调用关闭回调
    onClose();
  }, [onClose]);

  // 处理编辑器内容变化 - 更新state和ref
  const handleEditorChange = useCallback((value: string) => {
    setCurrentCode(value); // 更新state，这样可以在编辑时更新
    codeRef.current = value; // 同时更新ref，用于保存操作
  }, []);

  // 处理编辑器中的Escape键
  const handleEditorEscape = useCallback(() => {
    handleClose();
  }, [handleClose]);

  // 保存代码
  const handleSave = useCallback(() => {
    try {
      // 获取当前编辑器代码
      const currentCode = codeRef.current;

      // 关闭对话框
      handleClose();

      // 延迟执行保存操作，确保对话框已完全关闭
      setTimeout(() => {
        // 调用保存回调
        onSave(currentCode);

        // 显示成功消息
        setSnackbarMessage('代码保存成功');
        setSnackbarSeverity('success');
        setSnackbarOpen(true);
      }, 50);
    } catch (error) {
      console.error('保存代码失败:', error);
      setSnackbarMessage(`保存代码失败: ${error instanceof Error ? error.message : '未知错误'}`);
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  }, [handleClose, onSave]);

  // 切换主题
  const toggleTheme = useCallback(() => {
    setTheme(prevTheme => prevTheme === 'vs-dark' ? 'light' : 'vs-dark');
  }, []);

  // 复制代码到剪贴板
  const copyToClipboard = useCallback(() => {
    navigator.clipboard.writeText(codeRef.current)
      .then(() => {
        setSnackbarMessage('代码已复制到剪贴板');
        setSnackbarSeverity('info');
        setSnackbarOpen(true);
      })
      .catch(err => {
        console.error('复制失败:', err);
        setSnackbarMessage('复制失败');
        setSnackbarSeverity('error');
        setSnackbarOpen(true);
      });
  }, []);

  // 关闭消息提示
  const handleSnackbarClose = useCallback(() => {
    setSnackbarOpen(false);
  }, []);

  // 阻止事件冒泡
  const handleDialogClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  }, []);

  // 当initialCode变化时更新currentCode和codeRef
  useEffect(() => {
    if (open) {
      setCurrentCode(initialCode);
      codeRef.current = initialCode;
    }
  }, [open, initialCode]);

  // 处理编辑器就绪状态
  const handleEditorReady = useCallback(() => {
    setIsEditorReady(true);
  }, []);

  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        fullWidth
        maxWidth="md"
        disableEnforceFocus
        keepMounted
        aria-modal="true"
        onClick={handleDialogClick}
        sx={{
          '& .MuiDialog-paper': {
            height: '80vh',
            display: 'flex',
            flexDirection: 'column',
            zIndex: 9999
          }
        }}
      >
        <div
          className="code-editor-container"
          style={{ display: 'flex', flexDirection: 'column', height: '100%' }}
        >
          <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>{title}</span>
            <Box>
              <Tooltip title="复制代码">
                <IconButton onClick={copyToClipboard} size="small" sx={{ mr: 1 }}>
                  <ContentCopyIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Tooltip title={theme === 'vs-dark' ? '切换到亮色主题' : '切换到暗色主题'}>
                <IconButton onClick={toggleTheme} size="small">
                  {theme === 'vs-dark' ? <LightModeIcon fontSize="small" /> : <DarkModeIcon fontSize="small" />}
                </IconButton>
              </Tooltip>
            </Box>
          </DialogTitle>

          <DialogContent dividers sx={{ p: 0, display: 'flex', flexDirection: 'column', flex: 1 }}>
            {/* 代码编辑器 */}
            <Box sx={{ flex: 1, overflow: 'hidden' }}>
              <IsolatedEditor
                code={currentCode}
                language={language}
                theme={theme}
                onChange={handleEditorChange}
                onEscape={handleEditorEscape}
                onReady={handleEditorReady}
              />
            </Box>
          </DialogContent>

          <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>
            <Box>
              {isEditorReady && (
                <Typography variant="caption" color="success.main" sx={{ display: 'flex', alignItems: 'center' }}>
                  <CheckCircleOutlineIcon fontSize="small" sx={{ mr: 0.5 }} />
                  编辑器已就绪
                </Typography>
              )}
            </Box>
            <Box>
              <Button
                onClick={handleClose}
                startIcon={<CloseIcon />}
                sx={{ mr: 1 }}
              >
                取消
              </Button>
              <Button
                variant="contained"
                color="primary"
                startIcon={<SaveIcon />}
                onClick={handleSave}
                disabled={!isEditorReady}
              >
                保存
              </Button>
            </Box>
          </DialogActions>
        </div>
      </Dialog>

      {/* 消息提示 */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbarSeverity}
          variant="filled"
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

export default LightCodeEditor;
