/**
 * 对象类型树工具类
 * 专门处理RPG Maker MZ中显示对象和逻辑对象的类型树获取
 */

export interface TypeTreeNode {
  name: string;
  isCurrentType: boolean;
  description: string;
  children: TypeTreeNode[];
  parent: TypeTreeNode | null;
  depth: number;
  isEditable: boolean; // 是否可编辑（自定义类型）
}

export interface TypeTreeInfo {
  targetObject: any;        // 要显示类型树的目标对象（优先逻辑对象）
  displayObject: any;       // 原始显示对象
  typeTree: TypeTreeNode[]; // 类型继承树
  objectType: 'logic-primary' | 'display-only'; // 对象类型
  logicObjectType?: string; // 逻辑对象类型名
  displayObjectType?: string; // 显示对象类型名
}

/**
 * 获取关联的逻辑对象
 */
export function getLogicObject(displayObj: any): any | null {
  if (!displayObj) return null;

  // 检查常见的关联属性
  const logicProperties = ['_character', '_battler', '_enemy', '_actor'];

  for (const prop of logicProperties) {
    if (displayObj[prop] && isLogicObject(displayObj[prop])) {
      return displayObj[prop];
    }
  }

  return null;
}

/**
 * 判断是否是逻辑对象
 */
export function isLogicObject(obj: any): boolean {
  if (!obj || !obj.constructor) return false;

  const typeName = obj.constructor.name;
  // 逻辑对象通常以 Game_ 开头
  return typeName.startsWith('Game_');
}

/**
 * 获取对象的继承链
 */
export function getInheritanceChain(obj: any): string[] {
  if (!obj || !obj.constructor) return [];

  const chain: string[] = [];
  let currentConstructor = obj.constructor;

  // 添加当前对象的类型
  if (currentConstructor.name) {
    chain.push(currentConstructor.name);
  }

  // 遍历原型链
  let prototype = Object.getPrototypeOf(obj);
  while (prototype && prototype.constructor && prototype.constructor.name !== 'Object') {
    const constructorName = prototype.constructor.name;
    if (constructorName && !chain.includes(constructorName)) {
      chain.push(constructorName);
    }
    prototype = Object.getPrototypeOf(prototype);
  }

  return chain;
}

/**
 * 构建继承树 - 正确的层次结构：基类在上，当前对象在最下面
 */
export function buildInheritanceTree(obj: any): TypeTreeNode[] {
  const inheritanceChain = getInheritanceChain(obj);
  if (inheritanceChain.length === 0) return [];

  const currentTypeName = inheritanceChain[0];
  const parentChain = inheritanceChain.slice(1);

  // 反转继承链，让最基础的类在最上面，当前对象在最下面
  const reversedChain = [...parentChain].reverse();
  reversedChain.push(currentTypeName);

  // 构建从基类到派生类的树结构
  const nodes: TypeTreeNode[] = [];
  let currentParent: TypeTreeNode | null = null;

  reversedChain.forEach((typeName, index) => {
    const isCurrentType = typeName === currentTypeName;
    const isBaseType = index === 0;

    const node: TypeTreeNode = {
      name: typeName,
      isCurrentType,
      description: isCurrentType ? '当前对象类型' :
        isBaseType ? '基类' : '父类型',
      children: [],
      parent: currentParent,
      depth: index,
      isEditable: false // 基础类型通常不可编辑
    };

    if (currentParent) {
      currentParent.children.push(node);
    } else {
      // 第一个节点（基类）作为根节点
      nodes.push(node);
    }

    currentParent = node;
  });

  return nodes;
}

/**
 * 获取对象的类型树信息 - 优先逻辑对象
 */
export function getObjectTypeTree(obj: any): TypeTreeInfo {
  if (!obj) {
    return {
      targetObject: null,
      displayObject: null,
      typeTree: [],
      objectType: 'display-only'
    };
  }

  // 1. 检查是否是显示对象，如果是则获取关联的逻辑对象
  const logicObject = getLogicObject(obj);

  // 2. 如果有逻辑对象，使用逻辑对象的类型树
  if (logicObject) {
    return {
      targetObject: logicObject,
      displayObject: obj,
      typeTree: buildInheritanceTree(logicObject),
      objectType: 'logic-primary',
      logicObjectType: logicObject.constructor?.name || 'Unknown',
      displayObjectType: obj.constructor?.name || 'Unknown'
    };
  }

  // 3. 如果没有逻辑对象，使用显示对象的类型树
  return {
    targetObject: obj,
    displayObject: obj,
    typeTree: buildInheritanceTree(obj),
    objectType: 'display-only',
    displayObjectType: obj.constructor?.name || 'Unknown'
  };
}

/**
 * 获取类型树的深度
 */
export function getTypeTreeDepth(typeTree: TypeTreeNode[]): number {
  if (typeTree.length === 0) return 0;

  let maxDepth = 0;

  function calculateDepth(nodes: TypeTreeNode[], currentDepth: number = 0): void {
    for (const node of nodes) {
      maxDepth = Math.max(maxDepth, currentDepth);
      if (node.children.length > 0) {
        calculateDepth(node.children, currentDepth + 1);
      }
    }
  }

  calculateDepth(typeTree);
  return maxDepth;
}

/**
 * 查找类型树中的特定节点
 */
export function findNodeInTypeTree(typeTree: TypeTreeNode[], typeName: string): TypeTreeNode | null {
  for (const node of typeTree) {
    if (node.name === typeName) {
      return node;
    }

    if (node.children.length > 0) {
      const found = findNodeInTypeTree(node.children, typeName);
      if (found) return found;
    }
  }

  return null;
}

/**
 * 获取类型树的所有节点ID
 */
export function getAllNodeIds(typeTree: TypeTreeNode[]): string[] {
  const ids: string[] = [];

  function collectIds(nodes: TypeTreeNode[]): void {
    for (const node of nodes) {
      ids.push(node.name);
      if (node.children.length > 0) {
        collectIds(node.children);
      }
    }
  }

  collectIds(typeTree);
  return ids;
}