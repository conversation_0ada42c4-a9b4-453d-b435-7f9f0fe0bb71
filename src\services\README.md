# 属性管理服务优化

## 🎯 优化目标

将原来混合在一起的属性处理逻辑按类型分离，提供专门的处理器：

- **基础属性处理器** - 处理位置、大小、变换、外观等基础属性
- **Bitmap属性处理器** - 处理纹理、字体、文本等bitmap相关属性  
- **Sprite颜色处理器** - 处理色相、色调、混合颜色等sprite颜色属性
- **滤镜处理器** - 处理滤镜相关操作（已完成）

## 📊 架构对比

### 优化前的架构
```
属性组件 → Store.setSelectedObjectsProperty → ProtoChange.changeObjectProperty + BackendService.recordPropertyModification
                                                ↓
                                        混合处理所有类型的属性
```

### 优化后的架构
```
属性组件 → Store.setSelectedObjectsProperty → PropertyManager.updateProperty
                                                ↓
                                        根据属性类型自动选择处理器
                                                ↓
                    ┌─────────────────────────────────────────────────────┐
                    ↓                    ↓                    ↓           ↓
            BasicProperty        BitmapProperty      SpriteColor     Filter
            ↓                    ↓                    ↓           ↓
        前端更新+后端记录      前端更新+后端记录      前端更新+后端记录  前端更新+后端记录
```

## 🚀 使用方式

### 1. 统一使用（推荐）
```typescript
import { PropertyManager } from '../services';

// 自动检测属性类型并使用合适的处理器
const result = await PropertyManager.updateProperty(objects, 'x', 100);
if (result.success) {
  console.log(`属性更新成功，类型: ${result.propertyType}`);
}
```

### 2. 直接使用特定处理器
```typescript
import { 
  updateBasicProperty, 
  updateBitmapProperty, 
  updateSpriteColorProperty 
} from '../services';

// 基础属性
updateBasicProperty(objects, 'x', 100);

// Bitmap属性
updateBitmapProperty(objects, 'bitmap._url', '/path/to/image.png');

// Sprite颜色属性
updateSpriteColorProperty(objects, 'hue', 180);
```

### 3. 属性类型检测
```typescript
import { PropertyManager, PropertyType } from '../services';

const propertyType = PropertyManager.detectPropertyType('x');
// 返回: PropertyType.BASIC

const propertyType2 = PropertyManager.detectPropertyType('bitmap._url');
// 返回: PropertyType.BITMAP
```

## 📋 属性分类

### 基础属性 (BasicProperty)
- **位置**: `x`, `y`
- **大小**: `width`, `height`
- **变换**: `rotation`, `skew.x`, `skew.y`, `anchor.x`, `anchor.y`
- **外观**: `alpha`, `tint`, `color`
- **边框**: `border.width`, `border.color`
- **可见性**: `visible`

### Bitmap属性 (BitmapProperty)
- **纹理**: `bitmap._url`
- **元素数组**: `bitmap.elements`
- **字体全局属性**: `fontSize`, `fontFace`, `textColor`, `outlineColor`, `outlineWidth`, `fontBold`, `fontItalic`
- **文本项属性**: `text`, `x`, `y`, `maxWidth`, `lineHeight`, `align`

### Sprite颜色属性 (SpriteColorProperty)
- **色相**: `hue` (0-360)
- **色调**: `colorTone` ([red, green, blue, gray])
- **混合颜色**: `blendColor` ([red, green, blue, alpha])
- **混合模式**: `blendMode` (数字)

### 滤镜属性 (FilterProperty)
- 通过专门的滤镜方法处理
- `recordFilterParamModification`
- `recordAddFilter`
- `recordRemoveFilter`
- `recordReorderFilters`

## 🔧 优势

### 1. **代码组织更清晰**
- 每种属性类型有专门的处理器
- 逻辑分离，易于维护和扩展

### 2. **类型安全**
- 每个处理器都有明确的类型定义
- 编译时就能发现类型错误

### 3. **性能优化**
- 避免不必要的类型检查
- 针对性的优化处理逻辑

### 4. **易于扩展**
- 新增属性类型只需要添加新的处理器
- 不影响现有代码

### 5. **向后兼容**
- 保留原有的通用方法
- 现有代码无需修改即可工作

## 🎯 迁移指南

### 对于属性组件开发者
无需修改代码，继续使用 `setSelectedObjectsProperty` 即可：

```typescript
// 原来的代码继续工作
setSelectedObjectsProperty('x', 100);
setSelectedObjectsProperty('bitmap._url', '/path/to/image.png');
setSelectedObjectsProperty('hue', 180);
```

### 对于需要自定义处理的场景
可以直接使用特定的处理器：

```typescript
import { updateBasicProperty, recordBasicPropertyModification } from '../services';

// 只更新前端，不记录后端
updateBasicProperty(objects, 'x', 100);

// 只记录后端，不更新前端
await recordBasicPropertyModification(object, 'x', 100);
```

## 🔍 调试和监控

PropertyManager会自动记录属性类型检测结果：

```
PropertyManager: 检测到属性类型 x -> basic
PropertyManager: 检测到属性类型 bitmap._url -> bitmap
PropertyManager: 检测到属性类型 hue -> spriteColor
```

这有助于调试和了解属性处理流程。

## 📈 未来扩展

可以轻松添加新的属性类型处理器：

1. 在 `services/backendServer/` 下创建新的处理器文件
2. 在 `PropertyManager` 中添加检测逻辑
3. 在 `services/index.ts` 中导出新的服务

例如添加动画属性处理器：
```typescript
// services/backendServer/animation.ts
export const isAnimationProperty = (propertyName: string): boolean => { ... };
export const updateAnimationProperty = (objects: any[], propertyName: string, value: any): boolean => { ... };
```
