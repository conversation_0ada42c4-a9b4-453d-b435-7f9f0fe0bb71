use serde_json;
use std::collections::HashMap;

/// 获取对象类型的实际运行时类型
fn get_runtime_type(logical_type: &str) -> &str {
    match logical_type {
        "Button" => "Sprite",
        "Label" => "Sprite",
        "Text" => "Sprite",
        "Window" => "Window_Base",
        _ => logical_type,
    }
}

/// 生成对象生命周期相关代码（创建和删除，支持抵消逻辑）
pub fn generate_object_lifecycle_code(
    var_name: &str,
    properties: &HashMap<String, serde_json::Value>,
) -> String {
    let mut code = String::new();

    // 分析创建和删除操作，检查是否有抵消的情况
    let mut create_operations: HashMap<String, (&str, &serde_json::Value)> = HashMap::new();
    let mut delete_operations: HashMap<String, (&str, &serde_json::Value)> = HashMap::new();

    // 收集所有创建和删除操作
    for (key, value) in properties {
        if key.starts_with("__CREATE_CHILD__") {
            let object_type = key.trim_start_matches("__CREATE_CHILD__");

            // 提取对象名称作为唯一标识
            let object_name = value
                .get("objectName")
                .and_then(|v| v.as_str())
                .unwrap_or("未知对象");

            // 使用运行时类型进行匹配
            let runtime_type = get_runtime_type(object_type);
            let unique_key = format!("{}_{}", runtime_type, object_name);
            create_operations.insert(unique_key, (object_type, value));
        } else if key.starts_with("__DELETE_CHILD__") {
            let object_type = key.trim_start_matches("__DELETE_CHILD__");

            // 提取对象名称作为唯一标识
            let object_name = value
                .get("objectName")
                .and_then(|v| v.as_str())
                .unwrap_or("未知对象");

            // 删除操作已经使用运行时类型，直接使用
            let unique_key = format!("{}_{}", object_type, object_name);
            delete_operations.insert(unique_key, (object_type, value));
        }
    }

    // 检查抵消情况并生成代码
    let mut processed_keys: std::collections::HashSet<String> = std::collections::HashSet::new();

    // 处理创建操作
    for (unique_key, (object_type, creation_info)) in &create_operations {
        if delete_operations.contains_key(unique_key) {
            // 有对应的删除操作，抵消了
            let object_name = creation_info
                .get("objectName")
                .and_then(|v| v.as_str())
                .unwrap_or("未知对象");

            code.push_str(&format!(
                "            // {} 对象 '{}' 的创建和删除操作已抵消，无需生成代码\n",
                object_type, object_name
            ));
            code.push_str(&format!(
                "            if (DEBUG) log('对象生命周期操作抵消: {} - {}');\n",
                object_type, object_name
            ));
            code.push_str("\n");

            processed_keys.insert(unique_key.clone());
        } else {
            // 只有创建操作，生成创建代码
            code.push_str(&generate_object_creation_code(
                var_name,
                object_type,
                creation_info,
            ));
            processed_keys.insert(unique_key.clone());
        }
    }

    // 处理只有删除操作的情况
    for (unique_key, (object_type, deletion_info)) in &delete_operations {
        if !processed_keys.contains(unique_key) {
            // 只有删除操作，没有对应的创建操作
            code.push_str(&generate_object_deletion_code(
                var_name,
                object_type,
                deletion_info,
            ));
        }
    }

    code
}

/// 生成对象创建代码
fn generate_object_creation_code(
    var_name: &str,
    object_type: &str,
    creation_info: &serde_json::Value,
) -> String {
    let mut code = String::new();

    // 解析创建信息
    let object_name = creation_info
        .get("objectName")
        .and_then(|v| v.as_str())
        .unwrap_or("新对象");

    let default_properties = serde_json::Value::Object(serde_json::Map::new());
    let properties = creation_info
        .get("properties")
        .unwrap_or(&default_properties);

    // 生成唯一的变量名
    let child_var_name = format!(
        "child_{}_{}",
        object_type.to_lowercase(),
        chrono::Utc::now().timestamp_millis() % 10000
    );

    code.push_str(&format!(
        "            // 创建 {} 对象: {}\n",
        object_type, object_name
    ));

    // 根据对象类型生成创建代码
    match object_type {
        "Sprite" => {
            code.push_str(&format!(
                "            const {} = new Sprite();\n",
                child_var_name
            ));
        }
        "Container" => {
            code.push_str(&format!(
                "            const {} = new PIXI.Container();\n",
                child_var_name
            ));
        }
        "Label" => {
            code.push_str(&format!(
                "            const {} = new Sprite();\n",
                child_var_name
            ));
            code.push_str(&format!(
                "            {}.bitmap = new Bitmap(200, 40);\n",
                child_var_name
            ));
        }
        "Text" => {
            code.push_str(&format!(
                "            const {} = new Sprite();\n",
                child_var_name
            ));
            code.push_str(&format!(
                "            {}.bitmap = new Bitmap(200, 40);\n",
                child_var_name
            ));
        }
        "Button" => {
            code.push_str(&format!(
                "            const {} = new Sprite();\n",
                child_var_name
            ));
            code.push_str(&format!(
                "            {}.bitmap = new Bitmap(100, 30);\n",
                child_var_name
            ));
        }
        "Window" => {
            code.push_str(&format!(
                "            const {} = new Window_Base(new Rectangle(0, 0, 200, 150));\n",
                child_var_name
            ));
        }
        _ => {
            // 默认创建Sprite
            code.push_str(&format!(
                "            const {} = new Sprite();\n",
                child_var_name
            ));
        }
    }

    // 设置对象属性
    if let Some(props_obj) = properties.as_object() {
        for (prop_key, prop_value) in props_obj {
            let value_str =
                serde_json::to_string(prop_value).unwrap_or_else(|_| "null".to_string());

            match prop_key.as_str() {
                "text" => {
                    // 特殊处理文本属性
                    code.push_str(&format!("            if ({}.bitmap) {{\n", child_var_name));
                    code.push_str(&format!("                {}.bitmap.drawText({}, 0, 0, {}.bitmap.width, {}.bitmap.height, 'left');\n",
                        child_var_name, value_str, child_var_name, child_var_name));
                    code.push_str(&format!("            }}\n"));
                }
                "anchorX" => {
                    code.push_str(&format!(
                        "            if ({}.anchor) {}.anchor.x = {};\n",
                        child_var_name, child_var_name, value_str
                    ));
                }
                "anchorY" => {
                    code.push_str(&format!(
                        "            if ({}.anchor) {}.anchor.y = {};\n",
                        child_var_name, child_var_name, value_str
                    ));
                }
                "scaleX" => {
                    code.push_str(&format!(
                        "            if ({}.scale) {}.scale.x = {};\n",
                        child_var_name, child_var_name, value_str
                    ));
                }
                "scaleY" => {
                    code.push_str(&format!(
                        "            if ({}.scale) {}.scale.y = {};\n",
                        child_var_name, child_var_name, value_str
                    ));
                }
                "name" => {
                    // 特殊处理name属性，确保它被正确设置并添加调试日志
                    code.push_str(&format!(
                        "            {}.name = {};\n",
                        child_var_name, value_str
                    ));
                    code.push_str(&format!(
                        "            if (DEBUG) log('设置对象名称: {}');\n",
                        value_str
                    ));
                }
                _ => {
                    // 普通属性直接设置
                    code.push_str(&format!(
                        "            {}.{} = {};\n",
                        child_var_name, prop_key, value_str
                    ));
                }
            }
        }
    }

    // 添加到父对象
    code.push_str(&format!("            // 添加到父对象\n"));
    code.push_str(&format!(
        "            targetObject_{}.addChild({});\n",
        var_name, child_var_name
    ));
    code.push_str(&format!(
        "            if (DEBUG) log('创建并添加 {} 对象: {}');\n",
        object_type, object_name
    ));
    code.push_str("\n");

    code
}

/// 生成智能对象删除代码
fn generate_object_deletion_code(
    var_name: &str,
    object_type: &str,
    deletion_info: &serde_json::Value,
) -> String {
    let mut code = String::new();

    // 解析删除信息
    let object_name = deletion_info
        .get("objectName")
        .and_then(|v| v.as_str())
        .unwrap_or("未知对象");

    let default_path = vec![];
    let object_path = deletion_info
        .get("objectPath")
        .and_then(|v| v.as_array())
        .unwrap_or(&default_path);

    code.push_str(&format!(
        "            // 智能删除 {} 对象: {}\n",
        object_type, object_name
    ));

    // 生成智能删除逻辑
    code.push_str(&format!("            (function() {{\n"));
    code.push_str(&format!(
        "                // 1. 首先检查是否有对应的创建缓存\n"
    ));
    code.push_str(&format!("                let foundInCache = false;\n"));
    code.push_str(&format!(
        "                const parentObject = targetObject_{};\n",
        var_name
    ));
    code.push_str(&format!("                if (!parentObject) {{\n"));
    code.push_str(&format!(
        "                    if (DEBUG) log('父对象不存在，无法删除 {} 对象: {}');\n",
        object_type, object_name
    ));
    code.push_str(&format!("                    return;\n"));
    code.push_str(&format!("                }}\n"));
    code.push_str(&format!("                \n"));

    // 检查缓存中的创建记录
    code.push_str(&format!(
        "                // 2. 查找最近创建的同类型对象（缓存检查）\n"
    ));
    code.push_str(&format!(
        "                const children = parentObject.children || [];\n"
    ));
    code.push_str(&format!("                let targetChild = null;\n"));
    code.push_str(&format!("                \n"));

    // 优先按名称查找
    code.push_str(&format!("                // 3. 优先按名称精确匹配\n"));
    code.push_str(&format!(
        "                for (let i = children.length - 1; i >= 0; i--) {{\n"
    ));
    code.push_str(&format!("                    const child = children[i];\n"));
    code.push_str(&format!(
        "                    if (child && child.constructor.name === '{}' && child.name === '{}') {{\n",
        object_type, object_name
    ));
    code.push_str(&format!("                        targetChild = child;\n"));
    code.push_str(&format!("                        foundInCache = true;\n"));
    code.push_str(&format!(
        "                        if (DEBUG) log('找到缓存中的对象（按名称）: {} - {}');\n",
        object_type, object_name
    ));
    code.push_str(&format!("                        break;\n"));
    code.push_str(&format!("                    }}\n"));
    code.push_str(&format!("                }}\n"));
    code.push_str(&format!("                \n"));

    // 如果按名称没找到，尝试按路径查找
    if object_path.len() > 1 {
        code.push_str(&format!(
            "                // 4. 如果缓存中没找到，按路径查找\n"
        ));
        code.push_str(&format!("                if (!targetChild) {{\n"));

        let path_json = serde_json::to_string(object_path).unwrap_or_default();
        code.push_str(&format!(
            "                    const objectPath = {};\n",
            path_json
        ));
        code.push_str(&format!(
            "                    let currentObj = parentObject;\n"
        ));
        code.push_str(&format!("                    \n"));
        code.push_str(&format!(
            "                    // 跳过场景名称，从第二个元素开始\n"
        ));
        code.push_str(&format!(
            "                    for (let i = 1; i < objectPath.length; i++) {{\n"
        ));
        code.push_str(&format!(
            "                        const index = parseInt(objectPath[i]);\n"
        ));
        code.push_str(&format!(
            "                        if (isNaN(index) || !currentObj.children || index >= currentObj.children.length) {{\n"
        ));
        code.push_str(&format!(
            "                            if (DEBUG) log('路径无效，无法找到要删除的对象: {}');\n",
            object_name
        ));
        code.push_str(&format!("                            break;\n"));
        code.push_str(&format!("                        }}\n"));
        code.push_str(&format!("                        \n"));
        code.push_str(&format!(
            "                        if (i === objectPath.length - 1) {{\n"
        ));
        code.push_str(&format!(
            "                            // 最后一个索引，这是要删除的对象\n"
        ));
        code.push_str(&format!(
            "                            targetChild = currentObj.children[index];\n"
        ));
        code.push_str(&format!(
            "                            if (DEBUG) log('按路径找到对象: ' + (targetChild ? targetChild.constructor.name : 'null') + ' (索引: ' + index + ')');\n"
        ));
        code.push_str(&format!("                        }} else {{\n"));
        code.push_str(&format!(
            "                            currentObj = currentObj.children[index];\n"
        ));
        code.push_str(&format!("                        }}\n"));
        code.push_str(&format!("                    }}\n"));
        code.push_str(&format!("                }}\n"));
    }

    // 最后的删除操作
    code.push_str(&format!("                \n"));
    code.push_str(&format!("                // 5. 执行删除操作\n"));
    code.push_str(&format!("                if (targetChild) {{\n"));
    code.push_str(&format!(
        "                    const parent = targetChild.parent;\n"
    ));
    code.push_str(&format!(
        "                    if (parent && parent.removeChild) {{\n"
    ));
    code.push_str(&format!(
        "                        parent.removeChild(targetChild);\n"
    ));
    code.push_str(&format!(
        "                        if (DEBUG) log('成功删除 {} 对象: {} (' + (foundInCache ? '缓存中的' : '路径中的') + ')');\n",
        object_type, object_name
    ));
    code.push_str(&format!("                    }} else {{\n"));
    code.push_str(&format!(
        "                        if (DEBUG) log('无法删除对象，父对象无效: {}');\n",
        object_name
    ));
    code.push_str(&format!("                    }}\n"));
    code.push_str(&format!("                }} else {{\n"));
    code.push_str(&format!(
        "                    if (DEBUG) log('未找到要删除的 {} 对象: {}');\n",
        object_type, object_name
    ));
    code.push_str(&format!("                }}\n"));
    code.push_str(&format!("            }})();\n"));

    code.push_str("\n");
    code
}

/// 检查属性中是否包含对象生命周期操作
pub fn has_object_lifecycle_operations(properties: &HashMap<String, serde_json::Value>) -> bool {
    properties
        .keys()
        .any(|k| k.starts_with("__CREATE_CHILD__") || k.starts_with("__DELETE_CHILD__"))
}

/// 获取所有对象生命周期操作的属性名
pub fn get_lifecycle_property_names(
    properties: &HashMap<String, serde_json::Value>,
) -> Vec<String> {
    properties
        .keys()
        .filter(|k| k.starts_with("__CREATE_CHILD__") || k.starts_with("__DELETE_CHILD__"))
        .cloned()
        .collect()
}
