/*:
 * @plugindesc 布局容器插件 - 提供灵活的UI布局功能
 * <AUTHOR>
 * @target MZ
 * @help
 * ============================================================================
 * 布局容器插件 v2.1.0
 * ============================================================================
 *
 * 这个插件直接扩展PIXI.Container，在原型链上添加布局方法，
 * 使所有容器都能使用灵活的布局功能，简化UI元素的排列。
 *
 * 布局功能分为两大类：
 * 1. 相对自身布局：对象相对于父级容器的定位
 *    - topLeft, topRight, bottomLeft, bottomRight: 对齐到容器的四个角
 *    - topCenter, bottomCenter, leftCenter, rightCenter: 对齐到容器的四个边的中心
 *    - center: 居中对齐
 *    - 可以设置外边距(margin)调整位置
 *
 * 2. 子元素布局：管理容器内子元素的排列
 *    - column: 垂直排列子元素
 *    - row: 水平排列子元素
 *    - grid: 网格排列子元素
 *    - 可以设置多种对齐和间距选项
 *
 * 布局选项:
 * - mainAxisAlignment: 主轴对齐方式 ('start', 'center', 'end', 'space-between', 'space-around')
 * - crossAxisAlignment: 交叉轴对齐方式 ('start', 'center', 'end', 'stretch')
 * - spacing: 元素间距 (数字或百分比字符串，统一设置水平和垂直间距)
 * - horizontalSpacing: 水平间距 (优先级高于spacing)
 * - verticalSpacing: 垂直间距 (优先级高于spacing)
 * - padding: 内边距 (数字、百分比字符串或{top, right, bottom, left}对象)
 * - margin: 外边距 (数字、百分比字符串或{top, right, bottom, left}对象)
 * - columns: 网格布局的列数 (仅用于grid布局)
 * - autoUpdate: 是否自动更新布局 (默认false)
 *
 * 使用方法:
 * 1. 创建容器: const container = new PIXI.Container();
 * 2. 添加子元素: container.addChild(sprite1, sprite2, ...);
 * 3. 应用布局: container.applyLayout('column', { options });
 *
 * ============================================================================
 */

(function () {
    'use strict';

    // 调试模式开关
    const DEBUG_MODE = true;

    //-----------------------------------------------------------------------------
    // 工具函数
    //-----------------------------------------------------------------------------

    /**
     * 调试日志输出
     * @param {...any} args - 日志参数
     */
    function debugLog(...args) {
        if (DEBUG_MODE) {
            console.log('[Layout Plugin]', ...args);
        }
    }

    /**
     * 获取默认布局选项
     * @returns {Object} 默认选项对象
     */
    function getDefaultLayoutOptions() {
        return {
            mainAxisAlignment: 'start',
            crossAxisAlignment: 'start',
            spacing: 0,
            horizontalSpacing: null,
            verticalSpacing: null,
            padding: 0,
            margin: 0,
            columns: 2,
            autoUpdate: true  // 默认启用自动更新
        };
    }

    /**
     * 合并布局选项
     * @param {Object} options - 用户提供的选项
     * @returns {Object} 合并后的选项
     */
    function mergeLayoutOptions(options = {}) {
        const defaults = getDefaultLayoutOptions();
        const merged = Object.assign({}, defaults, options);

        // 处理间距的优先级：horizontalSpacing/verticalSpacing > spacing
        if (merged.horizontalSpacing === null) {
            merged.horizontalSpacing = merged.spacing;
        }
        if (merged.verticalSpacing === null) {
            merged.verticalSpacing = merged.spacing;
        }

        return merged;
    }



    //-----------------------------------------------------------------------------
    // PIXI.Container Layout Extension
    //
    // 扩展PIXI.Container，直接在原型链上添加布局方法
    //-----------------------------------------------------------------------------

    // 初始化布局属性
    const _Container_initialize = PIXI.Container.prototype.initialize;
    PIXI.Container.prototype.initialize = function () {
        if (_Container_initialize) {
            _Container_initialize.apply(this, arguments);
        }
        this._layoutType = null;
        this._layoutOptions = null;
        this._margin = { top: 0, left: 0 };
        this._autoUpdateLayout = false;
        this._originalMethods = null; // 存储原始方法引用
    };

    /**
     * 应用布局
     * @param {string} type - 布局类型 ('column', 'row', 'grid', 'topLeft', 等)
     * @param {Object} options - 布局选项
     * @returns {PIXI.Container} 返回this以支持链式调用
     */
    PIXI.Container.prototype.applyLayout = function (type, options) {
        if (!type) {
            console.warn('[Layout Plugin] 布局类型不能为空');
            return this;
        }

        // 合并选项
        const mergedOptions = mergeLayoutOptions(options);

        // 存储布局配置
        this._layoutType = type;
        this._layoutOptions = mergedOptions;

        // 处理margin选项
        this._margin = this._parsePadding(mergedOptions.margin);

        // 设置自动更新布局
        const isChildrenLayout = ['column', 'row', 'grid'].includes(type);

        // 如果已经通过setAutoLayoutUpdate设置了自动更新，保持该状态
        // 否则根据选项设置
        if (this._autoUpdateLayout === undefined || this._autoUpdateLayout === null) {
            this._autoUpdateLayout = isChildrenLayout && mergedOptions.autoUpdate;
        } else if (isChildrenLayout) {
            // 对于子元素布局，保持当前的自动更新状态
            // 不要被mergedOptions.autoUpdate覆盖
            debugLog('保持现有的自动更新状态:', this._autoUpdateLayout);
        } else {
            // 对于非子元素布局，禁用自动更新
            this._autoUpdateLayout = false;
        }

        // 如果是子元素布局且启用了自动更新，设置自动更新
        if (this._autoUpdateLayout && !this._originalMethods) {
            debugLog('设置自动布局更新监听器');
            this._setupAutoLayoutUpdates();
        } else {
            debugLog('跳过设置自动布局更新:', {
                autoUpdate: this._autoUpdateLayout,
                hasOriginalMethods: !!this._originalMethods
            });
        }

        debugLog('应用布局:', type, mergedOptions);

        // 应用对应的布局
        this._executeLayout(type, mergedOptions);

        return this;
    };

    /**
     * 执行具体的布局逻辑
     * @private
     * @param {string} type - 布局类型
     * @param {Object} options - 布局选项
     */
    PIXI.Container.prototype._executeLayout = function (type, options) {
        switch (type) {
            // 子元素布局
            case 'column':
                this._applyColumnLayout(options);
                break;
            case 'row':
                this._applyRowLayout(options);
                break;
            case 'grid':
                this._applyGridLayout(options);
                break;

            // 相对自身布局
            case 'topLeft':
                this._applyTopLeftLayout();
                break;
            case 'topRight':
                this._applyTopRightLayout();
                break;
            case 'bottomLeft':
                this._applyBottomLeftLayout();
                break;
            case 'bottomRight':
                this._applyBottomRightLayout();
                break;
            case 'topCenter':
                this._applyTopCenterLayout();
                break;
            case 'bottomCenter':
                this._applyBottomCenterLayout();
                break;
            case 'leftCenter':
                this._applyLeftCenterLayout();
                break;
            case 'rightCenter':
                this._applyRightCenterLayout();
                break;
            case 'center':
                this._applyCenterLayout();
                break;
            default:
                console.warn('[Layout Plugin] 未知的布局类型:', type);
        }
    };

    /**
     * 更新布局
     * 当容器尺寸或子元素变化时调用此方法重新应用布局
     * @returns {PIXI.Container} 返回this以支持链式调用
     */
    PIXI.Container.prototype.updateLayout = function () {
        if (this._layoutType && this._layoutOptions) {
            this._executeLayout(this._layoutType, this._layoutOptions);
        }
        return this;
    };

    /**
     * 设置是否启用自动布局更新
     * @param {boolean} enabled - 是否启用自动布局更新
     * @returns {PIXI.Container} 返回this以支持链式调用
     */
    PIXI.Container.prototype.setAutoLayoutUpdate = function (enabled) {
        debugLog('setAutoLayoutUpdate调用:', {
            enabled: enabled,
            current: this._autoUpdateLayout,
            hasOriginalMethods: !!this._originalMethods,
            layoutType: this._layoutType
        });

        if (enabled === this._autoUpdateLayout) {
            debugLog('自动布局更新状态未变化，跳过');
            return this;
        }

        this._autoUpdateLayout = !!enabled;

        // 如果启用自动更新且尚未设置，则设置自动更新
        if (this._autoUpdateLayout && !this._originalMethods) {
            debugLog('启用自动布局更新，设置监听器');
            this._setupAutoLayoutUpdates();
        } else if (!this._autoUpdateLayout && this._originalMethods) {
            debugLog('禁用自动布局更新，恢复原始方法');
            this._restoreOriginalMethods();
        }

        debugLog(`${this._autoUpdateLayout ? '启用' : '禁用'}自动布局更新完成`);
        return this;
    };

    /**
     * 清除布局设置
     * @returns {PIXI.Container} 返回this以支持链式调用
     */
    PIXI.Container.prototype.clearLayout = function () {
        this._layoutType = null;
        this._layoutOptions = null;
        this._autoUpdateLayout = false;
        this._restoreOriginalMethods();
        return this;
    };

    /**
     * 获取当前布局状态
     * @returns {Object} 布局状态信息
     */
    PIXI.Container.prototype.getLayoutState = function () {
        return {
            type: this._layoutType,
            options: this._layoutOptions ? Object.assign({}, this._layoutOptions) : null,
            autoUpdate: this._autoUpdateLayout,
            hasAutoUpdate: !!this._originalMethods
        };
    };

    /**
     * 测试自动布局功能
     * @returns {Object} 测试结果
     */
    PIXI.Container.prototype.testAutoLayout = function () {
        const state = this.getLayoutState();
        console.log('[Layout Test] 当前布局状态:', state);

        if (!this._autoUpdateLayout) {
            console.warn('[Layout Test] 自动布局未启用');
            return { success: false, reason: '自动布局未启用' };
        }

        if (!this._originalMethods) {
            console.warn('[Layout Test] 原始方法未被覆盖');
            return { success: false, reason: '原始方法未被覆盖' };
        }

        console.log('[Layout Test] 自动布局配置正确');
        return { success: true };
    };

    /**
     * 添加子元素并更新布局
     * @param {...PIXI.DisplayObject} children - 要添加的子元素
     * @returns {PIXI.DisplayObject} 最后添加的子元素
     */
    PIXI.Container.prototype.addChildAndUpdateLayout = function () {
        const lastChild = PIXI.Container.prototype.addChild.apply(this, arguments);
        this.updateLayout();
        return lastChild;
    };

    /**
     * 移除子元素并更新布局
     * @param {...PIXI.DisplayObject} children - 要移除的子元素
     * @returns {PIXI.DisplayObject} 最后移除的子元素
     */
    PIXI.Container.prototype.removeChildAndUpdateLayout = function () {
        const lastChild = PIXI.Container.prototype.removeChild.apply(this, arguments);
        this.updateLayout();
        return lastChild;
    };

    /**
     * 设置外边距
     * @param {number|string|Object} margin - 外边距值
     * @returns {PIXI.Container} 返回this以支持链式调用
     */
    PIXI.Container.prototype.setMargin = function (margin) {
        this._margin = this._parsePadding(margin);
        this.updateLayout();
        return this;
    };

    /**
     * 触发自动布局更新
     * @private
     */
    PIXI.Container.prototype._triggerAutoLayoutUpdate = function () {
        // 检查是否启用自动更新和是否为子元素布局类型
        const isChildrenLayout = this._layoutType && ['column', 'row', 'grid'].includes(this._layoutType);

        if (!this._autoUpdateLayout || !isChildrenLayout) {
            debugLog('跳过自动布局更新:', {
                autoUpdate: this._autoUpdateLayout,
                layoutType: this._layoutType,
                isChildrenLayout: isChildrenLayout
            });
            return;
        }

        // 如果没有设置布局类型，使用默认的column布局
        if (!this._layoutType) {
            this._layoutType = 'column';
            this._layoutOptions = this._layoutOptions || {
                mainAxisAlignment: 'start',
                crossAxisAlignment: 'start',
                spacing: 0,
                horizontalSpacing: 0,
                verticalSpacing: 0,
                padding: 0,
                margin: 0,
                columns: 2,
                autoUpdate: true
            };
        }

        debugLog('触发自动布局更新:', this._layoutType);

        // 使用requestAnimationFrame确保在下一帧更新，避免闪烁
        if (typeof requestAnimationFrame !== 'undefined') {
            requestAnimationFrame(() => {
                this._executeLayout(this._layoutType, this._layoutOptions);
                debugLog('自动更新布局完成');
            });
        } else {
            // 降级到setTimeout
            setTimeout(() => {
                this._executeLayout(this._layoutType, this._layoutOptions);
                debugLog('自动更新布局完成');
            }, 0);
        }
    };

    /**
     * 设置自动布局更新
     * 覆盖原生的addChild和removeChild方法，使其在添加或删除子元素时自动更新布局
     * @private
     */
    PIXI.Container.prototype._setupAutoLayoutUpdates = function () {
        debugLog('开始设置自动布局更新监听器');

        // 保存原始方法引用
        this._originalMethods = {
            addChild: this.addChild,
            removeChild: this.removeChild,
            addChildAt: this.addChildAt,
            removeChildAt: this.removeChildAt
        };

        debugLog('原始方法已保存');

        // 创建通用的子元素变化处理函数
        const handleChildrenChange = (originalMethod, methodName) => {
            return function () {
                const prevChildCount = this.children ? this.children.length : 0;
                const result = originalMethod.apply(this, arguments);
                const currentChildCount = this.children ? this.children.length : 0;

                debugLog(`${methodName}:`, prevChildCount, '->', currentChildCount, {
                    autoUpdate: this._autoUpdateLayout,
                    layoutType: this._layoutType
                });

                // 检查子元素数量是否变化且启用了自动更新
                if (this._autoUpdateLayout && currentChildCount !== prevChildCount) {
                    debugLog('触发自动布局更新条件满足');
                    this._triggerAutoLayoutUpdate();
                } else {
                    debugLog('跳过自动布局更新:', {
                        autoUpdate: this._autoUpdateLayout,
                        countChanged: currentChildCount !== prevChildCount
                    });
                }

                return result;
            };
        };

        // 覆盖所有相关方法
        this.addChild = handleChildrenChange(this._originalMethods.addChild, '添加子元素');
        this.removeChild = handleChildrenChange(this._originalMethods.removeChild, '移除子元素');
        this.addChildAt = handleChildrenChange(this._originalMethods.addChildAt, '在指定位置添加子元素');
        this.removeChildAt = handleChildrenChange(this._originalMethods.removeChildAt, '移除指定位置的子元素');

        debugLog('自动布局更新监听器设置完成');
    };

    /**
     * 恢复原始方法
     * @private
     */
    PIXI.Container.prototype._restoreOriginalMethods = function () {
        if (this._originalMethods) {
            this.addChild = this._originalMethods.addChild;
            this.removeChild = this._originalMethods.removeChild;
            this.addChildAt = this._originalMethods.addChildAt;
            this.removeChildAt = this._originalMethods.removeChildAt;
            this._originalMethods = null;
        }
    };



    /**
     * 获取可见子元素
     * @private
     * @returns {Array} 可见子元素数组
     */
    PIXI.Container.prototype._getVisibleChildren = function () {
        if (!this.children || !Array.isArray(this.children)) {
            console.warn('[Layout Plugin] 对象没有children属性或children不是数组');
            return [];
        }

        const visibleChildren = this.children.filter(child => {
            // 检查child是否为null或undefined
            if (!child) {
                return false;
            }

            // 检查_hidden属性和visible属性
            const isHidden = child._hidden === true;
            const isVisible = child.visible !== false; // 如果visible未定义，默认为可见

            return !isHidden && isVisible;
        });

        debugLog('获取可见子元素:', this.children.length, '个子元素中有', visibleChildren.length, '个可见');
        return visibleChildren;
    };

    /**
     * 解析间距值
     * @private
     * @param {number|string} spacing - 间距值，可以是数字或百分比字符串
     * @param {number} parentSize - 父容器尺寸
     * @returns {number} 解析后的间距值
     */
    PIXI.Container.prototype._parseSpacing = function (spacing, parentSize) {
        // 检查spacing是否为undefined或null
        if (spacing === undefined || spacing === null) {
            return 0;
        }

        // 处理百分比字符串
        if (typeof spacing === 'string' && spacing.endsWith('%')) {
            const percentage = parseFloat(spacing) / 100;
            return parentSize * percentage;
        }

        // 处理数字或数字字符串
        const numericSpacing = Number(spacing);

        // 检查是否为有效数字
        if (isNaN(numericSpacing)) {
            console.warn(`[Layout Plugin] 无效的间距值: ${spacing}，使用默认值0`);
            return 0;
        }

        debugLog(`解析间距: ${spacing} -> ${numericSpacing}`);
        return numericSpacing;
    };

    /**
     * 解析内边距/外边距值
     * @private
     * @param {number|string|Object} padding - 内边距值
     * @returns {Object} 解析后的内边距对象 {top, left}
     */
    PIXI.Container.prototype._parsePadding = function (padding) {
        const result = { top: 0, left: 0 };

        if (padding === null || padding === undefined) {
            return result;
        }

        if (typeof padding === 'number') {
            result.top = result.left = padding;
        } else if (typeof padding === 'string' && padding.endsWith('%')) {
            const percentage = parseFloat(padding) / 100;
            // 百分比内边距需要在应用布局时根据实际尺寸计算
            result.top = result.left = percentage;
            result.isPercentage = true;
        } else if (typeof padding === 'object') {
            result.top = padding.top || 0;
            result.left = padding.left || 0;
        }

        return result;
    };

    /**
     * 计算实际内边距值
     * @private
     * @param {Object} padding - 内边距对象
     * @param {number} width - 容器宽度
     * @param {number} height - 容器高度
     * @returns {Object} 计算后的内边距对象
     */
    PIXI.Container.prototype._calculateActualPadding = function (padding, width, height) {
        const result = {};
        Object.assign(result, padding);

        if (padding.isPercentage) {
            result.top = height * padding.top;
            result.left = width * padding.left;
            delete result.isPercentage;
        }

        return result;
    };

    /**
     * 应用列布局（垂直排列子元素）
     * @private
     * @param {Object} options - 布局选项
     */
    PIXI.Container.prototype._applyColumnLayout = function (options) {
        debugLog('应用列布局，选项:', options);
        const children = this._getVisibleChildren();

        if (children.length === 0) {
            debugLog('没有可见子元素，不应用布局');
            return;
        }

        // 解析内边距
        const padding = this._calculateActualPadding(
            this._parsePadding(options.padding),
            this.width,
            this.height
        );

        // 计算可用空间
        const parentWidth = this.width - padding.left * 2;
        const parentHeight = this.height - padding.top * 2;

        debugLog('列布局容器信息:', {
            containerSize: `${this.width}x${this.height}`,
            padding: padding,
            parentSize: `${parentWidth}x${parentHeight}`
        });

        // 解析垂直间距
        let spacing = this._parseSpacing(options.verticalSpacing, parentHeight);

        // 计算子元素总高度和间距总高度
        let totalChildrenHeight = 0;
        for (const child of children) {
            totalChildrenHeight += child.height;
            debugLog(`子元素[${children.indexOf(child)}]:`, {
                size: `${child.width}x${child.height}`,
                position: `(${child.x}, ${child.y})`
            });
        }
        const totalSpacingHeight = spacing * (children.length - 1);

        // 计算起始Y位置（根据主轴对齐方式）
        let startY = padding.top;
        const availableSpace = parentHeight - totalChildrenHeight - totalSpacingHeight;

        debugLog('列布局计算:', {
            totalChildrenHeight,
            totalSpacingHeight,
            availableSpace,
            spacing,
            mainAxisAlignment: options.mainAxisAlignment
        });

        switch (options.mainAxisAlignment) {
            case 'center':
                startY += availableSpace / 2;
                break;
            case 'end':
                startY += availableSpace;
                break;
            case 'space-between':
                if (children.length > 1) {
                    // space-between: 第一个和最后一个元素贴边，中间元素平均分配空间
                    spacing = availableSpace / (children.length - 1);
                } else if (children.length === 1) {
                    // 只有一个元素时居中
                    startY += availableSpace / 2;
                }
                break;
            case 'space-around':
                if (children.length > 0) {
                    // space-around: 每个元素周围分配相等的空间
                    const spaceAround = availableSpace / children.length;
                    startY += spaceAround / 2;
                    spacing = spaceAround;
                }
                break;
            case 'space-evenly':
                if (children.length > 0) {
                    // space-evenly: 所有空间（包括边缘）平均分配
                    const spaceEvenly = availableSpace / (children.length + 1);
                    startY += spaceEvenly;
                    spacing = spaceEvenly;
                }
                break;
            // 'start' 是默认值，不需要额外处理
        }

        debugLog('应用列布局，起始Y位置:', startY);

        // 布局子元素
        let currentY = startY;
        for (const child of children) {
            // 保存原始尺寸
            const originalWidth = child.width;
            let x = padding.left;

            // 交叉轴对齐计算
            switch (options.crossAxisAlignment) {
                case 'center':
                    x = padding.left + (parentWidth - child.width) / 2;
                    break;
                case 'end':
                    x = padding.left + parentWidth - child.width;
                    break;
                case 'stretch':
                    // 保存原始比例
                    const widthRatio = child.scale.x;
                    child.width = parentWidth;
                    // 保持比例
                    child.scale.x = child.scale.y = widthRatio;
                    x = padding.left;
                    break;
                // 'start' 是默认值，不需要额外处理
            }

            // 设置位置
            const oldPosition = `(${child.x}, ${child.y})`;
            child.x = x;
            child.y = currentY;

            debugLog(`子元素[${children.indexOf(child)}] 位置变化:`, {
                from: oldPosition,
                to: `(${child.x}, ${child.y})`,
                currentY: currentY,
                spacing: spacing
            });

            // 更新下一个元素的Y位置
            currentY += child.height + spacing;

            // 如果是stretch模式且修改了宽度，恢复原始宽度
            if (options.crossAxisAlignment === 'stretch') {
                child.width = originalWidth;
            }
        }
    };

    /**
     * 应用行布局（水平排列子元素）
     * @private
     * @param {Object} options - 布局选项
     */
    PIXI.Container.prototype._applyRowLayout = function (options) {
        const children = this._getVisibleChildren();
        if (children.length === 0) return;

        // 解析内边距
        const padding = this._calculateActualPadding(
            this._parsePadding(options.padding),
            this.width,
            this.height
        );

        // 计算可用空间
        const parentWidth = this.width - padding.left * 2;
        const parentHeight = this.height - padding.top * 2;

        // 解析水平间距
        let spacing = this._parseSpacing(options.horizontalSpacing, parentWidth);

        // 计算子元素总宽度和间距总宽度
        let totalChildrenWidth = 0;
        for (const child of children) {
            totalChildrenWidth += child.width;
        }
        const totalSpacingWidth = spacing * (children.length - 1);

        // 计算起始X位置（根据主轴对齐方式）
        let startX = padding.left;
        const availableSpace = parentWidth - totalChildrenWidth - totalSpacingWidth;

        switch (options.mainAxisAlignment) {
            case 'center':
                startX += availableSpace / 2;
                break;
            case 'end':
                startX += availableSpace;
                break;
            case 'space-between':
                if (children.length > 1) {
                    // space-between: 第一个和最后一个元素贴边，中间元素平均分配空间
                    spacing = availableSpace / (children.length - 1);
                } else if (children.length === 1) {
                    // 只有一个元素时居中
                    startX += availableSpace / 2;
                }
                break;
            case 'space-around':
                if (children.length > 0) {
                    // space-around: 每个元素周围分配相等的空间
                    const spaceAround = availableSpace / children.length;
                    startX += spaceAround / 2;
                    spacing = spaceAround;
                }
                break;
            case 'space-evenly':
                if (children.length > 0) {
                    // space-evenly: 所有空间（包括边缘）平均分配
                    const spaceEvenly = availableSpace / (children.length + 1);
                    startX += spaceEvenly;
                    spacing = spaceEvenly;
                }
                break;
            // 'start' 是默认值，不需要额外处理
        }

        // 布局子元素
        let currentX = startX;
        for (const child of children) {
            // 保存原始尺寸
            const originalHeight = child.height;
            let y = padding.top;

            // 交叉轴对齐计算
            switch (options.crossAxisAlignment) {
                case 'center':
                    y = padding.top + (parentHeight - child.height) / 2;
                    break;
                case 'end':
                    y = padding.top + parentHeight - child.height;
                    break;
                case 'stretch':
                    // 保存原始比例
                    const heightRatio = child.scale.y;
                    child.height = parentHeight;
                    // 保持比例
                    child.scale.y = child.scale.x = heightRatio;
                    y = padding.top;
                    break;
                // 'start' 是默认值，不需要额外处理
            }

            // 设置位置
            child.x = currentX;
            child.y = y;

            // 更新下一个元素的X位置
            currentX += child.width + spacing;

            // 如果是stretch模式且修改了高度，恢复原始高度
            if (options.crossAxisAlignment === 'stretch') {
                child.height = originalHeight;
            }
        }
    };

    /**
     * 应用网格布局
     * @private
     * @param {Object} options - 布局选项
     */
    PIXI.Container.prototype._applyGridLayout = function (options) {
        const children = this._getVisibleChildren();
        if (children.length === 0) return;

        // 确保容器有有效尺寸
        const containerWidth = this.width || 400;
        const containerHeight = this.height || 400;

        if (containerWidth !== this.width || containerHeight !== this.height) {
            debugLog('容器尺寸无效，使用默认值:', containerWidth, 'x', containerHeight);
            this.width = containerWidth;
            this.height = containerHeight;
        }

        // 解析内边距
        const padding = this._calculateActualPadding(
            this._parsePadding(options.padding),
            containerWidth,
            containerHeight
        );

        // 计算可用空间
        const parentWidth = containerWidth - padding.left * 2;
        const parentHeight = containerHeight - padding.top * 2;

        // 获取列数和计算行数
        const columns = Math.max(1, options.columns || 2);
        const rows = Math.ceil(children.length / columns);

        // 解析间距
        let horizontalSpacing = this._parseSpacing(options.horizontalSpacing, parentWidth);
        let verticalSpacing = this._parseSpacing(options.verticalSpacing, parentHeight);

        // 限制间距，确保单元格尺寸为正数
        if (columns > 1) {
            const maxHorizontalSpacing = parentWidth / columns / 2;
            horizontalSpacing = Math.min(horizontalSpacing, maxHorizontalSpacing);
        }

        if (rows > 1) {
            const maxVerticalSpacing = parentHeight / rows / 2;
            verticalSpacing = Math.min(verticalSpacing, maxVerticalSpacing);
        }

        debugLog(`网格布局: ${columns}列${rows}行, 间距: ${horizontalSpacing}x${verticalSpacing}`);

        // 计算单元格尺寸
        const cellWidth = columns > 1
            ? Math.max(10, (parentWidth - horizontalSpacing * (columns - 1)) / columns)
            : parentWidth;

        const cellHeight = rows > 1
            ? Math.max(10, (parentHeight - verticalSpacing * (rows - 1)) / rows)
            : parentHeight;

        debugLog(`网格布局: 单元格尺寸=${cellWidth}x${cellHeight}`);

        // 布局子元素
        for (let i = 0; i < children.length; i++) {
            const child = children[i];

            // 确保子元素有有效尺寸
            if (!child.width || child.width <= 0) {
                child.width = cellWidth * 0.8;
                debugLog(`子元素[${i}] 宽度无效，设置为单元格宽度的80%`);
            }
            if (!child.height || child.height <= 0) {
                child.height = cellHeight * 0.8;
                debugLog(`子元素[${i}] 高度无效，设置为单元格高度的80%`);
            }

            // 计算行列索引
            const row = Math.floor(i / columns);
            const col = i % columns;

            // 计算单元格位置
            const cellX = padding.left + col * (cellWidth + horizontalSpacing);
            const cellY = padding.top + row * (cellHeight + verticalSpacing);

            // 根据crossAxisAlignment确定子元素在单元格中的位置
            let x = cellX;
            let y = cellY;

            switch (options.crossAxisAlignment) {
                case 'center':
                    x = cellX + (cellWidth - child.width) / 2;
                    y = cellY + (cellHeight - child.height) / 2;
                    break;
                case 'end':
                    x = cellX + cellWidth - child.width;
                    y = cellY + cellHeight - child.height;
                    break;
                case 'stretch':
                    // 计算缩放比例
                    const scaleRatio = Math.min(
                        cellWidth / child.width,
                        cellHeight / child.height
                    );

                    // 应用缩放
                    if (scaleRatio > 0 && isFinite(scaleRatio)) {
                        const scaledWidth = child.width * scaleRatio;
                        const scaledHeight = child.height * scaleRatio;
                        child.scale.x = child.scale.y = scaleRatio;
                        x = cellX + (cellWidth - scaledWidth) / 2;
                        y = cellY + (cellHeight - scaledHeight) / 2;
                    } else {
                        debugLog(`子元素[${i}] 缩放比例无效: ${scaleRatio}`);
                    }
                    break;
                // 'start' 是默认值，不需要额外处理
            }

            // 设置位置
            child.x = x;
            child.y = y;

            debugLog(`网格布局: 子元素[${i}] 位置=(${x}, ${y})`);
        }
    };

    /**
     * 应用自身布局的通用方法
     * @private
     * @param {string} alignment - 对齐方式
     */
    PIXI.Container.prototype._applySelfLayout = function (alignment) {
        if (!this.parent) {
            console.warn('[Layout Plugin] 对象没有父容器，无法应用自身布局');
            return;
        }

        const actualWidth = this.width;
        const actualHeight = this.height;
        const parentWidth = this.parent.width;
        const parentHeight = this.parent.height;

        // 获取锚点偏移
        const anchorOffsetX = this.anchor ? this.anchor.x * actualWidth : 0;
        const anchorOffsetY = this.anchor ? this.anchor.y * actualHeight : 0;

        let x = this._margin.left + anchorOffsetX;
        let y = this._margin.top + anchorOffsetY;

        // 根据对齐方式计算位置
        switch (alignment) {
            case 'topLeft':
                // 默认位置，不需要调整
                break;
            case 'topRight':
                x = parentWidth - actualWidth - this._margin.left + anchorOffsetX;
                break;
            case 'bottomLeft':
                y = parentHeight - actualHeight - this._margin.top + anchorOffsetY;
                break;
            case 'bottomRight':
                x = parentWidth - actualWidth - this._margin.left + anchorOffsetX;
                y = parentHeight - actualHeight - this._margin.top + anchorOffsetY;
                break;
            case 'topCenter':
                x = (parentWidth - actualWidth) / 2 + this._margin.left + anchorOffsetX;
                break;
            case 'bottomCenter':
                x = (parentWidth - actualWidth) / 2 + this._margin.left + anchorOffsetX;
                y = parentHeight - actualHeight - this._margin.top + anchorOffsetY;
                break;
            case 'leftCenter':
                y = (parentHeight - actualHeight) / 2 + this._margin.top + anchorOffsetY;
                break;
            case 'rightCenter':
                x = parentWidth - actualWidth - this._margin.left + anchorOffsetX;
                y = (parentHeight - actualHeight) / 2 + this._margin.top + anchorOffsetY;
                break;
            case 'center':
                x = (parentWidth - actualWidth) / 2 + this._margin.left + anchorOffsetX;
                y = (parentHeight - actualHeight) / 2 + this._margin.top + anchorOffsetY;
                break;
        }

        this.x = x;
        this.y = y;

        debugLog(`应用${alignment}布局: 位置=(${x}, ${y})`);
    };

    /**
     * 应用顶部左侧对齐布局
     * @private
     */
    PIXI.Container.prototype._applyTopLeftLayout = function () {
        this._applySelfLayout('topLeft');
    };

    /**
     * 应用顶部右侧对齐布局
     * @private
     */
    PIXI.Container.prototype._applyTopRightLayout = function () {
        this._applySelfLayout('topRight');
    };

    /**
     * 应用底部左侧对齐布局
     * @private
     */
    PIXI.Container.prototype._applyBottomLeftLayout = function () {
        this._applySelfLayout('bottomLeft');
    };

    /**
     * 应用底部右侧对齐布局
     * @private
     */
    PIXI.Container.prototype._applyBottomRightLayout = function () {
        this._applySelfLayout('bottomRight');
    };

    /**
     * 应用顶部居中对齐布局
     * @private
     */
    PIXI.Container.prototype._applyTopCenterLayout = function () {
        this._applySelfLayout('topCenter');
    };

    /**
     * 应用底部居中对齐布局
     * @private
     */
    PIXI.Container.prototype._applyBottomCenterLayout = function () {
        this._applySelfLayout('bottomCenter');
    };

    /**
     * 应用左侧居中对齐布局
     * @private
     */
    PIXI.Container.prototype._applyLeftCenterLayout = function () {
        this._applySelfLayout('leftCenter');
    };

    /**
     * 应用右侧居中对齐布局
     * @private
     */
    PIXI.Container.prototype._applyRightCenterLayout = function () {
        this._applySelfLayout('rightCenter');
    };

    /**
     * 应用居中对齐布局
     * @private
     */
    PIXI.Container.prototype._applyCenterLayout = function () {
        this._applySelfLayout('center');
    };

    // 完成插件初始化
})();
