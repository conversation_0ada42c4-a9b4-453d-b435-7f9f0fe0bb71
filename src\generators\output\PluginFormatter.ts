/**
 * 插件文件格式化器
 * 负责将生成的代码格式化为RPG Maker MZ插件格式
 */

import {
  GeneratedCode,
  SceneModificationCode,
  PrototypeModificationCode,
  CodeSnippet
} from '../core/types';
import { cleanCode, mergeCodeSnippets } from '../core/utils';

/**
 * 插件格式化器配置
 */
export interface PluginFormatterConfig {
  pluginName: string;
  pluginDescription: string;
  author: string;
  version: string;
  target: string;
  addHeader: boolean;
  addDebugMode: boolean;
  minify: boolean;
}

/**
 * 插件文件格式化器
 */
export class PluginFormatter {
  private config: PluginFormatterConfig;

  constructor(config: Partial<PluginFormatterConfig> = {}) {
    this.config = {
      pluginName: 'RPG Editor - 自动生成的修改插件',
      pluginDescription: '由RPG Editor自动生成的对象和原型修改插件',
      author: 'RPG Editor',
      version: '1.0.0',
      target: 'MZ',
      addHeader: true,
      addDebugMode: true,
      minify: false,
      ...config
    };
  }

  /**
   * 格式化为完整的插件文件内容
   */
  public formatAsPlugin(generatedCode: GeneratedCode): string {
    const sections: string[] = [];

    // 添加插件头部
    if (this.config.addHeader) {
      sections.push(this.generatePluginHeader());
    }

    // 添加立即执行函数包装
    sections.push('(function() {');
    sections.push('    "use strict";');
    sections.push('');

    // 添加调试模式
    if (this.config.addDebugMode) {
      sections.push(this.generateDebugMode());
      sections.push('');
    }

    // 添加工具函数
    sections.push(this.generateUtilityFunctions());
    sections.push('');

    // 添加原型修改
    if (generatedCode.prototypeModifications.length > 0) {
      sections.push(this.formatPrototypeModifications(generatedCode.prototypeModifications));
      sections.push('');
    }

    // 添加场景修改
    if (generatedCode.sceneModifications.length > 0) {
      sections.push(this.formatSceneModifications(generatedCode.sceneModifications));
    }

    // 结束立即执行函数
    sections.push('})();');

    let result = sections.join('\n');

    // 如果启用压缩，移除多余的空行和注释
    if (this.config.minify) {
      result = this.minifyCode(result);
    }

    return result;
  }

  /**
   * 生成插件头部注释
   */
  private generatePluginHeader(): string {
    const lines = [
      '/*:',
      ` * @target ${this.config.target}`,
      ` * @plugindesc ${this.config.pluginName}`,
      ` * <AUTHOR>
      ` * @version ${this.config.version}`,
      ' * @url ',
      ' * @help ',
      ' * ',
      ` * ${this.config.pluginDescription}`,
      ' * ',
      ' * 此插件由RPG Editor自动生成，包含以下修改：',
      ' * - 对象属性修改',
      ' * - 对象创建和删除',
      ' * - 类型原型修改',
      ' * ',
      ' * 注意：请不要手动编辑此文件，所有修改都会在下次生成时丢失。',
      ' * ',
      ' * @param debug',
      ' * @text 调试模式',
      ' * @desc 是否启用调试输出',
      ' * @type boolean',
      ' * @default false',
      ' * ',
      ' */'
    ];

    return lines.join('\n');
  }

  /**
   * 生成调试模式代码
   */
  private generateDebugMode(): string {
    return [
      '    // 获取插件参数',
      '    const parameters = PluginManager.parameters("RPGEditor_PrototypeModifications");',
      '    const DEBUG = parameters["debug"] === "true";',
      '',
      '    // 调试日志函数',
      '    function log(message, ...args) {',
      '        if (DEBUG) {',
      '            console.log("[RPGEditor]", message, ...args);',
      '        }',
      '    }'
    ].join('\n');
  }

  /**
   * 生成工具函数
   */
  private generateUtilityFunctions(): string {
    return [
      '    // 工具函数：根据场景路径查找对象',
      '    function findObjectByScenePath(scenePath) {',
      '        if (!scenePath || scenePath.length === 0) return null;',
      '',
      '        let current = SceneManager._scene;',
      '        if (!current) return null;',
      '',
      '        // 验证场景名称',
      '        const expectedSceneName = scenePath[0];',
      '        const actualSceneName = current.constructor.name;',
      '        if (actualSceneName !== expectedSceneName) {',
      '            if (DEBUG) log(`场景不匹配: 期望 ${expectedSceneName}, 实际 ${actualSceneName}`);',
      '            return null;',
      '        }',
      '',
      '        // 遍历路径',
      '        for (let i = 1; i < scenePath.length; i++) {',
      '            const index = parseInt(scenePath[i]);',
      '            if (isNaN(index) || !current.children || !current.children[index]) {',
      '                if (DEBUG) log(`路径中断: 索引 ${index} 在 ${current.constructor.name} 中不存在`);',
      '                return null;',
      '            }',
      '            current = current.children[index];',
      '        }',
      '',
      '        return current;',
      '    }'
    ].join('\n');
  }

  /**
   * 格式化原型修改代码
   */
  private formatPrototypeModifications(modifications: PrototypeModificationCode[]): string {
    if (modifications.length === 0) return '';

    const sections = [
      '    // ==================== 原型修改 ===================='
    ];

    modifications.forEach(classModification => {
      sections.push('');
      sections.push(`    // 修改 ${classModification.className} 类型`);
      
      if (classModification.modifications.length > 0) {
        sections.push(`    const original${classModification.className}Initialize = ${classModification.className}.prototype.initialize;`);
        sections.push(`    ${classModification.className}.prototype.initialize = function() {`);
        sections.push('        // 调用原始初始化方法');
        sections.push(`        original${classModification.className}Initialize.apply(this, arguments);`);
        sections.push('');
        sections.push(`        if (DEBUG) log('${classModification.className}.initialize 被调用');`);
        sections.push('');

        // 添加修改代码
        classModification.modifications.forEach(modification => {
          const indentedCode = this.indentCode(modification.code, 2);
          if (modification.description) {
            sections.push(`        // ${modification.description}`);
          }
          sections.push(indentedCode);
          sections.push('');
        });

        sections.push('    };');
      }
    });

    return sections.join('\n');
  }

  /**
   * 格式化场景修改代码
   */
  private formatSceneModifications(modifications: SceneModificationCode[]): string {
    if (modifications.length === 0) return '';

    const sections = [
      '    // ==================== 场景修改 ===================='
    ];

    modifications.forEach(sceneModification => {
      sections.push('');
      sections.push(`    // 修改 ${sceneModification.sceneName} 场景`);
      
      if (sceneModification.modifications.length > 0) {
        sections.push(`    const original${sceneModification.sceneName}Start = ${sceneModification.sceneName}.prototype.start;`);
        sections.push(`    ${sceneModification.sceneName}.prototype.start = function() {`);
        sections.push('        // 调用原始方法');
        sections.push(`        original${sceneModification.sceneName}Start.apply(this, arguments);`);
        sections.push('');
        sections.push(`        if (DEBUG) log('${sceneModification.sceneName}.start 被调用');`);
        sections.push('');

        // 添加修改代码
        sceneModification.modifications.forEach(modification => {
          const indentedCode = this.indentCode(modification.code, 2);
          if (modification.description) {
            sections.push(`        // ${modification.description}`);
          }
          sections.push(indentedCode);
          sections.push('');
        });

        sections.push('    };');
      }
    });

    return sections.join('\n');
  }

  /**
   * 添加代码缩进
   */
  private indentCode(code: string, level: number): string {
    const indent = '    '.repeat(level);
    return code.split('\n').map(line => 
      line.trim() ? indent + line : line
    ).join('\n');
  }

  /**
   * 压缩代码
   */
  private minifyCode(code: string): string {
    return code
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0 && !line.startsWith('//'))
      .join('\n')
      .replace(/\n\s*\n/g, '\n'); // 移除多余的空行
  }

  /**
   * 生成文件名
   */
  public generateFileName(projectName?: string): string {
    const baseName = projectName ? 
      `RPGEditor_${projectName}_PrototypeModifications` : 
      'RPGEditor_PrototypeModifications';
    return `${baseName}.js`;
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<PluginFormatterConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取当前配置
   */
  public getConfig(): PluginFormatterConfig {
    return { ...this.config };
  }
}
