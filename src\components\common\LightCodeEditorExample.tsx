import React, { useState } from 'react';
import { Box, Button, Paper, Typography } from '@mui/material';
import CodeIcon from '@mui/icons-material/Code';
import LightCodeEditor from './LightCodeEditor';

const LightCodeEditorExample: React.FC = () => {
  // 示例代码
  const [code, setCode] = useState(`// 这是一个示例JavaScript函数
function greet(name) {
  console.log("Hello, " + name + "!");
  return "Greeting sent to " + name;
}

// 调用函数
const result = greet("World");
console.log(result);
`);

  // 编辑器对话框状态
  const [editorOpen, setEditorOpen] = useState(false);

  // 打开编辑器
  const handleOpenEditor = () => {
    setEditorOpen(true);
  };

  // 关闭编辑器
  const handleCloseEditor = () => {
    setEditorOpen(false);
  };

  // 保存代码
  const handleSaveCode = (newCode: string) => {
    setCode(newCode);
    console.log('保存的代码:', newCode);
  };

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h5" gutterBottom>
        轻量级代码编辑器示例
      </Typography>

      <Button
        variant="contained"
        color="primary"
        startIcon={<CodeIcon />}
        onClick={handleOpenEditor}
        sx={{ mb: 2 }}
      >
        打开代码编辑器
      </Button>

      <Paper
        variant="outlined"
        sx={{
          p: 2,
          bgcolor: '#f5f5f5',
          fontFamily: 'monospace',
          whiteSpace: 'pre-wrap',
          overflow: 'auto',
          maxHeight: 300
        }}
      >
        {code}
      </Paper>

      {/* 代码编辑器对话框 */}
      <LightCodeEditor
        open={editorOpen}
        title="编辑JavaScript代码"
        code={code}
        language="javascript"
        onClose={handleCloseEditor}
        onSave={handleSaveCode}
      />
    </Box>
  );
};

export default LightCodeEditorExample;
