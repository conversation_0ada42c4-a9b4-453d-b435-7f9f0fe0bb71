// 重新导出主要功能
pub use crate::save::modification_manager::record_property_modification;

use crate::save::modification_manager::{get_current_modifications, merge_modifications};
use crate::save::plugin_generator::{generate_empty_plugin, generate_plugin_code};
use crate::save::plugin_parser::parse_existing_modifications;
use crate::utils::{cache, logging, path, project_state};
use std::fs;
use tauri::AppHandle;

/// 保存所有修改的主入口函数
#[tauri::command]
pub fn save_all_modifications(app_handle: AppHandle) -> Result<String, String> {
    logging::log_info("SaveManager", "开始保存所有修改");

    // 获取项目名称
    let project_name = project_state::get_current_project_name(&app_handle)?;

    // 获取当前修改列表
    let modifications = get_current_modifications(&app_handle)?;
    logging::log_info(
        "SaveManager",
        &format!("获取到 {} 个修改", modifications.len()),
    );

    // 如果修改列表为空，直接返回
    if modifications.is_empty() {
        logging::log_info("SaveManager", "没有修改需要保存");
        return Ok("没有修改需要保存".to_string());
    }

    // 打印修改详情用于调试
    for (i, modification) in modifications.iter().enumerate() {
        logging::log_debug(
            "SaveManager",
            &format!(
                "修改 {}: 路径={:?}, 类名={}, 属性数量={}",
                i,
                modification.scene_path,
                modification.class_name,
                modification.properties.len()
            ),
        );
        for (key, value) in &modification.properties {
            logging::log_debug("SaveManager", &format!("  属性: {} = {}", key, value));
        }
    }

    // 获取临时插件目录
    let temp_plugins_dir =
        crate::temp_plugins::get_project_temp_plugins_dir(&app_handle, &project_name)?;

    // 构建临时插件文件路径（带项目名称前缀）
    let plugin_file_name = format!("RPGEditor_{}_PrototypeModifications.js", project_name);
    let plugin_file_path = temp_plugins_dir.join(&plugin_file_name);

    println!("临时插件文件路径: {:?}", plugin_file_path);

    // 读取现有文件或创建新文件
    let existing_content = if plugin_file_path.exists() {
        fs::read_to_string(&plugin_file_path)
            .map_err(|e| format!("Failed to read plugin file: {}", e))?
    } else {
        // 检查项目目录中是否存在插件文件
        let project_plugins_dir = path::get_project_plugins_dir(&project_name)?;
        let project_plugin_path = project_plugins_dir.join("RPGEditor_PrototypeModifications.js");

        if project_plugin_path.exists() {
            // 如果项目目录中存在插件文件，读取它
            fs::read_to_string(&project_plugin_path)
                .map_err(|e| format!("Failed to read project plugin file: {}", e))?
        } else {
            // 如果文件不存在，创建一个空的插件模板
            generate_empty_plugin()
        }
    };

    // 直接使用当前修改，不进行文件解析和合并
    // 这样可以避免因为解析器问题导致的属性丢失
    logging::log_info("SaveManager", "直接使用当前修改列表，跳过文件解析和合并");

    // 更新缓存
    cache::update_cached_modifications(modifications.clone())?;

    // 生成新的插件代码
    let new_content = generate_plugin_code(&app_handle, &modifications);

    // 确保目标目录存在
    if let Some(parent) = plugin_file_path.parent() {
        fs::create_dir_all(parent).map_err(|e| format!("Failed to create directory: {}", e))?;
    }

    // 写入临时文件
    fs::write(&plugin_file_path, &new_content)
        .map_err(|e| format!("Failed to write plugin file: {}", e))?;

    println!(
        "原型修改已成功保存到临时目录，共 {} 个修改",
        modifications.len()
    );

    Ok(format!("成功保存 {} 个修改", modifications.len()))
}
