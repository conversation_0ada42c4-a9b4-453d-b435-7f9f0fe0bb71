/**
 * 统一的属性管理器
 * 根据属性类型自动选择合适的处理器
 */

import {
  isBasicProperty,
  updateBasicProperty
} from './backendServer/basicProperty';

import {
  isBitmapProperty,
  updateBitmapProperty
} from './backendServer/bitmap';

import {
  isSpriteColorProperty,
  updateSpriteColorProperty,
  validateSpriteColorPropertyValue,
  getSpriteColorPropertyValue
} from './backendServer/spriteColor';

/**
 * 属性类型枚举
 */
export enum PropertyType {
  BASIC = 'basic',
  BITMAP = 'bitmap',
  SPRITE_COLOR = 'spriteColor',
  FILTER = 'filter',
  UNKNOWN = 'unknown'
}

/**
 * 属性修改结果
 */
export interface PropertyUpdateResult {
  success: boolean;
  propertyType: PropertyType;
  errorMessage?: string;
}

/**
 * 统一的属性管理器类
 */
export class PropertyManager {
  /**
   * 检测属性类型
   * @param propertyName 属性名称
   * @returns 属性类型
   */
  static detectPropertyType(propertyName: string): PropertyType {
    if (isBasicProperty(propertyName)) {
      return PropertyType.BASIC;
    }

    if (isBitmapProperty(propertyName)) {
      return PropertyType.BITMAP;
    }

    if (isSpriteColorProperty(propertyName)) {
      return PropertyType.SPRITE_COLOR;
    }

    // 滤镜属性通常通过专门的方法处理，不在这里检测

    return PropertyType.UNKNOWN;
  }

  /**
   * 统一的属性更新方法
   * @param objects 要修改的对象数组
   * @param propertyName 属性名称
   * @param value 新值
   * @returns 更新结果
   */
  static async updateProperty(
    objects: any[],
    propertyName: string,
    value: any
  ): Promise<PropertyUpdateResult> {
    if (!objects || objects.length === 0) {
      return {
        success: false,
        propertyType: PropertyType.UNKNOWN,
        errorMessage: '对象数组为空'
      };
    }

    const propertyType = this.detectPropertyType(propertyName);
    console.log(`PropertyManager: 检测到属性类型 ${propertyName} -> ${propertyType}`);

    try {
      let updateSuccess = false;
      let recordSuccess = false;

      // 1. 更新前端对象属性
      switch (propertyType) {
        case PropertyType.BASIC:
          updateSuccess = updateBasicProperty(objects, propertyName, value);
          break;

        case PropertyType.BITMAP:
          updateSuccess = updateBitmapProperty(objects, propertyName, value);
          break;

        case PropertyType.SPRITE_COLOR:
          updateSuccess = updateSpriteColorProperty(objects, propertyName, value);
          break;

        default:
          console.warn(`PropertyManager: 未知的属性类型 ${propertyType}，使用基础属性处理`);
          // 对于未知类型，使用基础属性处理器作为后备
          updateSuccess = updateBasicProperty(objects, propertyName, value);
          break;
      }

      if (!updateSuccess) {
        return {
          success: false,
          propertyType,
          errorMessage: '前端对象属性更新失败'
        };
      }

      // 2. 记录到后端
      try {
        // 使用统一的后端记录方法
        const { recordPropertyModification } = await import('./backendServer/BackendService');
        await recordPropertyModification(objects[0], propertyName, value);
        recordSuccess = true;
        console.log(`PropertyManager: 属性 ${propertyName} (类型: ${propertyType}) 已成功记录到后端`);
      } catch (error) {
        console.error(`PropertyManager: 记录属性到后端失败:`, error);
        // 后端记录失败不影响前端更新的成功
        recordSuccess = false;
      }

      return {
        success: true,
        propertyType,
        errorMessage: recordSuccess ? undefined : '后端记录失败，但前端更新成功'
      };

    } catch (error) {
      console.error(`PropertyManager: 更新属性 ${propertyName} 失败:`, error);
      return {
        success: false,
        propertyType,
        errorMessage: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 批量更新属性
   * @param objects 要修改的对象数组
   * @param properties 属性键值对
   * @returns 更新结果数组
   */
  static async updateMultipleProperties(
    objects: any[],
    properties: Record<string, any>
  ): Promise<PropertyUpdateResult[]> {
    const results: PropertyUpdateResult[] = [];

    for (const [propertyName, value] of Object.entries(properties)) {
      const result = await this.updateProperty(objects, propertyName, value);
      results.push(result);
    }

    return results;
  }

  /**
   * 验证属性值
   * @param objects 对象数组
   * @param propertyName 属性名称
   * @param value 新值
   * @returns 验证结果
   */
  static validateProperty(
    objects: any[],
    propertyName: string,
    value: any
  ): { isValid: boolean; errorMessage?: string } {
    if (!objects || objects.length === 0) {
      return { isValid: false, errorMessage: '对象数组为空' };
    }

    const propertyType = this.detectPropertyType(propertyName);

    switch (propertyType) {
      case PropertyType.SPRITE_COLOR:
        return validateSpriteColorPropertyValue(propertyName, value);

      case PropertyType.BASIC:
      case PropertyType.BITMAP:
        // 基础验证：检查属性是否存在
        const firstObject = objects[0];
        if (propertyName.includes('.')) {
          // 嵌套属性的验证
          const keys = propertyName.split('.');
          let current = firstObject;
          for (let i = 0; i < keys.length - 1; i++) {
            if (!current || typeof current !== 'object') {
              return { isValid: false, errorMessage: `嵌套属性路径无效: ${propertyName}` };
            }
            current = current[keys[i]];
          }
        }
        return { isValid: true };

      default:
        return { isValid: true }; // 未知类型默认通过验证
    }
  }

  /**
   * 获取属性的当前值
   * @param object 对象
   * @param propertyName 属性名称
   * @returns 当前值
   */
  static getPropertyValue(object: any, propertyName: string): any {
    if (!object) return undefined;

    const propertyType = this.detectPropertyType(propertyName);

    switch (propertyType) {
      case PropertyType.SPRITE_COLOR:
        return getSpriteColorPropertyValue(object, propertyName);

      default:
        // 通用的属性获取
        if (propertyName.includes('.')) {
          const keys = propertyName.split('.');
          let current = object;
          for (const key of keys) {
            if (current === null || current === undefined) {
              return undefined;
            }
            current = current[key];
          }
          return current;
        } else {
          return object[propertyName];
        }
    }
  }

  /**
   * 获取支持的属性类型统计
   * @param objects 对象数组
   * @returns 属性类型统计
   */
  static getPropertyTypeStats(objects: any[]): Record<PropertyType, number> {
    const stats: Record<PropertyType, number> = {
      [PropertyType.BASIC]: 0,
      [PropertyType.BITMAP]: 0,
      [PropertyType.SPRITE_COLOR]: 0,
      [PropertyType.FILTER]: 0,
      [PropertyType.UNKNOWN]: 0
    };

    // 这里可以根据对象类型和属性来统计支持的属性类型
    // 暂时返回基础统计
    stats[PropertyType.BASIC] = objects.length;

    return stats;
  }
}
