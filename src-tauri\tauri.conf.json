{"$schema": "https://schema.tauri.app/config/2", "productName": "rpgeditor", "version": "0.1.0", "identifier": "com.rpgeditor.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1430", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "rpgeditor", "width": 800, "height": 600}], "security": {"csp": null, "assetProtocol": {"enable": true, "scope": ["$RESOURCE/*", "$PUBLIC/*"]}}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}