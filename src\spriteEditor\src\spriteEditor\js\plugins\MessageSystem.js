//=============================================================================
// RPG Maker MZ - Message System
//=============================================================================

/*:
 * @target MZ
 * @plugindesc 消息提示系统
 * <AUTHOR> AI
 *
 * @param fontSize
 * @text 字体大小
 * @type number
 * @min 10
 * @max 72
 * @default 16
 * @desc 消息文字的字体大小
 *
 * @param fontColor
 * @text 字体颜色
 * @type string
 * @default #ffffff
 * @desc 消息文字的颜色（CSS颜色格式）
 *
 * @param backgroundColor
 * @text 背景颜色
 * @type string
 * @default rgba(0, 0, 0, 0.7)
 * @desc 消息窗口背景的颜色（CSS颜色格式）
 *
 * @param windowWidth
 * @text 窗口宽度
 * @type number
 * @min 100
 * @max 1000
 * @default 400
 * @desc 消息窗口的宽度（像素）
 *
 * @param windowHeight
 * @text 窗口高度
 * @type number
 * @min 50
 * @max 500
 * @default 80
 * @desc 消息窗口的高度（像素）
 *
 * @param padding
 * @text 内边距
 * @type number
 * @min 0
 * @max 100
 * @default 12
 * @desc 消息窗口的内边距（像素）
 *
 * @param borderSize
 * @text 边框大小
 * @type number
 * @min 0
 * @max 10
 * @default 2
 * @desc 消息窗口边框的大小（像素）
 *
 * @param borderColor
 * @text 边框颜色
 * @type string
 * @default #ffffff
 * @desc 消息窗口边框的颜色（CSS颜色格式）
 *
 * @param displayDuration
 * @text 显示时长
 * @type number
 * @min 10
 * @max 9999
 * @default 180
 * @desc 消息显示的持续时间（帧数）
 *
 * @param position
 * @text 显示位置
 * @type select
 * @option 顶部
 * @value top
 * @option 中间
 * @value center
 * @option 底部
 * @value bottom
 * @default top
 * @desc 消息窗口在屏幕上的显示位置
 *
 * @help MessageSystem.js
 *
 * 这个插件提供了一个可扩展的消息提示系统，用于显示各种类型的提示信息：
 * 1. 网络状态提示
 * 2. 错误信息提示
 * 3. 系统通知提示
 * 4. 支持自定义消息类型和回调函数
 *
 * 插件命令：
 * 无需插件命令，通过脚本调用即可：
 *
 * 基本用法：
 * MessageSystem.show("这是一条消息");
 *
 * 完整用法：
 * MessageSystem.show({
 *   text: "这是一条消息",
 *   type: "error",  // 消息类型：info, error, warning, success
 *   duration: 180,   // 持续时间（帧数）
 *   callback: () => { // 消息关闭后的回调函数
 *     console.log("消息已关闭");
 *   }
 * });
 */

(() => {
  const pluginName = "MessageSystem";
  const parameters = PluginManager.parameters(pluginName);

  const fontSize = Number(parameters.fontSize || 16);
  const fontColor = String(parameters.fontColor || "#ffffff");
  const backgroundColor = String(
    parameters.backgroundColor || "rgba(0, 0, 0, 0.7)"
  );
  const windowWidth = Number(parameters.windowWidth || 400);
  const windowHeight = Number(parameters.windowHeight || 80);
  const padding = Number(parameters.padding || 12);
  const borderSize = Number(parameters.borderSize || 2);
  const borderColor = String(parameters.borderColor || "#ffffff");
  const defaultDuration = Number(parameters.displayDuration || 180);
  const position = String(parameters.position || "top");

  //-----------------------------------------------------------------------------
  // MessageSystem
  //
  // 消息系统管理器

  window.MessageSystem = {
    _queue: [],
    _currentMessage: null,
    _messageWindow: null,

    initialize() {
      this._queue = [];
      this._currentMessage = null;
      this._messageWindow = null;
    },

    show(messageData) {
      if (typeof messageData === "string") {
        messageData = { text: messageData };
      }

      const message = {
        text: messageData.text,
        type: messageData.type || "info",
        duration: messageData.duration || defaultDuration,
        callback: messageData.callback,
        remainingTime: messageData.duration || defaultDuration,
      };

      // 检查队列中是否存在相同内容的消息
      const existingMessage = this._queue.find((m) => m.text === message.text);
      if (existingMessage) {
        // 如果存在相同消息，重置其显示时间并更新显示
        existingMessage.remainingTime = message.duration;
        if (this._messageWindow) {
          this._messageWindow.show(existingMessage);
        }
        return;
      }

      // 如果当前显示的消息与新消息相同，重置其显示时间并更新显示
      if (this._currentMessage && this._currentMessage.text === message.text) {
        this._currentMessage.remainingTime = message.duration;
        if (this._messageWindow) {
          this._messageWindow.show(this._currentMessage);
        }
        return;
      }

      // 如果是新消息，则添加到队列
      this._queue.push(message);
      this._processQueue();
    },

    update() {
      if (this._currentMessage) {
        this._currentMessage.remainingTime--;
        if (this._currentMessage.remainingTime <= 0) {
          this._hideCurrentMessage();
        }
      } else {
        this._processQueue();
      }
    },

    _processQueue() {
      if (!this._currentMessage && this._queue.length > 0) {
        this._currentMessage = this._queue.shift();
        this._showMessage(this._currentMessage);
      }
    },

    _showMessage(message) {
      if (!this._messageWindow || !this._messageWindow.parent) {
        this._createMessageWindow();
      }
      if (this._messageWindow) {
        this._messageWindow.show(message);
      }
    },

    _hideCurrentMessage() {
      if (this._messageWindow) {
        this._messageWindow.hide();
      }
      if (this._currentMessage && this._currentMessage.callback) {
        this._currentMessage.callback();
      }
      this._currentMessage = null;
      // 立即处理队列中的下一条消息
      this._processQueue();
    },

    _createMessageWindow() {
      const scene = SceneManager._scene;
      if (scene) {
        if (this._messageWindow) {
          if (this._messageWindow.parent) {
            this._messageWindow.parent.removeChild(this._messageWindow);
          }
          this._messageWindow.destroy();
          this._messageWindow = null;
        }
        this._messageWindow = new Window_SystemMessage();
        scene.addChild(this._messageWindow);
        this._messageWindow.z = 9999;
      }
    },

    clear() {
      this._queue = [];
      if (this._currentMessage) {
        this._hideCurrentMessage();
      }
    },
  };
  //-----------------------------------------------------------------------------
  // Window_SystemMessage
  //
  // 系统消息窗口

  function Window_SystemMessage() {
    this.initialize(...arguments);
  }

  Window_SystemMessage.prototype = Object.create(Window_Base.prototype);
  Window_SystemMessage.prototype.constructor = Window_SystemMessage;

  Window_SystemMessage.prototype.initialize = function () {
    let x = (Graphics.boxWidth - windowWidth) / 2;
    let y;

    switch (position) {
      case "top":
        y = 48;
        break;
      case "center":
        y = (Graphics.boxHeight - windowHeight) / 2;
        break;
      case "bottom":
        y = Graphics.boxHeight - windowHeight - 48;
        break;
      default:
        y = 48;
    }

    Window_Base.prototype.initialize.call(
      this,
      new Rectangle(x, y, windowWidth, windowHeight)
    );
    this.opacity = 255;
    this.contentsOpacity = 0;
    this.padding = padding;
    this._borderSize = borderSize;
    this._borderColor = borderColor;

    // 添加点击事件监听
    this._clickHandler = this._onWindowClick.bind(this);
    this._setupClickHandler();

    this.show();
  };

  Window_SystemMessage.prototype.show = function (message) {
    if (!message || typeof message !== "object") return;
    this.contents.clear();
    this.resetFontSettings();
    this.contents.fontSize = fontSize;
    this.changeTextColor(this._getColorByType(message.type || "info"));
    this.drawBackground(0, 0, this.innerWidth, this.innerHeight);
    this.drawText(message.text || "", 0, 0, this.innerWidth, "center");
    this._showCount = message.duration || defaultDuration;
    this.appear();
  };

  Window_SystemMessage.prototype.appear = function () {
    Window_Base.prototype.show.call(this);
    this.opacity = 255;
    this.contentsOpacity = 0;
    this._fadeInDuration = 16;
    this._fadeOutDuration = 16;
  };

  Window_SystemMessage.prototype.update = function () {
    Window_Base.prototype.update.call(this);
    if (this._showCount > 0) {
      this._showCount--;
      if (this._fadeInDuration > 0) {
        this.contentsOpacity += 255 / this._fadeInDuration;
      }
      if (this._showCount === 0) {
        this._fadeOutDuration = 16;
      }
    } else if (this._fadeOutDuration > 0) {
      this._fadeOutDuration--;
      this.contentsOpacity -= 255 / this._fadeOutDuration;
      if (this._fadeOutDuration === 0) {
        this.hide();
      }
    }
  };

  Window_SystemMessage.prototype.hide = function () {
    Window_Base.prototype.hide.call(this);
    this.opacity = 0;
    this.contentsOpacity = 0;
    this._showCount = 0;
    this._fadeInDuration = 0;
    this._fadeOutDuration = 0;
    // 移除点击事件监听
    this._removeClickHandler();
  };

  Window_SystemMessage.prototype._setupClickHandler = function () {
    if (this._clickHandler && !this._hasClickListener) {
      document.addEventListener("mousedown", this._clickHandler);
      this._hasClickListener = true;
    }
  };

  Window_SystemMessage.prototype._removeClickHandler = function () {
    if (this._clickHandler && this._hasClickListener) {
      document.removeEventListener("mousedown", this._clickHandler);
      this._hasClickListener = false;
    }
  };

  Window_SystemMessage.prototype._onWindowClick = function () {
    if (this.visible && this.contentsOpacity > 0) {
      this._showCount = 0; // 立即结束显示
      this._fadeOutDuration = 8; // 加快淡出速度
      MessageSystem._hideCurrentMessage();
    }
  };

  Window_SystemMessage.prototype.drawBackground = function (
    x,
    y,
    width,
    height
  ) {
    // 绘制边框
    if (this._borderSize > 0) {
      this.contents.strokeStyle = this._borderColor;
      this.contents.lineWidth = this._borderSize;
      this.contents.strokeRect(x, y, width, height);
    }

    // 绘制背景
    const color = this._parseColor(backgroundColor);
    this.contents.fillRect(
      x + this._borderSize,
      y + this._borderSize,
      width - this._borderSize * 2,
      height - this._borderSize * 2,
      color
    );
  };

  Window_SystemMessage.prototype._getColorByType = function (type) {
    switch (type) {
      case "error":
        return "#ff4444";
      case "warning":
        return "#ffbb33";
      case "success":
        return "#00C851";
      default:
        return fontColor;
    }
  };

  Window_SystemMessage.prototype._parseColor = function (color) {
    return color;
  };

  Window_SystemMessage.prototype._onWindowClick = function () {
    if (this.visible && this.contentsOpacity > 0) {
      this._showCount = 0; // 立即结束显示
      this._fadeOutDuration = 8; // 加快淡出速度
      MessageSystem._hideCurrentMessage();
    }
  };

  //-----------------------------------------------------------------------------
  // Scene_Base
  //
  // 扩展场景基类以支持消息系统更新

  const _Scene_Base_update = Scene_Base.prototype.update;
  Scene_Base.prototype.update = function () {
    _Scene_Base_update.call(this);
    if (MessageSystem) {
      MessageSystem.update();
    }
  };

  //-----------------------------------------------------------------------------
  // 初始化消息系统

  const _Scene_Boot_start = Scene_Boot.prototype.start;
  Scene_Boot.prototype.start = function () {
    _Scene_Boot_start.call(this);
    MessageSystem.initialize();
  };
})();
