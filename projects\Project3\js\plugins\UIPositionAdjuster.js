/*:
 * @target MZ
 * @plugindesc UI位置调整插件
 * <AUTHOR>
 *
 * @param defaultPositions
 * @text 默认位置设置
 * @type struct<Position>[]
 * @desc 设置各个UI元素的默认位置
 * @default []
 *
 * @help
 * 这个插件允许你调整游戏中各种UI元素的位置。
 *
 * 使用方法：
 * 1. 在插件参数中设置各个UI元素的位置
 * 2. 游戏运行时会自动应用这些设置
 *
 * 版本: 1.0.0
 */

/*~struct~Position:
 * @param targetClass
 * @text 目标类名
 * @type select
 * @option Sprite_Button
 * @option Sprite_Character
 * @option Sprite_Battler
 * @desc 要调整位置的UI类
 *
 * @param targetId
 * @text 目标ID
 * @type select
 * @option 取消按钮
 * @value cancel
 * @option 确定按钮
 * @value ok
 * @option 菜单按钮
 * @value menu
 * @option 上一页按钮
 * @value pageup
 * @option 下一页按钮
 * @value pagedown
 * @option 向上按钮
 * @value up
 * @option 向下按钮
 * @value down
 * @option 向上按钮2
 * @value up2
 * @option 向下按钮2
 * @value down2
 * @desc 选择要调整位置的UI元素
 *
 * @param x
 * @text X坐标
 * @type number
 * @min -9999
 * @max 9999
 * @desc X坐标位置
 *
 * @param y
 * @text Y坐标
 * @type number
 * @min -9999
 * @max 9999
 * @desc Y坐标位置
 */

(() => {
  "use strict";

  const pluginName = "UIPositionAdjuster";

  // 获取插件参数
  const parameters = PluginManager.parameters(pluginName);
  const defaultPositions = JSON.parse(parameters.defaultPositions || "[]").map(
    (position) => {
      return JSON.parse(position);
    }
  );

  // 保存原始的初始化函数
  const _Sprite_Clickable_initialize = Sprite_Clickable.prototype.initialize;

  // 扩展Sprite_Clickable的initialize函数
  Sprite_Clickable.prototype.initialize = function () {
    _Sprite_Clickable_initialize.call(this);
    this.applyPositionAdjustment();
  };

  // 添加位置调整函数
  Sprite_Clickable.prototype.applyPositionAdjustment = function () {
    const className = this.constructor.name;
    const targetId = this._targetId || "";
    const position = defaultPositions.find(
      (pos) =>
        pos.targetClass === className &&
        (!pos.targetId || pos.targetId === targetId)
    );

    if (position) {
      this.x = Number(position.x);
      // 保持原有的y坐标
      if (position.y !== undefined) {
        this.y = Number(position.y);
      }
    }
  };

  // 扩展更新函数以保持位置
  const _Sprite_Clickable_update = Sprite_Clickable.prototype.update;
  Sprite_Clickable.prototype.update = function () {
    _Sprite_Clickable_update.call(this);
    this.maintainPosition();
  };

  // 添加位置维护函数
  Sprite_Clickable.prototype.maintainPosition = function () {
    const className = this.constructor.name;
    const targetId = this._targetId || "";
    const position = defaultPositions.find(
      (pos) =>
        pos.targetClass === className &&
        (!pos.targetId || pos.targetId === targetId)
    );

    if (position) {
      // 只在位置发生变化时更新
      if (this.x !== Number(position.x)) {
        this.x = Number(position.x);
      }
      // 只有当y坐标被明确设置时才更新
      if (position.y !== undefined && this.y !== Number(position.y)) {
        this.y = Number(position.y);
      }
    }
  };
})();
