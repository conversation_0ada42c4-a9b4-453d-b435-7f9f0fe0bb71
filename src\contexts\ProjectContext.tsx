import { createContext, useContext, ReactNode } from 'react';
import useProjectStore from '../store/Store';

// 创建项目上下文
const ProjectContext = createContext<ReturnType<typeof useProjectStore> | null>(null);

// 项目提供者组件
interface ProjectProviderProps {
  children: ReactNode;
}

export const ProjectProvider = ({ children }: ProjectProviderProps) => {
  // 使用 Zustand store
  const projectStore = useProjectStore();
  
  return (
    <ProjectContext.Provider value={projectStore}>
      {children}
    </ProjectContext.Provider>
  );
};

// 自定义 hook 用于访问项目上下文
export const useProject = () => {
  const context = useContext(ProjectContext);
  if (context === null) {
    throw new Error('useProject must be used within a ProjectProvider');
  }
  return context;
};
