import React, { useState } from 'react';
import { Box, Tabs, Tab } from '@mui/material';
import PreviewArea from './preview/PreviewArea';
import GameEvents from './GameEvents';
import GameData from './GameData';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      id={`center-tabpanel-${index}`}
      aria-labelledby={`center-tab-${index}`}
      style={{
        height: 'calc(100% - 48px)',
        overflow: 'auto',
        display: value === index ? 'block' : 'none',
        visibility: value === index ? 'visible' : 'hidden',
        position: 'relative'
      }}
      {...other}
    >
      <Box sx={{ height: '100%' }}>
        {children}
      </Box>
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `center-tab-${index}`,
    'aria-controls': `center-tabpanel-${index}`,
  };
}

const CenterPanel: React.FC = () => {
  const [value, setValue] = useState(0);

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box sx={{
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={value}
          onChange={handleChange}
          aria-label="center panel tabs"
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label="预览" {...a11yProps(0)} />
          <Tab label="游戏事件" {...a11yProps(1)} />
          <Tab label="游戏数据" {...a11yProps(2)} />
        </Tabs>
      </Box>
      <TabPanel value={value} index={0}>
        <PreviewArea />
      </TabPanel>
      <TabPanel value={value} index={1}>
        <GameEvents />
      </TabPanel>
      <TabPanel value={value} index={2}>
        <GameData />
      </TabPanel>
    </Box>
  );
};

export default CenterPanel;
