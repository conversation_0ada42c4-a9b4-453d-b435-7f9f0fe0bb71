use serde_json;
use std::collections::HashMap;

/// 生成滤镜相关代码
pub fn generate_filter_code(
    var_name: &str,
    properties: &HashMap<String, serde_json::Value>,
) -> String {
    let mut code = String::new();

    code.push_str(&format!("        // 处理滤镜相关修改\n"));

    // 分离滤镜操作和滤镜参数修改
    let mut filter_operations = Vec::new();
    let mut filter_param_modifications = Vec::new();
    let mut direct_filters_assignment = None;

    for (key, value) in properties {
        if key.contains("filters_operation") {
            filter_operations.push((key, value));
        } else if key.starts_with("filters[") && key.contains("].") {
            filter_param_modifications.push((key, value));
        } else if key == "filters" {
            direct_filters_assignment = Some(value);
        }
    }

    // 如果有直接的 filters 数组赋值，跳过它（这通常是错误的序列化）
    if direct_filters_assignment.is_some() {
        code.push_str(&format!(
            "        // 跳过直接的 filters 数组赋值（应该使用滤镜操作）\n"
        ));
        code.push_str(&format!(
            "        if (DEBUG) log('警告: 检测到直接的 filters 数组赋值，这可能导致问题');\n"
        ));
    }

    // 处理滤镜操作
    if !filter_operations.is_empty() {
        code.push_str(&generate_filter_operations_code(
            var_name,
            &filter_operations,
        ));
    }

    // 处理滤镜参数修改
    if !filter_param_modifications.is_empty() {
        code.push_str(&generate_filter_param_modifications_code(
            var_name,
            &filter_param_modifications,
        ));
    }

    code
}

/// 生成滤镜操作代码（添加、删除、重排）
fn generate_filter_operations_code(
    var_name: &str,
    operations: &[(&String, &serde_json::Value)],
) -> String {
    let mut code = String::new();

    code.push_str(&format!("        // 执行滤镜操作\n"));

    for (key, value) in operations {
        if let Ok(operation) =
            serde_json::from_value::<serde_json::Map<String, serde_json::Value>>((*value).clone())
        {
            if let Some(op_type) = operation.get("operation").and_then(|v| v.as_str()) {
                match op_type {
                    "add" => {
                        code.push_str(&generate_add_filter_code(var_name, &operation));
                    }
                    "remove" => {
                        code.push_str(&generate_remove_filter_code(var_name, &operation));
                    }
                    "reorder" => {
                        code.push_str(&generate_reorder_filter_code(var_name, &operation));
                    }
                    _ => {
                        code.push_str(&format!("        // 未知的滤镜操作类型: {}\n", op_type));
                    }
                }
            }
        }
    }

    code
}

/// 生成添加滤镜的代码
fn generate_add_filter_code(
    var_name: &str,
    operation: &serde_json::Map<String, serde_json::Value>,
) -> String {
    let mut code = String::new();

    if let (Some(filter_type), Some(params)) = (
        operation.get("filterType").and_then(|v| v.as_str()),
        operation.get("params"),
    ) {
        let insert_index = operation.get("insertIndex").and_then(|v| v.as_u64());

        code.push_str(&format!("        // 添加 {} 滤镜\n", filter_type));

        // 确保对象有 filters 数组
        code.push_str(&format!(
            "        if (!targetObject_{}.filters) {{\n",
            var_name
        ));
        code.push_str(&format!(
            "            targetObject_{}.filters = [];\n",
            var_name
        ));
        code.push_str(&format!("        }}\n"));

        // 根据滤镜类型创建滤镜实例
        match filter_type {
            "blur" => {
                let blur = params.get("blur").and_then(|v| v.as_f64()).unwrap_or(2.0);
                let quality = params.get("quality").and_then(|v| v.as_u64()).unwrap_or(4);

                code.push_str(&format!(
                    "        const newBlurFilter = new PIXI.filters.BlurFilter({}, {});\n",
                    blur, quality
                ));

                if let Some(index) = insert_index {
                    code.push_str(&format!(
                        "        targetObject_{}.filters.splice({}, 0, newBlurFilter);\n",
                        var_name, index
                    ));
                } else {
                    code.push_str(&format!(
                        "        targetObject_{}.filters.push(newBlurFilter);\n",
                        var_name
                    ));
                }

                code.push_str(&format!(
                    "        if (DEBUG) log('添加模糊滤镜: blur={}, quality={}');\n",
                    blur, quality
                ));
            }
            "alpha" => {
                let alpha = params.get("alpha").and_then(|v| v.as_f64()).unwrap_or(1.0);

                code.push_str(&format!(
                    "        const newAlphaFilter = new PIXI.filters.AlphaFilter({});\n",
                    alpha
                ));

                if let Some(index) = insert_index {
                    code.push_str(&format!(
                        "        targetObject_{}.filters.splice({}, 0, newAlphaFilter);\n",
                        var_name, index
                    ));
                } else {
                    code.push_str(&format!(
                        "        targetObject_{}.filters.push(newAlphaFilter);\n",
                        var_name
                    ));
                }

                code.push_str(&format!(
                    "        if (DEBUG) log('添加透明度滤镜: alpha={}');\n",
                    alpha
                ));
            }
            _ => {
                // 处理自定义滤镜
                code.push_str(&generate_custom_filter_code(
                    var_name,
                    filter_type,
                    params,
                    insert_index,
                ));
            }
        }
    }

    code
}

/// 生成自定义滤镜代码
fn generate_custom_filter_code(
    var_name: &str,
    filter_type: &str,
    params: &serde_json::Value,
    insert_index: Option<u64>,
) -> String {
    let mut code = String::new();

    match filter_type {
        "smoke" => {
            let intensity = params
                .get("intensity")
                .and_then(|v| v.as_f64())
                .unwrap_or(1.0);

            code.push_str(&format!(
                "        const newSmokeFilter = new SmokeFilter();\n"
            ));
            code.push_str(&format!(
                "        newSmokeFilter.uniforms.intensity = {};\n",
                intensity
            ));
        }
        "fire" => {
            let intensity = params
                .get("intensity")
                .and_then(|v| v.as_f64())
                .unwrap_or(1.0);
            let fire_color_r = params
                .get("fireColor_r")
                .and_then(|v| v.as_f64())
                .unwrap_or(1.0);
            let fire_color_g = params
                .get("fireColor_g")
                .and_then(|v| v.as_f64())
                .unwrap_or(0.5);
            let fire_color_b = params
                .get("fireColor_b")
                .and_then(|v| v.as_f64())
                .unwrap_or(0.0);

            code.push_str(&format!(
                "        const newFireFilter = new FireFilter();\n"
            ));
            code.push_str(&format!(
                "        newFireFilter.uniforms.intensity = {};\n",
                intensity
            ));
            code.push_str(&format!(
                "        newFireFilter.uniforms.fireColor = [{}, {}, {}];\n",
                fire_color_r, fire_color_g, fire_color_b
            ));
        }
        "water" => {
            let amplitude = params
                .get("amplitude")
                .and_then(|v| v.as_f64())
                .unwrap_or(0.1);
            let frequency = params
                .get("frequency")
                .and_then(|v| v.as_f64())
                .unwrap_or(2.0);

            code.push_str(&format!(
                "        const newWaterFilter = new WaterFilter();\n"
            ));
            code.push_str(&format!(
                "        newWaterFilter.uniforms.amplitude = {};\n",
                amplitude
            ));
            code.push_str(&format!(
                "        newWaterFilter.uniforms.frequency = {};\n",
                frequency
            ));
        }
        "cloth" => {
            let intensity = params
                .get("intensity")
                .and_then(|v| v.as_f64())
                .unwrap_or(1.0);
            let wave_frequency = params
                .get("waveFrequency")
                .and_then(|v| v.as_f64())
                .unwrap_or(2.0);

            code.push_str(&format!(
                "        const newClothFilter = new ClothFilter();\n"
            ));
            code.push_str(&format!(
                "        newClothFilter.uniforms.intensity = {};\n",
                intensity
            ));
            code.push_str(&format!(
                "        newClothFilter.uniforms.waveFrequency = {};\n",
                wave_frequency
            ));
        }
        "glow" => {
            let intensity = params
                .get("intensity")
                .and_then(|v| v.as_f64())
                .unwrap_or(1.0);
            let glow_color_r = params
                .get("glowColor_r")
                .and_then(|v| v.as_f64())
                .unwrap_or(1.0);
            let glow_color_g = params
                .get("glowColor_g")
                .and_then(|v| v.as_f64())
                .unwrap_or(1.0);
            let glow_color_b = params
                .get("glowColor_b")
                .and_then(|v| v.as_f64())
                .unwrap_or(0.0);

            code.push_str(&format!(
                "        const newGlowFilter = new GlowFilter();\n"
            ));
            code.push_str(&format!(
                "        newGlowFilter.uniforms.intensity = {};\n",
                intensity
            ));
            code.push_str(&format!(
                "        newGlowFilter.uniforms.glowColor = [{}, {}, {}];\n",
                glow_color_r, glow_color_g, glow_color_b
            ));
        }
        "frost" => {
            let intensity = params
                .get("intensity")
                .and_then(|v| v.as_f64())
                .unwrap_or(1.0);

            code.push_str(&format!(
                "        const newFrostFilter = new FrostFilter();\n"
            ));
            code.push_str(&format!(
                "        newFrostFilter.uniforms.intensity = {};\n",
                intensity
            ));
        }
        "advancedColor" => {
            let contrast = params
                .get("contrast")
                .and_then(|v| v.as_f64())
                .unwrap_or(1.0);
            let brightness = params
                .get("brightness")
                .and_then(|v| v.as_f64())
                .unwrap_or(1.0);
            let saturation = params
                .get("saturation")
                .and_then(|v| v.as_f64())
                .unwrap_or(1.0);

            code.push_str(&format!(
                "        const newAdvancedColorFilter = new AdvancedColorFilter();\n"
            ));
            code.push_str(&format!(
                "        newAdvancedColorFilter.uniforms.contrast = {};\n",
                contrast
            ));
            code.push_str(&format!(
                "        newAdvancedColorFilter.uniforms.brightness = {};\n",
                brightness
            ));
            code.push_str(&format!(
                "        newAdvancedColorFilter.uniforms.saturation = {};\n",
                saturation
            ));
        }
        _ => {
            code.push_str(&format!(
                "        // 未知的自定义滤镜类型: {}\n",
                filter_type
            ));
            return code;
        }
    }

    // 添加滤镜到数组
    let filter_var_name = format!(
        "new{}Filter",
        filter_type
            .chars()
            .next()
            .unwrap()
            .to_uppercase()
            .collect::<String>()
            + &filter_type[1..]
    );

    if let Some(index) = insert_index {
        code.push_str(&format!(
            "        targetObject_{}.filters.splice({}, 0, {});\n",
            var_name, index, filter_var_name
        ));
    } else {
        code.push_str(&format!(
            "        targetObject_{}.filters.push({});\n",
            var_name, filter_var_name
        ));
    }

    code.push_str(&format!(
        "        if (DEBUG) log('添加{}滤镜');\n",
        filter_type
    ));

    code
}

/// 生成删除滤镜的代码
fn generate_remove_filter_code(
    var_name: &str,
    operation: &serde_json::Map<String, serde_json::Value>,
) -> String {
    let mut code = String::new();

    if let Some(filter_index) = operation.get("filterIndex").and_then(|v| v.as_u64()) {
        code.push_str(&format!("        // 删除索引为 {} 的滤镜\n", filter_index));

        code.push_str(&format!(
            "        if (targetObject_{}.filters && targetObject_{}.filters.length > {}) {{\n",
            var_name, var_name, filter_index
        ));

        code.push_str(&format!(
            "            targetObject_{}.filters.splice({}, 1);\n",
            var_name, filter_index
        ));

        code.push_str(&format!(
            "            if (DEBUG) log('删除索引为 {} 的滤镜');\n",
            filter_index
        ));

        code.push_str(&format!("        }} else {{\n"));
        code.push_str(&format!(
            "            if (DEBUG) log('警告: 无法删除索引为 {} 的滤镜，索引超出范围');\n",
            filter_index
        ));
        code.push_str(&format!("        }}\n"));
    }

    code
}

/// 生成重排滤镜的代码
fn generate_reorder_filter_code(
    var_name: &str,
    operation: &serde_json::Map<String, serde_json::Value>,
) -> String {
    let mut code = String::new();

    if let Some(new_order) = operation.get("newOrder") {
        if let Ok(order_array) = serde_json::from_value::<Vec<usize>>(new_order.clone()) {
            code.push_str(&format!("        // 重新排序滤镜\n"));

            code.push_str(&format!(
                "        if (targetObject_{}.filters && targetObject_{}.filters.length > 0) {{\n",
                var_name, var_name
            ));

            code.push_str(&format!(
                "            const oldFilters = [...targetObject_{}.filters];\n",
                var_name
            ));

            code.push_str(&format!(
                "            const newOrder = {};\n",
                serde_json::to_string(&order_array).unwrap_or_default()
            ));

            code.push_str(&format!(
                "            targetObject_{}.filters = newOrder.map(index => oldFilters[index]).filter(f => f);\n",
                var_name
            ));

            code.push_str(&format!(
                "            if (DEBUG) log('重新排序滤镜，新顺序:', newOrder);\n"
            ));

            code.push_str(&format!("        }}\n"));
        }
    }

    code
}

/// 生成滤镜参数修改代码
fn generate_filter_param_modifications_code(
    var_name: &str,
    modifications: &[(&String, &serde_json::Value)],
) -> String {
    let mut code = String::new();

    code.push_str(&format!("        // 修改滤镜参数\n"));

    for (key, value) in modifications {
        // 解析 filters[index].param 格式
        if let Some(captures) = regex::Regex::new(r"filters\[(\d+)\]\.(.+)")
            .unwrap()
            .captures(key)
        {
            if let (Ok(index), param_name) = (
                captures.get(1).unwrap().as_str().parse::<usize>(),
                captures.get(2).unwrap().as_str(),
            ) {
                let value_str = serde_json::to_string(value).unwrap_or_else(|_| "null".to_string());

                code.push_str(&format!(
                    "        // 修改滤镜 {} 的参数 {}\n",
                    index, param_name
                ));

                code.push_str(&format!(
                    "        if (targetObject_{}.filters && targetObject_{}.filters[{}]) {{\n",
                    var_name, var_name, index
                ));

                // 根据参数名称决定如何设置
                if param_name.contains("Color_") {
                    // 处理颜色数组参数
                    let color_type = if param_name.starts_with("fireColor_") {
                        "fireColor"
                    } else if param_name.starts_with("glowColor_") {
                        "glowColor"
                    } else {
                        "color"
                    };

                    let component = if param_name.ends_with("_r") {
                        0
                    } else if param_name.ends_with("_g") {
                        1
                    } else {
                        2
                    };

                    code.push_str(&format!(
                        "            if (targetObject_{}.filters[{}].uniforms && targetObject_{}.filters[{}].uniforms.{}) {{\n",
                        var_name, index, var_name, index, color_type
                    ));

                    code.push_str(&format!(
                        "                targetObject_{}.filters[{}].uniforms.{}[{}] = {};\n",
                        var_name, index, color_type, component, value_str
                    ));

                    code.push_str(&format!("            }}\n"));
                } else {
                    // 处理普通参数
                    code.push_str(&format!(
                        "            if (targetObject_{}.filters[{}].uniforms) {{\n",
                        var_name, index
                    ));

                    code.push_str(&format!(
                        "                targetObject_{}.filters[{}].uniforms.{} = {};\n",
                        var_name, index, param_name, value_str
                    ));

                    code.push_str(&format!("            }} else if (targetObject_{}.filters[{}].{} !== undefined) {{\n", var_name, index, param_name));

                    code.push_str(&format!(
                        "                targetObject_{}.filters[{}].{} = {};\n",
                        var_name, index, param_name, value_str
                    ));

                    code.push_str(&format!("            }}\n"));
                }

                code.push_str(&format!(
                    "            if (DEBUG) log('修改滤镜 {} 的参数 {} =', {});\n",
                    index, param_name, value_str
                ));

                code.push_str(&format!("        }} else {{\n"));
                code.push_str(&format!(
                    "            if (DEBUG) log('警告: 滤镜索引 {} 不存在');\n",
                    index
                ));
                code.push_str(&format!("        }}\n"));
            }
        }
    }

    code
}
