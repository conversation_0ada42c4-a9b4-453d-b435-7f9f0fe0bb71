/*:
 * @target MZ
 * @plugindesc RPG Maker MZ Type Definitions
 * <AUTHOR> Name
 * @help
 * This plugin provides type definitions for RPG Maker MZ.
 * It helps with type checking and code completion.
 */

(() => {
  "use strict";
  window.mzl = true
  // 保存原始的Scene_Boot.start方法
  const _Scene_Boot_start = Scene_Boot.prototype.start;

  // 扩展Scene_Boot.start方法
  Scene_Boot.prototype.start = function () {
    // 调用原始方法
    _Scene_Boot_start.call(this);

    // 游戏加载完成后的自定义代码
    console.log("游戏加载完成！=================");
    // 在这里添加你想要执行的代码
  };
  // 创建全局类型定义对象
  window.RPGMakerMZTypes = {
    // 构造函数注册表
    constructors: {},

    // 自定义类型存储
    customTypes: [],

    // 自定义对象存储
    customObjects: [],
  }
})();
