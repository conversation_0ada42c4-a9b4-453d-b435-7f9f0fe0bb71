/*:
 * @target MZ
 * @plugindesc RPG Maker MZ Type Definitions
 * <AUTHOR> Name
 * @help
 * This plugin provides type definitions for RPG Maker MZ.
 * It helps with type checking and code completion.
 */

(() => {
  "use strict";
  window.mzl = true
  // 创建全局类型定义对象
  window.RPGMakerMZTypes = {
    // 构造函数注册表
    constructors: {},

    // 自定义类型存储
    customTypes: [],

    // 自定义对象存储
    customObjects: [],
  }
})();
