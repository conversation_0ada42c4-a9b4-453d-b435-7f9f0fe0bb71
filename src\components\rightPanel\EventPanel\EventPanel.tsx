import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Alert,
  Divider,
  Button,
  Menu,
  MenuItem,
  ListItemIcon
} from '@mui/material';
import EventIcon from '@mui/icons-material/Event';
import TouchAppIcon from '@mui/icons-material/TouchApp';
import MouseIcon from '@mui/icons-material/Mouse';
import AddIcon from '@mui/icons-material/Add';
import useProjectStore from '../../../store/Store';
import EventCard from './EventCard';

// 定义事件类型接口
interface EventType {
  name: string;       // 事件名称
  property: string;   // 对象属性名
  description: string; // 事件描述
  icon: React.ReactNode; // 事件图标
}

// 事件面板组件
const EventPanel: React.FC = () => {
  const selectedObjectsState = useProjectStore(state => state.selectedObjects);
  const selectedObject = selectedObjectsState.objects.length > 0 ? selectedObjectsState.objects[0] : null;

  // 存储已激活的事件属性（已有代码的事件）
  const [activeEvents, setActiveEvents] = useState<string[]>([]);
  // 菜单状态
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  // 是否是可点击对象
  const [isClickableObject, setIsClickableObject] = useState(false);

  // 定义需要检测的事件类型
  const eventTypes: EventType[] = [
    { name: '点击事件', property: 'onClick', description: '当对象被点击时触发，可以用于处理点击操作', icon: <TouchAppIcon /> },
    { name: '按下事件', property: 'onPress', description: '当对象被按下时触发，可以用于处理按下操作', icon: <MouseIcon /> },
    { name: '鼠标进入', property: 'onMouseEnter', description: '当鼠标进入对象时触发，可以用于处理鼠标悬停效果', icon: <MouseIcon /> },
    { name: '鼠标离开', property: 'onMouseExit', description: '当鼠标离开对象时触发，可以用于处理鼠标离开效果', icon: <MouseIcon /> },
    { name: '触摸检测', property: 'isBeingTouched', description: '检测对象是否被触摸，返回布尔值', icon: <TouchAppIcon /> },
    { name: '点击启用', property: 'isClickEnabled', description: '检测对象是否启用点击，返回布尔值', icon: <TouchAppIcon /> },
    { name: '按下检测', property: 'isPressed', description: '检测对象是否被按下，返回布尔值', icon: <MouseIcon /> },
    { name: '处理触摸', property: 'processTouch', description: '处理对象的触摸事件，包含完整的触摸逻辑', icon: <TouchAppIcon /> }
  ];

  // 打开添加事件菜单
  const handleOpenMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  // 关闭添加事件菜单
  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  // 事件信息对话框相关函数已移除

  // 添加事件
  const handleAddEvent = (eventProperty: string) => {
    // 如果事件已经在激活列表中，不重复添加
    if (!activeEvents.includes(eventProperty)) {
      // 检查对象上是否已有该事件的代码
      const hasExistingCode = checkIfEventActive(selectedObject, eventProperty);
      console.log(`添加事件 ${eventProperty}，对象上是否已有代码:`, hasExistingCode);

      // 添加到激活列表
      setActiveEvents(prev => [...prev, eventProperty]);
    }
    handleCloseMenu();
  };

  // 删除事件卡片
  const handleDeleteEventCard = (eventProperty: string) => {
    console.log(`删除事件卡片: ${eventProperty}`);

    // 从激活列表中移除该事件
    setActiveEvents(prev => prev.filter(prop => prop !== eventProperty));
  };

  // 检查对象是否是Sprite_Clickable类型
  const checkIfClickable = useCallback((obj: any) => {
    if (!obj) return false;

    // 检查对象是否是 Sprite_Clickable 或其子类
    return (
      obj.constructor.name === 'Sprite_Clickable' ||
      (obj.constructor.prototype && obj.constructor.prototype.processTouch) ||
      (typeof obj.processTouch === 'function')
    );
  }, []);

  // 检查事件是否已激活（有特定的编辑器代码标记）
  const checkIfEventActive = useCallback((obj: any, eventProperty: string) => {
    if (!obj || !eventProperty) return false;

    // 检查对象上是否有该事件方法
    const hasMethod = typeof obj[eventProperty] === 'function';
    if (!hasMethod) return false;

    // 获取方法的字符串表示
    const methodString = obj[eventProperty].toString();

    // 检查是否是空函数 - 更全面的检查
    const emptyFunctionPatterns = [
      'function () { }',
      'function() {}',
      '() => {}',
      'function () {}',
      'function(){}',
      '()=>{}',
      'function () { [native code] }', // 原生方法
      'function() { [native code] }',   // 原生方法
      '() => { [native code] }'        // 原生方法
    ];

    // 检查是否匹配任何空函数模式
    const isEmptyFunction = emptyFunctionPatterns.some(pattern =>
      methodString === pattern ||
      methodString.trim() === pattern
    );

    if (isEmptyFunction) return false;

    // 检查是否是系统自带的代码（原生方法）
    const isSystemCode = methodString.includes('[native code]');

    if (isSystemCode) return false;

    // 只检查特定的编辑器代码标记
    const specificEditorMarkers = [
      // 新格式标记 - 精确匹配
      '// ========== 编辑器代码区块开始 ==========',
      '/** EDITOR_CODE_START */',
      '// ========== 拖拽事件区块开始 ==========',
      '/** EVENT_START:',

      // 旧格式标记 - 精确匹配
      '// 执行自定义代码',
      '// 执行 '
    ];

    // 检查是否包含任何特定的编辑器代码标记
    const hasSpecificEditorMarkers = specificEditorMarkers.some(marker =>
      methodString.includes(marker)
    );

    // 只有当函数包含特定的编辑器代码标记时，才认为它是激活的
    const isActive = hasSpecificEditorMarkers;

    if (isActive) {
      console.log(`事件 ${eventProperty} 检测到特定的编辑器代码标记:`, {
        methodString: methodString.substring(0, 100) + (methodString.length > 100 ? '...' : ''),
        isEmptyFunction,
        isSystemCode,
        hasSpecificEditorMarkers,
        isActive
      });
    }

    return isActive;
  }, []);

  // 组件挂载时记录一次
  useEffect(() => {
    console.log('EventPanel组件已挂载');

    // 返回清理函数
    return () => {
      console.log('EventPanel组件将卸载');
    };
  }, []);

  // 检测选中对象上的事件
  useEffect(() => {
    if (!selectedObject) {
      setActiveEvents([]);
      setIsClickableObject(false);
      return;
    }

    console.log('选中对象变化，检查对象事件:', selectedObject);

    // 检查对象是否是可点击的
    const isClickable = checkIfClickable(selectedObject);
    setIsClickableObject(isClickable);

    // 如果不是可点击对象，清空所有事件并返回
    if (!isClickable) {
      setActiveEvents([]);
      return;
    }

    // 存储激活的事件属性（已有代码的事件）
    const active: string[] = [];

    // 检查对象上的事件方法
    eventTypes.forEach(type => {
      // 检查事件是否已激活（有代码）
      if (checkIfEventActive(selectedObject, type.property)) {
        console.log(`检测到事件 ${type.property} 已有代码，自动添加到激活列表`);
        active.push(type.property);
      }
    });

    console.log('当前激活的事件', active);

    // 设置激活的事件列表
    setActiveEvents(active);
  }, [selectedObject, checkIfClickable, checkIfEventActive]);

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
      {/* 标题栏 */}
      <Box sx={{
        p: 1,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottom: '1px solid #e0e0e0'
      }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
          事件面板
        </Typography>

        {/* 添加事件按钮 - 只在选中可点击对象时显示 */}
        {isClickableObject && (
          <Button
            size="small"
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={handleOpenMenu}
          >
            添加事件
          </Button>
        )}
      </Box>

      {/* 事件内容 */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
        {!selectedObject ? (
          <Alert severity="info" sx={{ mt: 2 }}>
            请先选择一个对象以查看其事件
          </Alert>
        ) : !isClickableObject ? (
          <Alert severity="warning" sx={{ mt: 2 }}>
            当前对象不是可点击对象，无法添加事件
          </Alert>
        ) : activeEvents.length === 0 ? (
          <Box>
            <Alert severity="info" sx={{ mt: 2, mb: 2 }}>
              当前对象没有已激活的事件，点击上方"添加事件"按钮创建新事件
            </Alert>
          </Box>
        ) : (
          <Box>
            <Box sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 2
            }}>
              <Typography variant="subtitle2" sx={{ display: 'flex', alignItems: 'center' }}>
                <EventIcon fontSize="small" sx={{ mr: 0.5 }} />
                已检测到的事件 ({activeEvents.length})
              </Typography>
            </Box>

            {/* <Alert severity="info" sx={{ mb: 2 }} variant="outlined">
              系统已自动检测到对象上的事件代码并显示。您也可以点击"添加事件"按钮添加新事件。
            </Alert> */}

            {/* 事件卡片列表 - 只显示已激活的事件 */}
            {activeEvents.map((eventProperty: string) => {
              // 获取事件类型信息
              const eventType = eventTypes.find(type => type.property === eventProperty);

              // 如果找不到事件类型信息，跳过
              if (!eventType) return null;

              return (
                <Box key={eventProperty} sx={{ mb: 2 }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mb: 0.5,
                    backgroundColor: 'rgba(25, 118, 210, 0.08)',
                    p: 0.5,
                    borderRadius: 1
                  }}>
                    <Box sx={{ mr: 1 }}>{eventType.icon}</Box>
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      {eventType.name}
                    </Typography>
                  </Box>

                  <EventCard
                    eventType={eventProperty}
                    initialCode={selectedObject && typeof selectedObject[eventProperty] === 'function' ?
                      selectedObject[eventProperty].toString() : undefined}
                    onDelete={handleDeleteEventCard}
                  />
                  <Divider sx={{ mt: 1 }} />
                </Box>
              );
            })}
          </Box>
        )}
      </Box>

      {/* 添加事件菜单 */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
        slotProps={{
          paper: {
            style: {
              maxHeight: 300,
              width: 250,
            },
          },
        }}
      >
        <Typography variant="subtitle2" sx={{ px: 2, py: 1, fontWeight: 'bold' }}>
          选择要添加的事件类型
        </Typography>
        <Divider />
        {eventTypes
          .filter(type => !activeEvents.includes(type.property))
          .map((type) => (
            <MenuItem
              key={type.property}
              onClick={() => handleAddEvent(type.property)}
            >
              <ListItemIcon>{type.icon}</ListItemIcon>
              <Typography variant="body2">{type.name}</Typography>
            </MenuItem>
          ))}
        {eventTypes.filter(type => !activeEvents.includes(type.property)).length === 0 && (
          <MenuItem disabled>
            <Typography variant="body2">已添加所有可用事件</Typography>
          </MenuItem>
        )}
      </Menu>
    </Box>
  );
};

export default EventPanel;
