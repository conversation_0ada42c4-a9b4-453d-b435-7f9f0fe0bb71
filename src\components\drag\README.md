# Drag Components

简化的拖拽组件库，只包含两个核心组件：

## 组件

### DraggableBase
基础可拖拽组件，提供拖拽功能。

```tsx
import { DraggableBase } from './drag';

<DraggableBase
  id="unique-id"
  data={{ type: 'image', name: 'example.png' }}
>
  <div>可拖拽的内容</div>
</DraggableBase>
```

### DroppableBase
基础可放置组件，提供拖放目标功能。

```tsx
import { DroppableBase } from './drag';

<DroppableBase
  id="drop-zone"
  accepts={['image', 'audio']}
  onDrop={(data) => console.log('放置了:', data)}
  onDragOver={(data) => console.log('悬停:', data)}
>
  <div>拖放区域</div>
</DroppableBase>
```

## 使用示例

```tsx
import React from 'react';
import { DndContext } from '@dnd-kit/core';
import { DraggableBase, DroppableBase } from './drag';

const Example = () => {
  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (over) {
      console.log(`${active.id} 被放置到 ${over.id}`);
    }
  };

  return (
    <DndContext onDragEnd={handleDragEnd}>
      <DraggableBase id="item-1" data={{ type: 'file', name: 'document.pdf' }}>
        <div style={{ padding: 20, border: '1px solid #ccc' }}>
          拖拽我
        </div>
      </DraggableBase>

      <DroppableBase
        id="drop-zone"
        onDrop={(data) => alert(`放置了: ${data.name}`)}
      >
        <div style={{ padding: 40, border: '2px dashed #ccc', marginTop: 20 }}>
          放置区域
        </div>
      </DroppableBase>
    </DndContext>
  );
};
```

## 优势

- **简单**: 只有两个核心组件
- **灵活**: 支持自定义UI和行为
- **高性能**: 减少了组件层级和文件数量
- **易维护**: 代码量减少80%，逻辑更清晰
