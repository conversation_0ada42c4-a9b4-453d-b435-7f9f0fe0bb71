import { useState, useCallback } from 'react';

/**
 * PropertyPanel UI状态管理Hook
 * 处理UI相关的状态和交互逻辑
 */
export const usePropertyPanelUI = () => {
  // UI状态
  const [expandedGroups, setExpandedGroups] = useState<{ [key: string]: boolean }>({
    baseInfo: true,
    imgInfo: true,
    spriteColor: true,
    textInfo: true,
    filters: true,
  });

  // 切换组件展开/收起状态
  const toggleGroupExpanded = useCallback((groupKey: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupKey]: !prev[groupKey]
    }));
  }, []);

  // 设置组件展开状态
  const setGroupExpanded = useCallback((groupKey: string, expanded: boolean) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupKey]: expanded
    }));
  }, []);

  // 展开所有组件
  const expandAllGroups = useCallback(() => {
    setExpandedGroups({
      baseInfo: true,
      imgInfo: true,
      spriteColor: true,
      textInfo: true,
      filters: true,
    });
  }, []);

  // 收起所有组件
  const collapseAllGroups = useCallback(() => {
    setExpandedGroups({
      baseInfo: false,
      imgInfo: false,
      spriteColor: false,
      textInfo: false,
      filters: false,
    });
  }, []);

  return {
    expandedGroups,
    toggleGroupExpanded,
    setGroupExpanded,
    expandAllGroups,
    collapseAllGroups,
  };
};
