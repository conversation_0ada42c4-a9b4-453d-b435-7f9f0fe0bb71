import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Tabs, 
  Tab, 
  Paper, 
  List, 
  ListItem, 
  ListItemText, 
  ListItemAvatar, 
  Avatar,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  LinearProgress,
  Tooltip
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import PersonIcon from '@mui/icons-material/Person';
import InventoryIcon from '@mui/icons-material/Inventory';
import MapIcon from '@mui/icons-material/Map';
import SettingsIcon from '@mui/icons-material/Settings';
import RefreshIcon from '@mui/icons-material/Refresh';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`game-data-tabpanel-${index}`}
      aria-labelledby={`game-data-tab-${index}`}
      style={{ height: 'calc(100% - 48px)', overflow: 'auto' }}
      {...other}
    >
      {value === index && (
        <Box sx={{ height: '100%', p: 1 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `game-data-tab-${index}`,
    'aria-controls': `game-data-tabpanel-${index}`,
  };
}

// 角色数据接口
interface Character {
  id: number;
  name: string;
  level: number;
  hp: number;
  maxHp: number;
  mp: number;
  maxMp: number;
  atk: number;
  def: number;
  mat: number;
  mdf: number;
  agi: number;
  luk: number;
  exp: number;
  nextLevelExp: number;
  faceImage?: string;
  characterImage?: string;
  class?: string;
  equips?: Item[];
  skills?: Skill[];
}

// 物品数据接口
interface Item {
  id: number;
  name: string;
  description: string;
  price: number;
  iconIndex: number;
  type: 'item' | 'weapon' | 'armor' | 'key';
  count?: number;
  effects?: string[];
}

// 技能数据接口
interface Skill {
  id: number;
  name: string;
  description: string;
  mpCost: number;
  tpCost: number;
  iconIndex: number;
  damage?: {
    type: string;
    elementId: number;
    formula: string;
  };
  effects?: string[];
}

// 地图数据接口
interface MapInfo {
  id: number;
  name: string;
  displayName: string;
  width: number;
  height: number;
  scrollX: number;
  scrollY: number;
  events: number;
}

// 系统数据接口
interface SystemInfo {
  gameTitle: string;
  version: string;
  locale: string;
  currencyUnit: string;
  windowTone: number[];
  battleBgm?: { name: string; pan: number; pitch: number; volume: number };
  defeatMe?: { name: string; pan: number; pitch: number; volume: number };
  gameoverMe?: { name: string; pan: number; pitch: number; volume: number };
  titleBgm?: { name: string; pan: number; pitch: number; volume: number };
  victoryMe?: { name: string; pan: number; pitch: number; volume: number };
}

const GameData: React.FC = () => {
  const [value, setValue] = useState(0);
  const [characters, setCharacters] = useState<Character[]>([]);
  const [items, setItems] = useState<Item[]>([]);
  const [mapInfo, setMapInfo] = useState<MapInfo | null>(null);
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  // 刷新数据
  const refreshData = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // 获取游戏数据
  useEffect(() => {
    const iframe = document.querySelector('iframe');
    if (!iframe || !iframe.contentWindow) return;
    
    const gameWindow = iframe.contentWindow as any;
    
    try {
      // 获取角色数据
      if (gameWindow.$gameParty && gameWindow.$gameActors) {
        const party = gameWindow.$gameParty;
        const actors = gameWindow.$gameActors;
        const members = party._actors || [];
        
        const characterData: Character[] = members.map((id: number) => {
          const actor = actors._data[id];
          if (!actor) return null;
          
          return {
            id: actor._actorId,
            name: actor._name,
            level: actor._level,
            hp: actor._hp,
            maxHp: actor.mhp,
            mp: actor._mp,
            maxMp: actor.mmp,
            atk: actor.atk,
            def: actor.def,
            mat: actor.mat,
            mdf: actor.mdf,
            agi: actor.agi,
            luk: actor.luk,
            exp: actor._exp[actor._classId],
            nextLevelExp: actor.nextLevelExp(),
            class: actor.currentClass()?.name || '未知',
            faceImage: actor.faceName(),
            characterImage: actor.characterName(),
            equips: actor.equips().filter(Boolean).map((equip: any) => ({
              id: equip.id,
              name: equip.name,
              description: equip.description,
              price: equip.price,
              iconIndex: equip.iconIndex,
              type: equip.wtypeId ? 'weapon' : 'armor'
            })),
            skills: actor.skills().map((skill: any) => ({
              id: skill.id,
              name: skill.name,
              description: skill.description,
              mpCost: skill.mpCost,
              tpCost: skill.tpCost,
              iconIndex: skill.iconIndex,
              damage: skill.damage
            }))
          };
        }).filter(Boolean);
        
        setCharacters(characterData);
      }
      
      // 获取物品数据
      if (gameWindow.$gameParty) {
        const party = gameWindow.$gameParty;
        const allItems = [
          ...(party.items() || []).map((item: any) => ({ ...item, type: 'item', count: party.numItems(item) })),
          ...(party.weapons() || []).map((item: any) => ({ ...item, type: 'weapon', count: party.numItems(item) })),
          ...(party.armors() || []).map((item: any) => ({ ...item, type: 'armor', count: party.numItems(item) })),
          ...(party.keyItems() || []).map((item: any) => ({ ...item, type: 'key', count: 1 }))
        ];
        
        setItems(allItems);
      }
      
      // 获取地图数据
      if (gameWindow.$gameMap) {
        const map = gameWindow.$gameMap;
        setMapInfo({
          id: map._mapId,
          name: map.mapId ? `Map${map.mapId().padZero(3)}` : 'Unknown',
          displayName: map.displayName ? map.displayName() : 'Unknown',
          width: map.width(),
          height: map.height(),
          scrollX: map._displayX,
          scrollY: map._displayY,
          events: map._events ? map._events.filter(Boolean).length : 0
        });
      }
      
      // 获取系统数据
      if (gameWindow.$dataSystem) {
        const system = gameWindow.$dataSystem;
        setSystemInfo({
          gameTitle: system.gameTitle,
          version: gameWindow.Utils?.RPGMAKER_VERSION || 'Unknown',
          locale: gameWindow.Utils?.RPGMAKER_LOCALE || 'Unknown',
          currencyUnit: system.currencyUnit,
          windowTone: system.windowTone,
          battleBgm: system.battleBgm,
          defeatMe: system.defeatMe,
          gameoverMe: system.gameoverMe,
          titleBgm: system.titleBgm,
          victoryMe: system.victoryMe
        });
      }
    } catch (error) {
      console.error('获取游戏数据失败:', error);
    }
  }, [refreshTrigger]);

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ 
        p: 1, 
        display: 'flex', 
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottom: '1px solid #e0e0e0'
      }}>
        <Typography variant="h6">游戏数据</Typography>
        <Tooltip title="刷新数据">
          <RefreshIcon 
            sx={{ cursor: 'pointer' }} 
            onClick={refreshData}
          />
        </Tooltip>
      </Box>
      
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs 
          value={value} 
          onChange={handleChange} 
          aria-label="game data tabs"
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab icon={<PersonIcon />} label="角色" {...a11yProps(0)} />
          <Tab icon={<InventoryIcon />} label="物品" {...a11yProps(1)} />
          <Tab icon={<MapIcon />} label="地图" {...a11yProps(2)} />
          <Tab icon={<SettingsIcon />} label="系统" {...a11yProps(3)} />
        </Tabs>
      </Box>
      
      <TabPanel value={value} index={0}>
        {characters.length > 0 ? (
          <List>
            {characters.map((character) => (
              <Paper key={character.id} sx={{ mb: 2 }}>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar>
                      <PersonIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={`${character.name} (Lv.${character.level} ${character.class})`}
                    secondary={
                      <Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <Typography variant="body2" component="span" sx={{ minWidth: 40 }}>
                            HP:
                          </Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={(character.hp / character.maxHp) * 100} 
                            sx={{ flex: 1, mr: 1 }}
                          />
                          <Typography variant="body2">
                            {character.hp}/{character.maxHp}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <Typography variant="body2" component="span" sx={{ minWidth: 40 }}>
                            MP:
                          </Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={(character.mp / character.maxMp) * 100} 
                            color="secondary"
                            sx={{ flex: 1, mr: 1 }}
                          />
                          <Typography variant="body2">
                            {character.mp}/{character.maxMp}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <Typography variant="body2" component="span" sx={{ minWidth: 40 }}>
                            EXP:
                          </Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={(character.exp / character.nextLevelExp) * 100} 
                            color="success"
                            sx={{ flex: 1, mr: 1 }}
                          />
                          <Typography variant="body2">
                            {character.exp}/{character.nextLevelExp}
                          </Typography>
                        </Box>
                      </Box>
                    }
                  />
                </ListItem>
                
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography>属性</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="body2">攻击力: {character.atk}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2">防御力: {character.def}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2">魔法攻击: {character.mat}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2">魔法防御: {character.mdf}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2">敏捷: {character.agi}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2">幸运: {character.luk}</Typography>
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </Accordion>
                
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography>装备</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    {character.equips && character.equips.length > 0 ? (
                      <List dense>
                        {character.equips.map((equip, index) => (
                          <ListItem key={index}>
                            <ListItemText
                              primary={equip.name}
                              secondary={equip.description}
                            />
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <Typography>无装备</Typography>
                    )}
                  </AccordionDetails>
                </Accordion>
                
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography>技能</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    {character.skills && character.skills.length > 0 ? (
                      <List dense>
                        {character.skills.map((skill, index) => (
                          <ListItem key={index}>
                            <ListItemText
                              primary={`${skill.name} (MP: ${skill.mpCost})`}
                              secondary={skill.description}
                            />
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <Typography>无技能</Typography>
                    )}
                  </AccordionDetails>
                </Accordion>
              </Paper>
            ))}
          </List>
        ) : (
          <Typography>无角色数据</Typography>
        )}
      </TabPanel>
      
      <TabPanel value={value} index={1}>
        {items.length > 0 ? (
          <List>
            {items.map((item, index) => (
              <React.Fragment key={index}>
                <ListItem>
                  <ListItemText
                    primary={`${item.name} x${item.count || 1}`}
                    secondary={
                      <Box>
                        <Typography variant="body2">{item.description}</Typography>
                        <Typography variant="body2">价格: {item.price} G</Typography>
                        <Typography variant="body2">类型: {
                          item.type === 'item' ? '物品' :
                          item.type === 'weapon' ? '武器' :
                          item.type === 'armor' ? '防具' : '钥匙物品'
                        }</Typography>
                      </Box>
                    }
                  />
                </ListItem>
                {index < items.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        ) : (
          <Typography>无物品数据</Typography>
        )}
      </TabPanel>
      
      <TabPanel value={value} index={2}>
        {mapInfo ? (
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6">{mapInfo.displayName}</Typography>
            <Divider sx={{ my: 1 }} />
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="body2">地图ID: {mapInfo.id}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2">文件名: {mapInfo.name}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2">宽度: {mapInfo.width}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2">高度: {mapInfo.height}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2">X坐标: {mapInfo.scrollX}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2">Y坐标: {mapInfo.scrollY}</Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2">事件数量: {mapInfo.events}</Typography>
              </Grid>
            </Grid>
          </Paper>
        ) : (
          <Typography>无地图数据</Typography>
        )}
      </TabPanel>
      
      <TabPanel value={value} index={3}>
        {systemInfo ? (
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6">{systemInfo.gameTitle}</Typography>
            <Divider sx={{ my: 1 }} />
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="body2">RPG Maker 版本: {systemInfo.version}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2">区域设置: {systemInfo.locale}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2">货币单位: {systemInfo.currencyUnit}</Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2">窗口色调: [{systemInfo.windowTone.join(', ')}]</Typography>
              </Grid>
              
              <Grid item xs={12}>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography>音乐设置</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {systemInfo.battleBgm && (
                        <ListItem>
                          <ListItemText
                            primary="战斗BGM"
                            secondary={`${systemInfo.battleBgm.name} (音量: ${systemInfo.battleBgm.volume}, 音调: ${systemInfo.battleBgm.pitch})`}
                          />
                        </ListItem>
                      )}
                      {systemInfo.victoryMe && (
                        <ListItem>
                          <ListItemText
                            primary="胜利ME"
                            secondary={`${systemInfo.victoryMe.name} (音量: ${systemInfo.victoryMe.volume}, 音调: ${systemInfo.victoryMe.pitch})`}
                          />
                        </ListItem>
                      )}
                      {systemInfo.defeatMe && (
                        <ListItem>
                          <ListItemText
                            primary="失败ME"
                            secondary={`${systemInfo.defeatMe.name} (音量: ${systemInfo.defeatMe.volume}, 音调: ${systemInfo.defeatMe.pitch})`}
                          />
                        </ListItem>
                      )}
                      {systemInfo.gameoverMe && (
                        <ListItem>
                          <ListItemText
                            primary="游戏结束ME"
                            secondary={`${systemInfo.gameoverMe.name} (音量: ${systemInfo.gameoverMe.volume}, 音调: ${systemInfo.gameoverMe.pitch})`}
                          />
                        </ListItem>
                      )}
                      {systemInfo.titleBgm && (
                        <ListItem>
                          <ListItemText
                            primary="标题BGM"
                            secondary={`${systemInfo.titleBgm.name} (音量: ${systemInfo.titleBgm.volume}, 音调: ${systemInfo.titleBgm.pitch})`}
                          />
                        </ListItem>
                      )}
                    </List>
                  </AccordionDetails>
                </Accordion>
              </Grid>
            </Grid>
          </Paper>
        ) : (
          <Typography>无系统数据</Typography>
        )}
      </TabPanel>
    </Box>
  );
};

export default GameData;
