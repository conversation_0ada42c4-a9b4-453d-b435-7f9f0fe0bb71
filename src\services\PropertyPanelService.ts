/**
 * PropertyPanel Facade Service
 * 封装PropertyPanel相关的复杂API调用和业务逻辑
 */

import { getObjectTypeName } from '../utils/object/ObjectFinder';
import { PropertyManager } from './PropertyManager';
import { supportsSpriteColorProperties } from './backendServer/spriteColor';

export class PropertyPanelService {
  /**
   * 检查对象是否支持某个属性组
   */
  static shouldShowPropertyGroup(selectedObject: any, groupType: string): boolean {
    if (!selectedObject) return false;

    switch (groupType) {
      case 'baseInfo':
        return true; // 所有对象都显示基础信息

      case 'imgInfo':
        // 只对图片Sprite显示，且不是复杂bitmap
        return selectedObject._bitmap &&
          !Array.isArray(selectedObject._bitmap.elements);

      case 'spriteColor':
        // 对所有支持Sprite颜色属性的对象显示
        return supportsSpriteColorProperties(selectedObject);

      case 'textInfo':
        // 对带有elements数组的bitmap显示
        return selectedObject._bitmap &&
          Array.isArray(selectedObject._bitmap.elements);

      case 'filters':
        // 对所有支持滤镜的对象显示
        return this.supportsFilters(selectedObject);

      default:
        return false;
    }
  }

  /**
   * 检查对象是否是Sprite类型
   */
  static isSpriteObject(obj: any): boolean {
    if (!obj) return false;

    const typeName = getObjectTypeName(obj);
    return typeName.includes('Sprite') ||
      obj.constructor?.name?.includes('Sprite') ||
      obj._texture !== undefined;
  }

  /**
   * 检查对象是否支持滤镜
   */
  static supportsFilters(obj: any): boolean {
    if (!obj) return false;

    // 检查是否有filters属性或者是DisplayObject类型
    return obj.filters !== undefined ||
      obj._filters !== undefined ||
      this.isDisplayObject(obj);
  }

  /**
   * 检查对象是否是DisplayObject类型
   */
  static isDisplayObject(obj: any): boolean {
    if (!obj) return false;

    const typeName = getObjectTypeName(obj);
    return typeName.includes('Sprite') ||
      typeName.includes('Container') ||
      typeName.includes('Graphics') ||
      obj.visible !== undefined;
  }

  /**
   * 获取对象的显示信息
   */
  static getObjectDisplayInfo(obj: any): {
    typeName: string;
    displayName: string;
    hasChildren: boolean;
    childrenCount: number;
  } {
    if (!obj) {
      return {
        typeName: 'Unknown',
        displayName: '未知对象',
        hasChildren: false,
        childrenCount: 0
      };
    }

    const typeName = getObjectTypeName(obj);
    const hasChildren = obj.children && Array.isArray(obj.children);
    const childrenCount = hasChildren ? obj.children.length : 0;

    let displayName = obj.name || obj.constructor?.name || typeName;
    if (hasChildren && childrenCount > 0) {
      displayName += ` (${childrenCount} 个子对象)`;
    }

    return {
      typeName,
      displayName,
      hasChildren,
      childrenCount
    };
  }

  /**
   * 获取多选对象的统计信息
   */
  static getMultiSelectionStats(objects: any[]): { [key: string]: number } {
    const stats: { [key: string]: number } = {};

    objects.forEach((obj) => {
      const typeName = getObjectTypeName(obj);
      stats[typeName] = (stats[typeName] || 0) + 1;
    });

    return stats;
  }

  /**
   * 检查是否应该显示Sprite编辑器
   */
  static shouldShowSpriteEditor(selectedObject: any): boolean {
    return selectedObject &&
      selectedObject._bitmap &&
      Array.isArray(selectedObject._bitmap.elements);
  }

  /**
   * 获取项目资源路径
   */
  static getProjectResourcePath(projectName?: string): string {
    if (!projectName) return '';

    // 根据是否在mzl环境返回不同的路径
    const basePath = (window as any).mzl ? `../projects/${projectName}` : '';
    return basePath;
  }

  /**
   * 验证对象属性修改的有效性
   * @deprecated 建议使用 PropertyManager.validateProperty 替代
   */
  static validatePropertyChange(obj: any, propertyName: string, newValue: any): {
    isValid: boolean;
    errorMessage?: string;
  } {
    console.warn('validatePropertyChange 已废弃，建议使用 PropertyManager.validateProperty');
    return PropertyManager.validateProperty([obj], propertyName, newValue);
  }
}
