import { useEffect } from 'react';
import GlobalEventHandler from '../utils/common/GlobalEventHandler';

/**
 * 使用全局事件处理的钩子函数
 * 在组件挂载时初始化全局事件处理，在组件卸载时清理
 * @param enabled 是否启用全局事件处理，默认为true
 */
const useGlobalEvents = (enabled: boolean = true): void => {
  useEffect(() => {
    // 只有在启用时才初始化全局事件处理
    if (enabled) {
      const eventHandler = GlobalEventHandler.getInstance();
      eventHandler.initialize();

      // 在组件卸载时清理全局事件处理
      return () => {
        eventHandler.cleanup();
      };
    }
  }, [enabled]);
};

export default useGlobalEvents;
