import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  Button,
  IconButton,
  RadioGroup,
  FormControlLabel,
  Radio,
  Switch,
  TextField,
  Divider
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import SaveIcon from '@mui/icons-material/Save';
import CloseIcon from '@mui/icons-material/Close';
import LoopIcon from '@mui/icons-material/Loop';
import SwapHorizIcon from '@mui/icons-material/SwapHoriz';
import NumberInput from '../../ui/NumberInput';
import SelectInput from '../../ui/SelectInput';
import SliderInput from '../../ui/SliderInput';
import { AnimationProperty, AnimationStackItem, AnimationMode } from '../../../types/components/AnimationTypes';

// 缓动函数选项
const easingOptions = [
  { value: 'linear', label: '线性 (Linear)' },
  { value: 'easeInQuad', label: '二次方缓入 (Quad In)' },
  { value: 'easeOutQuad', label: '二次方缓出 (Quad Out)' },
  { value: 'easeInOutQuad', label: '二次方缓入缓出 (Quad InOut)' },
  { value: 'easeInCubic', label: '三次方缓入 (Cubic In)' },
  { value: 'easeOutCubic', label: '三次方缓出 (Cubic Out)' },
  { value: 'easeInOutCubic', label: '三次方缓入缓出 (Cubic InOut)' },
  { value: 'easeInElastic', label: '弹性缓入 (Elastic In)' },
  { value: 'easeOutElastic', label: '弹性缓出 (Elastic Out)' },
  { value: 'easeInOutElastic', label: '弹性缓入缓出 (Elastic InOut)' },
  { value: 'easeInBounce', label: '反弹缓入 (Bounce In)' },
  { value: 'easeOutBounce', label: '反弹缓出 (Bounce Out)' },
  { value: 'easeInOutBounce', label: '反弹缓入缓出 (Bounce InOut)' },
];

// 动画属性选项
const propertyOptions = [
  { value: 'x', label: 'X 位置' },
  { value: 'y', label: 'Y 位置' },
  { value: 'alpha', label: '透明度' },
  { value: 'scale.x', label: 'X 缩放' },
  { value: 'scale.y', label: 'Y 缩放' },
  { value: 'rotation', label: '旋转' },
];

// 动画预设
const animationPresets = [
  {
    name: '淡入',
    properties: [{ property: 'alpha', value: 1, relative: false }],
    duration: 1000,
    easing: 'easeOutQuad'
  },
  {
    name: '淡出',
    properties: [{ property: 'alpha', value: 0, relative: false }],
    duration: 1000,
    easing: 'easeOutQuad'
  },
  {
    name: '向右移动',
    properties: [{ property: 'x', value: 100, relative: true }],
    duration: 1000,
    easing: 'easeOutElastic'
  },
  {
    name: '向左移动',
    properties: [{ property: 'x', value: -100, relative: true }],
    duration: 1000,
    easing: 'easeOutElastic'
  },
  {
    name: '放大',
    properties: [
      { property: 'scale.x', value: 1.5, relative: false },
      { property: 'scale.y', value: 1.5, relative: false }
    ],
    duration: 1000,
    easing: 'easeOutQuad'
  },
  {
    name: '缩小',
    properties: [
      { property: 'scale.x', value: 0.5, relative: false },
      { property: 'scale.y', value: 0.5, relative: false }
    ],
    duration: 1000,
    easing: 'easeOutQuad'
  },
  {
    name: '旋转360度',
    properties: [{ property: 'rotation', value: Math.PI * 2, relative: true }],
    duration: 1500,
    easing: 'easeInOutQuad'
  },
  {
    name: '弹跳',
    properties: [{ property: 'y', value: -30, relative: true }],
    duration: 500,
    easing: 'easeOutBounce'
  }
];

// 动画属性项组件
interface AnimationPropertyItemProps {
  property: string;
  value: number;
  relative: boolean;
  onPropertyChange: (value: string) => void;
  onValueChange: (value: number) => void;
  onRelativeChange: (value: boolean) => void;
  onDelete: () => void;
}

const AnimationPropertyItem: React.FC<AnimationPropertyItemProps> = ({
  property,
  value,
  relative,
  onPropertyChange,
  onValueChange,
  onRelativeChange,
  onDelete
}) => {
  // 转换属性选项为SelectInput需要的格式
  const selectOptions = propertyOptions.map(option => ({
    value: option.value,
    label: option.label
  }));

  // 相对/绝对值选项
  const relativeOptions = [
    { value: 'absolute', label: '绝对值' },
    { value: 'relative', label: '相对值' }
  ];

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5, gap: 0.5 }}>
      <Box sx={{ width: '40%' }}>
        <SelectInput
          value={property}
          options={selectOptions}
          onChange={onPropertyChange}
          compact={true}
        />
      </Box>

      <Box sx={{ width: '25%' }}>
        <NumberInput
          label=""
          value={value}
          onChange={onValueChange}
          step={1}
          precision={property === 'alpha' ? 2 : 0}
        />
      </Box>

      <Box sx={{ width: '25%' }}>
        <SelectInput
          value={relative ? 'relative' : 'absolute'}
          options={relativeOptions}
          onChange={(value) => onRelativeChange(value === 'relative')}
          compact={true}
        />
      </Box>

      <IconButton
        size="small"
        onClick={onDelete}
        color="error"
        sx={{ padding: 0.25 }}
      >
        <DeleteIcon sx={{ fontSize: '1rem' }} />
      </IconButton>
    </Box>
  );
};

interface AnimationEditorProps {
  open: boolean;
  onClose: () => void;
  onSave: (animation: Omit<AnimationStackItem, 'id' | 'active' | 'animation'>) => void;
  editingAnimation: AnimationStackItem | null;
}

const AnimationEditor: React.FC<AnimationEditorProps> = ({
  open,
  onClose,
  onSave,
  editingAnimation
}) => {
  const [animationName, setAnimationName] = useState<string>('新动画');
  const [properties, setProperties] = useState<AnimationProperty[]>([
    { property: 'x', value: 100, relative: true }
  ]);
  const [duration, setDuration] = useState<number>(1000);
  const [easing, setEasing] = useState<string>('easeOutQuad');
  const [animationTarget, setAnimationTarget] = useState<'self' | 'children'>('self');
  const [playMode, setPlayMode] = useState<AnimationMode>('once');
  const [playCount, setPlayCount] = useState<number>(1); // -1 表示无限循环
  const [childrenDelay, setChildrenDelay] = useState<number>(0); // 子元素动画开始的时间间隔（毫秒）

  // 当编辑现有动画时，加载其属性
  useEffect(() => {
    if (editingAnimation) {
      setAnimationName(editingAnimation.name);
      setProperties([...editingAnimation.properties]);
      setDuration(editingAnimation.duration);
      setEasing(editingAnimation.easing);
      setAnimationTarget(editingAnimation.targetType);
      setPlayMode(editingAnimation.playMode as AnimationMode || 'once');
      setPlayCount(editingAnimation.playCount || 1);
      setChildrenDelay(editingAnimation.childrenDelay || 0);
    } else {
      // 重置为默认值
      setAnimationName('新动画');
      setProperties([{ property: 'x', value: 100, relative: true }]);
      setDuration(1000);
      setEasing('easeOutQuad');
      setAnimationTarget('self');
      setPlayMode('once');
      setPlayCount(1);
      setChildrenDelay(0);
    }
  }, [editingAnimation, open]);

  // 添加新的动画属性
  const handleAddProperty = () => {
    setProperties([...properties, { property: 'x', value: 0, relative: true }]);
  };

  // 删除动画属性
  const handleDeleteProperty = (index: number) => {
    const newProperties = [...properties];
    newProperties.splice(index, 1);
    setProperties(newProperties);
  };

  // 更新动画属性
  const handlePropertyChange = (index: number, field: string, value: any) => {
    const newProperties = [...properties];
    newProperties[index] = { ...newProperties[index], [field]: value };
    setProperties(newProperties);
  };

  // 应用动画预设
  const applyPreset = (preset: any) => {
    setProperties(preset.properties);
    setDuration(preset.duration);
    setEasing(preset.easing);
  };

  // 保存动画
  const handleSave = () => {
    if (!animationName.trim()) {
      alert('请输入动画名称');
      return;
    }

    if (properties.length === 0) {
      alert('请添加至少一个动画属性');
      return;
    }

    onSave({
      name: animationName,
      properties: [...properties],
      duration,
      easing,
      targetType: animationTarget,
      playMode,
      playCount,
      childrenDelay
    });

    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      className="animation-editor-dialog"
    >
      <DialogTitle sx={{ p: 1.5 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6" sx={{ fontSize: '1rem' }}>
            {editingAnimation ? '编辑动画' : '创建新动画'}
          </Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent dividers sx={{ p: 1.5 }}>
        <Box sx={{ display: 'flex', gap: 1.5 }}>
          {/* 左侧：动画属性编辑区 */}
          <Box sx={{ flex: 1 }}>
            <Box sx={{ mb: 1.5 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ mb: 0.5 }}>动画名称</Typography>
              <TextField
                size="small"
                fullWidth
                value={animationName}
                onChange={(e) => setAnimationName(e.target.value)}
                placeholder="输入动画名称"
                sx={{ '.MuiInputBase-root': { height: '32px' } }}
              />
            </Box>

            <Box sx={{ mb: 1.5 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ mb: 0.5 }}>动画目标</Typography>
              <RadioGroup
                row
                value={animationTarget}
                onChange={(e) => setAnimationTarget(e.target.value as 'self' | 'children')}
              >
                <FormControlLabel value="self" control={<Radio size="small" />} label="当前对象" />
                <FormControlLabel value="children" control={<Radio size="small" />} label="子元素" />
              </RadioGroup>

              {/* 子元素延迟设置 */}
              {animationTarget === 'children' && (
                <Box sx={{ mt: 1 }}>
                  <Typography variant="caption" gutterBottom>子元素动画间隔 (毫秒)</Typography>
                  <SliderInput
                    value={childrenDelay}
                    onChange={setChildrenDelay}
                    min={0}
                    max={1000}
                    step={10}
                    showInput={true}
                    unit="ms"
                  />
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                    0表示所有子元素同时执行动画，大于0表示子元素按照设定的时间间隔依次执行
                  </Typography>
                </Box>
              )}
            </Box>

            <Box sx={{ mb: 1.5 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                <Typography variant="subtitle2">动画属性</Typography>
                <Button
                  startIcon={<AddIcon />}
                  onClick={handleAddProperty}
                  size="small"
                  sx={{ minWidth: 'auto', py: 0.25 }}
                >
                  添加
                </Button>
              </Box>
              {properties.map((prop, index) => (
                <AnimationPropertyItem
                  key={index}
                  property={prop.property}
                  value={prop.value}
                  relative={prop.relative}
                  onPropertyChange={(value: string) => handlePropertyChange(index, 'property', value)}
                  onValueChange={(value: number) => handlePropertyChange(index, 'value', value)}
                  onRelativeChange={(value: boolean) => handlePropertyChange(index, 'relative', value)}
                  onDelete={() => handleDeleteProperty(index)}
                />
              ))}
            </Box>

            <Box sx={{ mb: 1.5 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ mb: 0.5 }}>动画时长 (毫秒)</Typography>
              <SliderInput
                value={duration}
                onChange={setDuration}
                min={100}
                max={5000}
                step={100}
                showInput={true}
                unit="ms"
              />
            </Box>

            <Box sx={{ mb: 1.5 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ mb: 0.5 }}>缓动函数</Typography>
              <SelectInput
                value={easing}
                options={easingOptions}
                onChange={setEasing}
                width="100%"
              />
            </Box>

            <Box sx={{ mb: 1.5 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ mb: 0.5 }}>播放模式</Typography>
              <Box sx={{ mb: 1 }}>
                <SelectInput
                  value={playMode}
                  options={[
                    { value: 'once', label: '单次播放' },
                    { value: 'loop', label: '循环播放', icon: <LoopIcon fontSize="small" /> },
                    { value: 'yoyo', label: '来回播放', icon: <SwapHorizIcon fontSize="small" /> }
                  ]}
                  onChange={(value) => setPlayMode(value as AnimationMode)}
                  width="100%"
                />
              </Box>

              {playMode !== 'once' && (
                <Box sx={{ mb: 1 }}>
                  <Typography variant="caption" gutterBottom>播放次数</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={playCount === -1}
                          onChange={(e) => setPlayCount(e.target.checked ? -1 : 3)}
                          size="small"
                        />
                      }
                      label="无限循环"
                      sx={{ mr: 2 }}
                    />

                    {playCount !== -1 && (
                      <NumberInput
                        label=""
                        value={playCount}
                        onChange={setPlayCount}
                        min={1}
                        max={100}
                        step={1}
                        precision={0}
                        width="80px"
                      />
                    )}
                  </Box>
                </Box>
              )}
            </Box>
          </Box>

          {/* 右侧：动画预设 */}
          <Box sx={{ width: '200px' }}>
            <Typography variant="subtitle2" gutterBottom sx={{ mb: 0.5 }}>动画预设</Typography>
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(1, 1fr)', gap: 0.5 }}>
              {animationPresets.map((preset, index) => (
                <Button
                  key={index}
                  variant="outlined"
                  size="small"
                  onClick={() => applyPreset(preset)}
                  sx={{ justifyContent: 'flex-start', textTransform: 'none', py: 0.5 }}
                >
                  {preset.name}
                </Button>
              ))}
            </Box>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions sx={{ p: 1 }}>
        <Button onClick={onClose} size="small">取消</Button>
        <Button
          variant="contained"
          color="primary"
          startIcon={<SaveIcon />}
          onClick={handleSave}
          size="small"
        >
          {editingAnimation ? '更新' : '保存'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AnimationEditor;
