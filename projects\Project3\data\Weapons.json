[null, {"id": 1, "animationId": 6, "description": "【剑】轻巧易操作的短剑。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 97, "name": "短剑", "note": "", "params": [0, 0, 8, 0, 0, 0, 0, 0], "price": 300, "wtypeId": 2}, {"id": 2, "animationId": 6, "description": "【剑】一把细长的战斗剑，拥有锋利的刀口。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 97, "name": "长剑", "note": "", "params": [0, 0, 12, 0, 0, 0, 0, 0], "price": 670, "wtypeId": 2}, {"id": 3, "animationId": 6, "description": "【剑】带有弯曲刀身的宽剑。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 97, "name": "弯刀", "note": "", "params": [0, 0, 18, 0, 0, 0, 0, 0], "price": 1480, "wtypeId": 2}, {"id": 4, "animationId": 6, "description": "【剑】秘银锻造的长剑。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 97, "name": "秘银剑", "note": "", "params": [0, 0, 25, 0, 0, 0, 0, 0], "price": 3890, "wtypeId": 2}, {"id": 5, "animationId": 6, "description": "【剑】使用龙之齿锻造的剑。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 97, "name": "龙刃", "note": "", "params": [0, 0, 36, 0, 0, 0, 0, 0], "price": 6970, "wtypeId": 2}, {"id": 6, "animationId": 0, "description": "", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0, "wtypeId": 0}, {"id": 7, "animationId": 1, "description": "【杖】廉价且使用广泛的棍子。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 101, "name": "木棍", "note": "", "params": [0, 0, 2, 0, 8, 3, 0, 0], "price": 280, "wtypeId": 6}, {"id": 8, "animationId": 1, "description": "【杖】这根杖能略微提升使用者\n的魔力。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 101, "name": "魔杖", "note": "", "params": [0, 0, 3, 0, 12, 6, 0, 0], "price": 780, "wtypeId": 6}, {"id": 9, "animationId": 1, "description": "【杖】这根银杖能够提升使用者\n的魔力。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 101, "name": "力量之杖", "note": "", "params": [0, 0, 5, 0, 21, 12, 0, 0], "price": 1680, "wtypeId": 6}, {"id": 10, "animationId": 1, "description": "【杖】这根秘银棒能够提升使用者\n的魔力。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 101, "name": "秘银棒", "note": "", "params": [0, 0, 12, 0, 32, 24, 0, 0], "price": 4120, "wtypeId": 6}, {"id": 11, "animationId": 1, "description": "【杖】这根龙杖能够提升使用者\n的魔力。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 101, "name": "龙杖", "note": "", "params": [0, 0, 18, 0, 45, 52, 0, 0], "price": 7680, "wtypeId": 6}, {"id": 12, "animationId": 0, "description": "", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0, "wtypeId": 0}, {"id": 13, "animationId": 1, "description": "【链枷】手柄前端用锁链连接着石球的链枷。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 98, "name": "石链枷", "note": "", "params": [0, 0, 7, 0, 0, 0, 0, 0], "price": 360, "wtypeId": 3}, {"id": 14, "animationId": 1, "description": "【链枷】手柄前端用锁链连接着铜球的链枷。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 98, "name": "铜链枷", "note": "", "params": [0, 0, 12, 0, 0, 0, 0, 0], "price": 680, "wtypeId": 3}, {"id": 15, "animationId": 1, "description": "【链枷】用钢铁进行强化的链枷。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 98, "name": "晨星", "note": "", "params": [0, 0, 18, 0, 0, 0, 0, 0], "price": 1460, "wtypeId": 3}, {"id": 16, "animationId": 1, "description": "【链枷】用秘银进行强化的链枷。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 98, "name": "秘银链枷", "note": "", "params": [0, 0, 23, 0, 0, 0, 0, 0], "price": 2980, "wtypeId": 3}, {"id": 17, "animationId": 1, "description": "【链枷】用龙之齿进行强化的链枷。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 98, "name": "龙链枷", "note": "", "params": [0, 0, 29, 0, 0, 0, 0, 0], "price": 6180, "wtypeId": 3}, {"id": 18, "animationId": 0, "description": "", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0, "wtypeId": 0}, {"id": 19, "animationId": 6, "description": "【斧】用于砍伐的小型斧头。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 99, "name": "手斧", "note": "", "params": [0, 0, 10, 0, 0, 0, 0, 0], "price": 480, "wtypeId": 4}, {"id": 20, "animationId": 6, "description": "【斧】用于战斗的双刃斧。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 99, "name": "战斧", "note": "", "params": [0, 0, 18, 0, 0, 0, 0, 0], "price": 980, "wtypeId": 4}, {"id": 21, "animationId": 6, "description": "【斧】新月形刀身的战斧。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 99, "name": "弯月斧", "note": "", "params": [0, 0, 25, 0, 0, 0, 0, 0], "price": 2180, "wtypeId": 4}, {"id": 22, "animationId": 6, "description": "【斧】用秘银锻造而成的战斧。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 99, "name": "秘银斧", "note": "", "params": [0, 0, 32, 0, 0, 0, 0, 0], "price": 4500, "wtypeId": 4}, {"id": 23, "animationId": 6, "description": "【斧】用龙之齿锻造而成的战斧。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 99, "name": "龙斧", "note": "", "params": [0, 0, 45, 0, 0, 0, 0, 0], "price": 9600, "wtypeId": 4}, {"id": 24, "animationId": 0, "description": "", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0, "wtypeId": 0}, {"id": 25, "animationId": 11, "description": "【矛】用于突刺的短矛。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 107, "name": "短矛", "note": "", "params": [0, 0, 6, 0, 0, 0, 0, 0], "price": 320, "wtypeId": 12}, {"id": 26, "animationId": 11, "description": "【矛】将矛身加长到极限的枪矛。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 107, "name": "长矛", "note": "", "params": [0, 0, 12, 0, 0, 0, 0, 0], "price": 890, "wtypeId": 12}, {"id": 27, "animationId": 11, "description": "【矛】枪尖附有斧刃的长矛。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 107, "name": "戟", "note": "", "params": [0, 0, 18, 0, 0, 0, 0, 0], "price": 1760, "wtypeId": 12}, {"id": 28, "animationId": 11, "description": "【矛】用秘银锻造而成的矛。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 107, "name": "秘银矛", "note": "", "params": [0, 0, 24, 0, 0, 0, 0, 0], "price": 3420, "wtypeId": 12}, {"id": 29, "animationId": 11, "description": "【矛】用龙之齿锻造而成的矛。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 107, "name": "龙矛", "note": "", "params": [0, 0, 32, 0, 0, 0, 0, 0], "price": 5980, "wtypeId": 12}, {"id": 30, "animationId": 0, "description": "", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0, "wtypeId": 0}, {"id": 31, "animationId": 1, "description": "【手套】一种战斗手套，其指节上\n带有长钉。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}, {"code": 54, "dataId": 2, "value": 1}, {"code": 34, "dataId": 0, "value": 1}], "iconIndex": 106, "name": "手套", "note": "", "params": [0, 0, 5, 5, 0, 0, 0, 0], "price": 360, "wtypeId": 11}, {"id": 32, "animationId": 16, "description": "【爪】附有4个短爪的战斗武器。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}, {"code": 54, "dataId": 2, "value": 1}, {"code": 34, "dataId": 0, "value": 1}], "iconIndex": 105, "name": "虎爪", "note": "", "params": [0, 0, 12, 8, 0, 0, 0, 0], "price": 980, "wtypeId": 10}, {"id": 33, "animationId": 16, "description": "【爪】附有复数铁爪的战斗武器。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}, {"code": 54, "dataId": 2, "value": 1}, {"code": 34, "dataId": 0, "value": 1}], "iconIndex": 105, "name": "铁爪", "note": "", "params": [0, 0, 20, 15, 0, 0, 0, 0], "price": 1560, "wtypeId": 10}, {"id": 34, "animationId": 16, "description": "【爪】附有复数秘银爪的战斗武器。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}, {"code": 54, "dataId": 2, "value": 1}, {"code": 34, "dataId": 0, "value": 1}], "iconIndex": 105, "name": "秘银爪", "note": "", "params": [0, 0, 25, 18, 0, 0, 0, 0], "price": 3480, "wtypeId": 10}, {"id": 35, "animationId": 16, "description": "【爪】附有复数龙爪的战斗武器。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}, {"code": 54, "dataId": 2, "value": 1}, {"code": 34, "dataId": 0, "value": 1}], "iconIndex": 105, "name": "龙爪", "note": "", "params": [0, 0, 32, 21, 0, 0, 0, 0], "price": 6890, "wtypeId": 10}, {"id": 36, "animationId": 0, "description": "", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0, "wtypeId": 0}, {"id": 37, "animationId": 11, "description": "【弓】用于狩猎的短弓。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 102, "name": "短弓", "note": "", "params": [0, 0, 7, 0, 0, 0, 0, 0], "price": 310, "wtypeId": 7}, {"id": 38, "animationId": 11, "description": "【弓】威力提高的战斗用长弓。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 102, "name": "长弓", "note": "", "params": [0, 0, 12, 0, 0, 0, 0, 0], "price": 780, "wtypeId": 7}, {"id": 39, "animationId": 11, "description": "【弓】能够射出强力箭矢的\n石弓。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 103, "name": "弩", "note": "", "params": [0, 0, 18, 0, 0, 0, 0, 0], "price": 1860, "wtypeId": 8}, {"id": 40, "animationId": 11, "description": "【弓】能够射出强力秘银箭矢的\n石弓。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 103, "name": "秘银弓", "note": "", "params": [0, 0, 23, 0, 0, 0, 0, 0], "price": 3260, "wtypeId": 8}, {"id": 41, "animationId": 11, "description": "【弓】使用龙须制成用于战斗\n的长弓。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 102, "name": "龙弓", "note": "", "params": [0, 0, 28, 0, 0, 0, 0, 0], "price": 5980, "wtypeId": 7}, {"id": 42, "animationId": 0, "description": "", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0, "wtypeId": 0}, {"id": 43, "animationId": 6, "description": "【匕首】日常生活中使用的刀具。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 96, "name": "刀", "note": "", "params": [0, 0, 5, 0, 0, 0, 0, 0], "price": 280, "wtypeId": 1}, {"id": 44, "animationId": 6, "description": "【匕首】用于战斗的短匕首。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 96, "name": "匕首", "note": "", "params": [0, 0, 9, 0, 0, 0, 0, 0], "price": 690, "wtypeId": 1}, {"id": 45, "animationId": 6, "description": "【匕首】刀身厚实且坚固的短刀。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 96, "name": "弯刀", "note": "", "params": [0, 0, 12, 0, 0, 0, 0, 0], "price": 1280, "wtypeId": 1}, {"id": 46, "animationId": 6, "description": "【匕首】用秘银锻造而成的短刀。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 96, "name": "秘银匕首", "note": "", "params": [0, 0, 18, 0, 0, 0, 0, 0], "price": 2930, "wtypeId": 1}, {"id": 47, "animationId": 6, "description": "【匕首】用龙之齿锻造而成的短刀。", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 96, "name": "龙匕首", "note": "", "params": [0, 0, 23, 0, 0, 0, 0, 0], "price": 5820, "wtypeId": 1}, {"id": 48, "animationId": 0, "description": "", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0, "wtypeId": 0}, {"id": 49, "animationId": 0, "description": "", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0, "wtypeId": 0}, {"id": 50, "animationId": 0, "description": "", "etypeId": 1, "traits": [{"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 0, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0, "wtypeId": 0}]