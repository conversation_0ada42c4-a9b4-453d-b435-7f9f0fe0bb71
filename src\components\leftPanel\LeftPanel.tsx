import React, { useState } from 'react';
import { Box, Tabs, Tab, IconButton, Tooltip } from '@mui/material';
import { AccountTree as TreeIcon } from '@mui/icons-material';
import PixiObjectTree from './object/PixiObjectTree';
import TypeTreeWindow from './class/TypeTreeWindow';

interface LeftPanelProps {
  // 可以添加需要的属性
}

const LeftPanel: React.FC<LeftPanelProps> = () => {
  const [tabIndex, setTabIndex] = useState(0);
  const [typeTreeWindowOpen, setTypeTreeWindowOpen] = useState(false);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabIndex(newValue);
  };

  // 处理打开类型树窗口
  const handleOpenTypeTree = () => {
    setTypeTreeWindowOpen(true);
  };

  // 处理关闭类型树窗口
  const handleCloseTypeTree = () => {
    setTypeTreeWindowOpen(false);
  };

  // 正常模式显示对象树和类型选项卡
  return (
    <>
      <Box sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* 头部：选项卡和类型树按钮 */}
        <Box sx={{
          borderBottom: 1,
          borderColor: 'divider',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <Tabs value={tabIndex} onChange={handleTabChange}>
            <Tab label="对象树" />
            <Tab label="类型" />
          </Tabs>

          {/* 类型树按钮 */}
          <Tooltip title="显示类型树">
            <IconButton
              size="small"
              onClick={handleOpenTypeTree}
              sx={{ mr: 1 }}
            >
              <TreeIcon />
            </IconButton>
          </Tooltip>
        </Box>

        {/* 内容区域 */}
        <Box sx={{ flex: 1, overflow: 'auto', display: tabIndex === 0 ? 'block' : 'none' }}>
          <PixiObjectTree />
        </Box>

        {/* <Box sx={{ flex: 1, overflow: 'auto', display: tabIndex === 1 ? 'block' : 'none' }}>
          <RpgClassPanel />
        </Box> */}
      </Box>

      {/* 类型树窗口 */}
      <TypeTreeWindow
        open={typeTreeWindowOpen}
        onClose={handleCloseTypeTree}
      />
    </>
  );
};

export default LeftPanel;
